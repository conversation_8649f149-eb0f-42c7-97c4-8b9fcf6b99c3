<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MobSkillTemplate;
use App\Models\MobSkill;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Services\Mine\MobSkillIntegrationService;
use App\Services\LogFormattingService;
use App\Services\BattleLogService;
use App\Services\PlayerHealthService;
use App\Services\CombatFormulaService;
use App\Jobs\MineAutoAttackJob;

class TestRealMineStunAttack extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:real-mine-stun {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Тестирует реальную атаку моба со скиллом стана в руднике';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        
        // Получаем пользователя
        $user = $userId ? User::find($userId) : User::first();
        if (!$user) {
            $this->error("Пользователь не найден!");
            return 1;
        }

        $this->info("🧪 Тестируем реальную атаку моба со скиллом стана для: {$user->name}");

        // Находим моба с навыком стана
        $mob = Mob::whereHas('skills.skillTemplate', function($query) {
            $query->where('effect_type', 'stun');
        })->first();

        if (!$mob) {
            $this->error("Моб со скиллом стана не найден!");
            return 1;
        }

        $this->info("✅ Найден моб со скиллом стана: {$mob->name}");

        // Находим локацию рудника
        $mineLocation = MineLocation::first();
        if (!$mineLocation) {
            $this->error("Локация рудника не найдена!");
            return 1;
        }

        $this->info("✅ Найдена локация рудника: {$mineLocation->name}");

        // Создаем сервисы
        $mobSkillService = app(MobSkillIntegrationService::class);
        $logFormatter = app(LogFormattingService::class);
        $battleLogService = app(BattleLogService::class);
        $playerHealthService = app(PlayerHealthService::class);
        $combatFormulaService = app(CombatFormulaService::class);

        // Симулируем атаку моба
        $this->info("\n⚔️ Симулируем атаку моба...");

        // Рассчитываем урон
        $mobStrength = $mob->strength ?? 1;
        $playerArmor = $user->profile->getEffectiveStats()['armor'] ?? 0;
        $damage = $combatFormulaService->calculateDamage($mobStrength, $playerArmor);

        $this->info("💥 Рассчитанный урон: {$damage}");

        // Обрабатываем скиллы моба
        $activatedSkills = $mobSkillService->processMobAttackSkills($mob, $user, [
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'damage_dealt' => $damage,
            'attack_type' => 'test_mine_attack'
        ]);

        $this->info("🎯 Активированные скиллы: " . count($activatedSkills));
        foreach ($activatedSkills as $skill) {
            $this->info("   - Тип: " . ($skill['skill_type'] ?? 'unknown'));
            $this->info("   - Сообщение: " . ($skill['message'] ?? 'нет сообщения'));
            $this->info("   - Успех: " . ($skill['success'] ? 'да' : 'нет'));
        }

        // Тестируем formatMineDetectionAttack
        $this->info("\n📝 Тестируем formatMineDetectionAttack:");
        $mineAttackMessage = $logFormatter->formatMineDetectionAttack(
            $mob->name,
            $user->name,
            $damage,
            $mineLocation->name,
            $activatedSkills
        );

        $this->line("Результат: " . strip_tags($mineAttackMessage));
        $this->line("HTML: " . $mineAttackMessage);

        // Тестируем formatMobAttack
        $this->info("\n🔄 Тестируем formatMobAttack:");
        $mobAttackMessage = $logFormatter->formatMobAttack(
            $mob,
            $user,
            $damage,
            false,
            $activatedSkills
        );

        $this->line("Результат: " . strip_tags($mobAttackMessage));
        $this->line("HTML: " . $mobAttackMessage);

        // Добавляем логи в журнал боя
        $battleLogKey = "battle_logs:mines:{$user->id}";
        $this->info("\n📋 Добавляем логи в журнал боя...");
        
        $battleLogService->addLog($battleLogKey, $mineAttackMessage, 'danger');
        $battleLogService->addLog($battleLogKey, $mobAttackMessage, 'warning');

        $this->info("✅ Логи добавлены в журнал боя");
        $this->info("💡 Ключ журнала: {$battleLogKey}");

        // Проверяем, есть ли скилл стана среди активированных
        $hasStunSkill = false;
        foreach ($activatedSkills as $skill) {
            if (isset($skill['skill_type']) && $skill['skill_type'] === 'stun') {
                $hasStunSkill = true;
                break;
            }
        }

        if ($hasStunSkill) {
            $this->info("\n🎉 УСПЕХ: Скилл стана активировался!");
            $this->info("📌 В журнале боя должно отображаться сообщение об оглушении БЕЗ урона");
        } else {
            $this->warn("\n⚠️ Скилл стана НЕ активировался в этот раз");
            $this->info("💡 Попробуйте запустить команду еще раз - скиллы активируются по шансу");
        }

        return 0;
    }
}
