<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage; // Добавлено для работы с файлами и путями // Added for working with files and paths
use Illuminate\Support\Facades\Log; // Импортируем логгер // Import logger

/**
 * Модель для рецептов зелий в алхимической системе
 * Represents a potion recipe in the alchemy system
 */
class PotionRecipe extends Model
{
    use HasFactory;

    /**
     * Константы для качества зелий/рецептов.
     * Constants for potion/recipe quality.
     */
    public const QUALITY_COMMON = 'Обычное'; // Обычное // Common
    public const QUALITY_UNCOMMON = 'Необычное'; // Необычное // Uncommon
    public const QUALITY_RARE = 'Редкое'; // Редкое // Rare
    public const QUALITY_EPIC = 'Эпическое'; // Эпическое // Epic
    public const QUALITY_LEGENDARY = 'Легендарное'; // Легендарное // Legendary

    /**
     * Порядок сортировки качеств. Используется в контроллерах.
     * Sort order for qualities. Used in controllers.
     * @var array
     */
    public const QUALITY_ORDER = [
        self::QUALITY_COMMON,
        self::QUALITY_UNCOMMON,
        self::QUALITY_RARE,
        self::QUALITY_EPIC,
        self::QUALITY_LEGENDARY,
    ];

    /**
     * Атрибуты, которые можно массово назначать
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'recipe_id',
        'name',
        'description',
        // Добавляем новые поля // Add new fields
        'quality',                 // Качество рецепта // Recipe quality
        'brewing_time_modifier', // Модификатор времени варки // Brewing time modifier
        'icon',                    // Иконка рецепта // Recipe icon
        'effect',
        'effect_value',
        'effect_duration',
        'level',
        'color',
        'brewing_time',
        'min_alchemy_level',
        'is_active',
        'price_gold',       // Цена в золоте (для покупки в магазине) // Price in gold (for shop purchase)
        'price_silver',     // Цена в серебре // Price in silver
        'price_bronze',     // Цена в бронзе // Price in bronze
    ];

    /**
     * Преобразование атрибутов
     * The attributes that should be cast.
     */
    protected $casts = [
        'effect_value' => 'integer',
        'effect_duration' => 'integer',
        'brewing_time' => 'integer',
        'min_alchemy_level' => 'integer',
        'is_active' => 'boolean',
        // Добавляем преобразование для нового поля // Add cast for the new field
        'brewing_time_modifier' => 'decimal:2', // Модификатор времени варки // Brewing time modifier
        'level' => 'integer',               // Уровень теперь целое число // Level is now integer
        'price_gold' => 'integer',          // Целое число // Integer
        'price_silver' => 'integer',        // Целое число // Integer
        'price_bronze' => 'integer',        // Целое число // Integer
    ];

    /**
     * Значения по умолчанию для атрибутов модели.
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'quality' => self::QUALITY_COMMON, // По умолчанию качество 'Обычное' // Default quality is 'Common'
        'brewing_time_modifier' => 1.00,  // По умолчанию модификатор 1.00 // Default modifier is 1.00
    ];

    /**
     * Получить ингредиенты для рецепта
     * Get the ingredients for the recipe.
     */
    public function ingredients()
    {
        // Определяем связь многие-ко-многим с моделью AlchemyIngredient через таблицу recipe_ingredients
        // Defines the many-to-many relationship with the AlchemyIngredient model via the recipe_ingredients table
        return $this->belongsToMany(AlchemyIngredient::class, 'recipe_ingredients', 'potion_recipe_id', 'alchemy_ingredient_id')
            ->withPivot('quantity') // Также загружаем количество из сводной таблицы // Also load the quantity from the pivot table
            ->withTimestamps(); // Поддерживаем временные метки // Maintain timestamps
    }

    /**
     * Связь с записью в магазине
     * Relationship with shop record
     */
    public function shopRecipe()
    {
        return $this->hasOne(ShopRecipe::class, 'potion_recipe_id');
    }

    /**
     * Проверить, может ли пользователь создать зелье по этому рецепту
     * Check if the user can brew a potion using this recipe.
     *
     * @param User $user Пользователь // The user
     * @return array Результат проверки ['can_brew' => bool, 'message' => string, 'missing' => array] // Check result
     */
    public function canBrewByUser(User $user)
    {
        // Проверка уровня алхимии пользователя // Check user's alchemy level
        $alchemyLevel = $user->profile->alchemy_level ?? 1; // Получаем уровень алхимии или 1, если не задан // Get alchemy level or 1 if not set
        if ($alchemyLevel < $this->min_alchemy_level) { // Если уровень ниже требуемого // If level is lower than required
            return [
                'can_brew' => false, // Нельзя сварить // Cannot brew
                'message' => "Требуется уровень алхимии {$this->min_alchemy_level}, у вас {$alchemyLevel}", // Сообщение об ошибке // Error message
                'missing' => ['alchemy_level' => $this->min_alchemy_level - $alchemyLevel] // Сколько не хватает уровня // How much level is missing
            ];
        }

        // Проверка наличия всех необходимых ингредиентов // Check for all necessary ingredients
        $ingredients = $this->ingredients()->get(); // Получаем список ингредиентов рецепта // Get the list of recipe ingredients
        $missingIngredients = []; // Массив для недостающих ингредиентов // Array for missing ingredients

        foreach ($ingredients as $ingredient) { // Перебираем ингредиенты // Iterate over ingredients
            // Ищем ресурс в инвентаре пользователя (раньше искал AlchemyIngredient, теперь ищем по resource_id)
            // Search for the resource in the user's inventory (previously searched for AlchemyIngredient, now searching by resource_id)
            $userHas = UserResource::where('user_id', $user->id) // Ищем у пользователя // Search for the user
                ->where('resource_id', $ingredient->resource_id) // Ищем по ID ресурса из AlchemyIngredient // Search by resource ID from AlchemyIngredient
                ->where('location', 'inventory') // Только в инвентаре // Only in inventory
                ->sum('quantity'); // Суммируем количество // Sum the quantity

            $needed = $ingredient->pivot->quantity; // Требуемое количество // Required quantity

            if ($userHas < $needed) { // Если у пользователя меньше, чем нужно // If the user has less than needed
                $missingIngredients[] = [ // Добавляем в список недостающих // Add to the list of missing
                    'id' => $ingredient->id, // ID ингредиента (модели AlchemyIngredient) // Ingredient ID (AlchemyIngredient model)
                    'resource_id' => $ingredient->resource_id, // ID ресурса // Resource ID
                    'name' => $ingredient->name, // Название ингредиента // Ingredient name
                    'has' => $userHas, // Сколько есть у пользователя // How much the user has
                    'needed' => $needed, // Сколько нужно // How much is needed
                    'missing' => $needed - $userHas // Сколько не хватает // How much is missing
                ];
            }
        }

        if (count($missingIngredients) > 0) { // Если есть недостающие ингредиенты // If there are missing ingredients
            return [
                'can_brew' => false, // Нельзя сварить // Cannot brew
                'message' => 'Недостаточно ингредиентов', // Сообщение // Message
                'missing' => $missingIngredients // Список недостающих // List of missing
            ];
        }

        // Все проверки пройдены // All checks passed
        return [
            'can_brew' => true, // Можно сварить // Can brew
            'message' => 'Можно создать зелье', // Сообщение // Message
            'missing' => [] // Нет недостающих // No missing items
        ];
    }

    /**
     * Создать зелье для пользователя на основе этого рецепта
     * Create a potion for the user based on this recipe.
     *
     * @param User $user Пользователь, для которого создается зелье // The user for whom the potion is created
     * @return array Результат создания ['success' => bool, 'message' => string, 'potion' => Potion|null] // Creation result
     */
    public function brewForUser(User $user)
    {
        // Проверяем возможность создания // Check if creation is possible
        $canBrew = $this->canBrewByUser($user);
        if (!$canBrew['can_brew']) { // Если нельзя сварить // If cannot brew
            return [
                'success' => false, // Неудача // Failure
                'message' => $canBrew['message'], // Сообщение об ошибке // Error message
                'potion' => null // Зелья нет // No potion
            ];
        }

        // Списываем ингредиенты // Deduct ingredients
        \DB::beginTransaction(); // Начинаем транзакцию // Start transaction
        try {
            foreach ($this->ingredients as $ingredient) { // Перебираем ингредиенты // Iterate over ingredients
                $needed = $ingredient->pivot->quantity; // Необходимое количество // Required quantity

                // Ищем ресурсы пользователя и списываем необходимое количество (по resource_id)
                // Find user resources and deduct the required amount (by resource_id)
                $userResources = UserResource::where('user_id', $user->id) // У пользователя // For the user
                    ->where('resource_id', $ingredient->resource_id) // По ID ресурса // By resource ID
                    ->where('location', 'inventory') // В инвентаре // In inventory
                    ->where('quantity', '>', 0) // Где количество больше нуля // Where quantity is greater than zero
                    ->orderBy('created_at') // Сначала старые // Oldest first
                    ->get(); // Получаем коллекцию // Get the collection

                $remaining = $needed; // Сколько еще нужно списать // How much more to deduct
                foreach ($userResources as $resource) { // Перебираем найденные стаки ресурса // Iterate over found resource stacks
                    if ($remaining <= 0) // Если уже списали достаточно // If enough has been deducted
                        break; // Выходим из цикла // Exit loop

                    if ($resource->quantity <= $remaining) { // Если стак меньше или равен остатку // If stack is less than or equal to remaining
                        $remaining -= $resource->quantity; // Уменьшаем остаток // Decrease remaining
                        $resource->quantity = 0; // Обнуляем стак // Zero out the stack
                    } else { // Если стак больше остатка // If stack is greater than remaining
                        $resource->quantity -= $remaining; // Уменьшаем стак на остаток // Decrease stack by remaining
                        $remaining = 0; // Остаток обнуляется // Remaining becomes zero
                    }

                    $resource->save(); // Сохраняем изменения стака // Save stack changes
                }

                if ($remaining > 0) { // Если после перебора стаков все еще не хватило // If still not enough after iterating through stacks
                    throw new \Exception("Недостаточно ресурса: {$ingredient->name}"); // Выбрасываем исключение // Throw exception
                }
            }

            // Создаем зелье // Create the potion
            $potion = Potion::create([
                'user_id' => $user->id, // ID пользователя // User ID
                'name' => $this->name, // Название из рецепта // Name from recipe
                'description' => $this->description, // Описание из рецепта // Description from recipe
                'effect' => $this->effect, // Эффект из рецепта // Effect from recipe
                'effect_value' => $this->effect_value, // Значение эффекта из рецепта // Effect value from recipe
                'effect_duration' => $this->effect_duration, // Длительность эффекта из рецепта // Effect duration from recipe
                'level' => $this->level, // Уровень из рецепта // Level from recipe
                // 'quality' => 'Обычное', // УДАЛЕНО: Качество будет зависеть от рецепта и возможно катализаторов // REMOVED: Quality will depend on recipe and possibly catalysts
                'quality' => $this->determinePotionQuality(), // Определяем качество зелья // Determine potion quality
                'icon' => $this->icon, // Иконка из рецепта // Icon from recipe
                'color' => $this->color, // Цвет из рецепта // Color from recipe
                'recipe_id' => $this->id, // ID рецепта, по которому сварено // ID of the recipe used
                'uses_left' => 1, // По умолчанию одно использование // Default one use
                'price_gold' => $this->price_gold, // Цена в золоте // Gold price
                'price_silver' => $this->price_silver, // Цена в серебре // Silver price
                'price_bronze' => $this->price_bronze, // Цена в бронзе // Bronze price
            ]);

            \DB::commit(); // Подтверждаем транзакцию // Commit transaction

            // Даем пользователю опыт алхимии // Grant alchemy experience to the user
            // Опыт зависит от уровня рецепта И его качества // Experience depends on recipe level AND its quality
            $baseExperience = match ($this->level) {
                'basic' => 5,
                'advanced' => 10,
                'expert' => 15,
                'master' => 20,
                default => 5
            };
            $qualityMultiplier = match ($this->quality) {
                self::QUALITY_UNCOMMON => 1.2,
                self::QUALITY_RARE => 1.5,
                self::QUALITY_EPIC => 2.0,
                self::QUALITY_LEGENDARY => 2.5,
                default => 1.0,
            };
            $experienceGained = round($baseExperience * $qualityMultiplier); // Расчитываем опыт // Calculate experience


            // Обновляем опыт пользователя (предполагается, что метод существует в UserProfile)
            // Update user experience (assuming the method exists in UserProfile)
            if ($user->profile) {
                // $user->profile->addExperience('alchemy', $experienceGained); // Метод для добавления опыта
            }


            return [
                'success' => true, // Успех // Success
                'message' => "Создано зелье: {$potion->name} ({$potion->quality})", // Сообщение об успехе // Success message
                'potion' => $potion, // Созданное зелье // Created potion
                'experience_gained' => $experienceGained, // Полученный опыт // Experience gained
                'price_gold' => $this->price_gold, // Цена в золоте // Gold price
                'price_silver' => $this->price_silver, // Цена в серебре // Silver price
                'price_bronze' => $this->price_bronze, // Цена в бронзе // Bronze price
            ];

        } catch (\Exception $e) { // Если произошла ошибка // If an error occurred
            \DB::rollBack(); // Откатываем транзакцию // Rollback transaction
            return [
                'success' => false, // Неудача // Failure
                'message' => "Ошибка создания зелья: {$e->getMessage()}", // Сообщение об ошибке // Error message
                'potion' => null, // Нет зелья // No potion
            ];
        }
    }

    /**
     * Получить отформатированное время варки (с учетом модификатора качества)
     * Get the formatted brewing time (considering quality modifier).
     */
    public function getBrewingTimeFormattedAttribute()
    {
        // Рассчитываем реальное время варки с учетом модификатора рецепта
        // Calculate the actual brewing time considering the recipe modifier
        $actualBrewingTime = ceil($this->brewing_time * $this->brewing_time_modifier); // Округляем вверх // Round up

        $minutes = floor($actualBrewingTime / 60); // Получаем минуты // Get minutes
        $seconds = $actualBrewingTime % 60; // Получаем секунды // Get seconds

        if ($minutes > 0) { // Если есть минуты // If there are minutes
            return $minutes . ' мин ' . ($seconds > 0 ? $seconds . ' сек' : ''); // Возвращаем минуты и секунды (если есть) // Return minutes and seconds (if any)
        }

        return $seconds . ' сек'; // Возвращаем только секунды // Return only seconds
    }

    /**
     * Получить форматированное описание эффекта
     * Get the formatted effect description.
     */
    public function getEffectDescriptionAttribute()
    {
        if (empty($this->effect)) { // Если эффект не задан // If effect is not set
            return 'Нет эффекта'; // Возвращаем "Нет эффекта" // Return "No effect"
        }

        // Формируем базовое описание // Form the base description
        $description = $this->effect . ' +' . $this->effect_value;

        if ($this->effect_duration > 0) { // Если есть длительность // If there is a duration
            $minutes = floor($this->effect_duration / 60); // Рассчитываем минуты // Calculate minutes
            $description .= ' (' . $minutes . ' мин)'; // Добавляем длительность в минутах // Add duration in minutes
        }

        return $description; // Возвращаем полное описание // Return the full description
    }

    /**
     * Получить полный путь к иконке рецепта
     * Get the full path to the recipe icon.
     *
     * @return string|null Полный URL иконки или null, если иконка не задана. // Full icon URL or null if icon is not set.
     */
    public function getIconPathAttribute(): ?string
    {
        // Проверяем, задана ли иконка и не является ли она пустой строкой
        // Check if the icon is set and is not an empty string
        if ($this->icon && trim($this->icon) !== '') {
            // Если путь начинается с 'http://' или 'https://', возвращаем как есть
            // If the path starts with 'http://' or 'https://', return as is
            if (str_starts_with($this->icon, 'http://') || str_starts_with($this->icon, 'https://')) {
                return $this->icon;
            }

            // Если путь начинается с 'assets/', просто оборачиваем asset()
            // If the path starts with 'assets/', just wrap with asset()
            if (str_starts_with($this->icon, 'assets/')) {
                return asset($this->icon);
            }

            // В остальных случаях предполагаем, что это относительный путь от assets/
            // In other cases, assume it's a relative path from assets/
            return asset('assets/' . $this->icon);
        }

        // Если иконка не задана, возвращаем путь к плейсхолдеру
        // If the icon is not set, return the path to the placeholder
        return asset('assets/placeholder.png');
    }

    /**
     * Получить CSS класс в зависимости от качества рецепта
     * Get the CSS class based on the recipe quality.
     *
     * @return string CSS класс // CSS class
     */
    public function getQualityClassAttribute(): string
    {
        // Возвращаем CSS класс на основе значения поля 'quality'
        // Return a CSS class based on the value of the 'quality' field
        return match ($this->quality) {
            self::QUALITY_UNCOMMON => 'quality-uncommon', // Необычный // Uncommon
            self::QUALITY_RARE => 'quality-rare',       // Редкий // Rare
            self::QUALITY_EPIC => 'quality-epic',       // Эпический // Epic
            self::QUALITY_LEGENDARY => 'quality-legendary', // Легендарное // Legendary
            default => 'quality-common',              // Обычный (по умолчанию) // Common (default)
        };
    }

    /**
     * Определяет качество создаваемого зелья.
     * Может быть переопределено для более сложной логики (например, учет катализаторов).
     * Determines the quality of the potion being created.
     * Can be overridden for more complex logic (e.g., considering catalysts).
     *
     * @return string Качество зелья // Potion quality
     */
    protected function determinePotionQuality(): string
    {
        // Пока просто возвращаем качество рецепта.
        // For now, just return the recipe quality.
        // В будущем здесь можно добавить логику, зависящую от успеха крафта, катализаторов и т.д.
        // In the future, logic depending on crafting success, catalysts, etc., can be added here.
        return $this->quality;
    }
}