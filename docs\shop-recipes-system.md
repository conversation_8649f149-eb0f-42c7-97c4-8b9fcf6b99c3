# Система рецептов в магазине

## Обзор

Система рецептов в магазине позволяет администраторам добавлять рецепты зелий в магазин для покупки игроками. Система интегрирована с существующей системой алхимии и предоставляет полный контроль над ценообразованием и доступностью рецептов.

## Основные компоненты

### 1. Модели
- **ShopRecipe** - модель для рецептов в магазине
- **PotionRecipe** - существующая модель рецептов зелий
- Связь: один рецепт может быть только один раз в магазине

### 2. Контроллер
- **ShopAdminController** - обновлен для поддержки рецептов
- Новые методы: `storeRecipe()`, `destroyRecipe()`

### 3. Представления
- **admin/shop/index.blade.php** - обновлен с вкладками для предметов и рецептов
- **admin/shop/create-recipe.blade.php** - новая форма для добавления рецептов

### 4. База данных
- **shop_recipes** - новая таблица для рецептов в магазине

## Использование

### Просмотр рецептов в магазине
1. Перейдите в админку: `/admin/shop`
2. Выберите вкладку "Рецепты"
3. Просмотрите список добавленных рецептов

### Добавление рецепта в магазин
1. На странице `/admin/shop` нажмите кнопку с иконкой зелья (справа от кнопки добавления предмета)
2. Или перейдите напрямую: `/admin/shop/create?type=recipe`
3. Выберите рецепт из выпадающего списка
4. Установите цену (золото, серебро, бронза)
5. Укажите категорию (опционально)
6. Установите доступность
7. Нажмите "Добавить рецепт"

### Удаление рецепта из магазина
1. На вкладке "Рецепты" найдите нужный рецепт
2. Нажмите кнопку удаления (🗑️)
3. Подтвердите действие

## Особенности

### Валидация
- Рецепт может быть добавлен в магазин только один раз
- Только активные рецепты доступны для добавления
- Цена в серебре и бронзе ограничена до 99

### Предварительный просмотр
- При выборе рецепта отображается информация о качестве, уровне и иконке
- JavaScript валидация формы

### Безопасность
- CSRF защита для всех форм
- Валидация на уровне сервера
- Route Model Binding для безопасного доступа к записям

## Команды для тестирования

### Создание тестовых рецептов
```bash
php artisan db:seed --class=TestPotionRecipeSeeder
```

### Тестирование системы
```bash
php artisan test:shop-recipes
```

### Проверка структуры таблицы
```bash
php artisan tinker
Schema::getColumnListing('shop_recipes')
```

## Структура таблицы shop_recipes

| Поле | Тип | Описание |
|------|-----|----------|
| id | bigint | Первичный ключ |
| potion_recipe_id | bigint | ID рецепта зелья (FK) |
| price_gold | integer | Цена в золоте |
| price_silver | integer | Цена в серебре (0-99) |
| price_bronze | integer | Цена в бронзе (0-99) |
| is_available | boolean | Доступен ли для покупки |
| shop_category | string | Категория в магазине |
| created_at | timestamp | Дата создания |
| updated_at | timestamp | Дата обновления |

## Маршруты

| Метод | URL | Действие |
|-------|-----|----------|
| GET | `/admin/shop?tab=recipes` | Список рецептов |
| GET | `/admin/shop/create?type=recipe` | Форма добавления |
| POST | `/admin/shop/store-recipe` | Сохранение рецепта |
| DELETE | `/admin/shop/recipe/{shopRecipe}` | Удаление рецепта |

## Интеграция с игровой логикой

Система готова к интеграции с существующей логикой покупки рецептов в ShopController. Рецепты из shop_recipes могут быть отображены в игровом магазине и приобретены игроками.

## Расширение функциональности

Система спроектирована для легкого расширения:
- Добавление скидок и акций
- Система уровней доступа к рецептам
- Ограничения по количеству покупок
- Временные рецепты (ограниченное время продажи)
