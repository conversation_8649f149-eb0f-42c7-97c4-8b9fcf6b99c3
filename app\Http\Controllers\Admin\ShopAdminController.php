<?php

// Указываем пространство имен для контроллера
namespace App\Http\Controllers\Admin;

// Подключаем базовый контроллер Laravel
use App\Http\Controllers\Controller;
// Подключаем модель Item (глобальные предметы)
use App\Models\Item;
// Подключаем модель ShopItem (предметы в магазине)
use App\Models\ShopItem;
// Подключаем модель ShopRecipe (рецепты в магазине)
use App\Models\ShopRecipe;
// Подключаем модель PotionRecipe (рецепты зелий)
use App\Models\PotionRecipe;
// Подключаем фасад для работы с HTTP-запросами
use Illuminate\Http\Request;
// Подключаем фасад для работы с базой данных
use Illuminate\Support\Facades\DB;
// Подключаем модель UserProfile для получения данных о валюте (если потребуется)
use App\Models\UserProfile;
// Подключаем сервис для работы с валютой
use App\Services\CurrencyService;

/**
 * Контроллер для управления предметами в магазине через административную панель.
 * Handles managing shop items via the admin panel.
 */
class ShopAdminController extends Controller
{
    // Экземпляр сервиса для работы с валютой
    protected $currencyService;

    /**
     * Конструктор контроллера.
     * Внедряем зависимость CurrencyService.
     * Constructor for the controller. Injects the CurrencyService dependency.
     *
     * @param CurrencyService $currencyService Сервис для работы с валютой
     */
    public function __construct(CurrencyService $currencyService)
    {
        // Сохраняем экземпляр сервиса валют
        $this->currencyService = $currencyService;
    }

    /**
     * Отображает список предметов и рецептов, доступных в магазине.
     * Displays a list of items and recipes available in the shop.
     *
     * @param Request $request Входящий HTTP-запрос
     * @return \Illuminate\Contracts\View\View Представление со списком предметов и рецептов магазина
     */
    public function index(Request $request)
    {
        // Определяем активную вкладку (по умолчанию - предметы)
        $activeTab = $request->get('tab', 'items');

        // Получаем предметы магазина с их связанными глобальными предметами (Item)
        // Используем пагинацию для отображения по 15 предметов на странице
        $shopItems = ShopItem::with('item') // Загружаем связанную модель Item
            ->orderBy('created_at', 'desc') // Сортируем по дате добавления (сначала новые)
            ->paginate(15, ['*'], 'items_page'); // Разбиваем на страницы по 15 элементов

        // Получаем рецепты магазина с их связанными рецептами зелий
        $shopRecipes = ShopRecipe::with('potionRecipe') // Загружаем связанную модель PotionRecipe
            ->orderBy('created_at', 'desc') // Сортируем по дате добавления (сначала новые)
            ->paginate(15, ['*'], 'recipes_page'); // Разбиваем на страницы по 15 элементов

        // Возвращаем представление 'admin.shop.index', передавая туда полученные предметы и рецепты магазина
        return view('admin.shop.index', compact('shopItems', 'shopRecipes', 'activeTab'));
    }

    /**
     * Показывает форму для добавления нового предмета или рецепта в магазин.
     * Shows the form for adding a new item or recipe to the shop.
     *
     * @param Request $request Входящий HTTP-запрос
     * @return \Illuminate\Contracts\View\View Представление с формой добавления
     */
    public function create(Request $request)
    {
        // Определяем тип добавляемого элемента (предмет или рецепт)
        $type = $request->get('type', 'item');

        if ($type === 'recipe') {
            // Получаем список всех рецептов зелий, которые еще не добавлены в магазин
            $recipes = PotionRecipe::whereDoesntHave('shopRecipe') // Выбираем только те рецепты, у которых нет связи с ShopRecipe
                ->where('is_active', true) // Только активные рецепты
                ->orderBy('name', 'asc') // Сортируем по названию для удобства выбора
                ->get(['id', 'name', 'quality', 'level', 'icon']); // Выбираем только необходимые поля

            // Возвращаем представление с формой добавления рецепта в магазин
            return view('admin.shop.create-recipe', compact('recipes'));
        } else {
            // Получаем список всех глобальных предметов (Item), которые еще не добавлены в магазин (ShopItem)
            // Это предотвращает дублирование предметов в магазине
            $items = Item::whereDoesntHave('shopItem') // Выбираем только те Item, у которых нет связи с ShopItem
                ->orderBy('name', 'asc') // Сортируем по названию для удобства выбора
                ->get(['id', 'name', 'type', 'quality']); // Выбираем только необходимые поля (id, name, type, quality)

            // Возвращаем представление с формой добавления предмета в магазин
            return view('admin.shop.create', compact('items'));
        }
    }

    /**
     * Сохраняет новый предмет магазина в базе данных.
     * Stores a newly created shop item in storage.
     *
     * @param Request $request Входящий HTTP-запрос с данными формы
     * @return \Illuminate\Http\RedirectResponse Перенаправление на список предметов магазина с сообщением
     */
    public function store(Request $request)
    {
        // Проводим валидацию входящих данных из формы
        $validatedData = $request->validate([
            // Поле 'item_id' обязательно и должно существовать в таблице 'items'
            'item_id' => 'required|exists:items,id|unique:shop_items,item_id', // Уникальность для shop_items
            // Поле 'price_gold' не обязательно, но должно быть целым числом не меньше 0
            'price_gold' => 'nullable|integer|min:0',
            // Поле 'price_silver' не обязательно, но должно быть целым числом не меньше 0 и не больше 99
            'price_silver' => 'nullable|integer|min:0|max:99',
            // Поле 'price_bronze' не обязательно, но должно быть целым числом не меньше 0 и не больше 99
            'price_bronze' => 'nullable|integer|min:0|max:99',
            // Поле 'is_available' для статуса доступности, по умолчанию true
            'is_available' => 'sometimes|boolean',
        ], [
            // Сообщения об ошибках валидации на русском языке
            'item_id.required' => 'Необходимо выбрать предмет.',
            'item_id.exists' => 'Выбранный предмет не существует.',
            'item_id.unique' => 'Этот предмет уже добавлен в магазин.',
            'price_gold.integer' => 'Цена (золото) должна быть целым числом.',
            'price_gold.min' => 'Цена (золото) не может быть отрицательной.',
            'price_silver.integer' => 'Цена (серебро) должна быть целым числом.',
            'price_silver.min' => 'Цена (серебро) не может быть отрицательной.',
            'price_silver.max' => 'Цена (серебро) не может быть больше 99.',
            'price_bronze.integer' => 'Цена (бронза) должна быть целым числом.',
            'price_bronze.min' => 'Цена (бронза) не может быть отрицательной.',
            'price_bronze.max' => 'Цена (бронза) не может быть больше 99.',
        ]);

        // Проверяем, указана ли хоть какая-то цена
        if (($validatedData['price_gold'] ?? 0) == 0 && ($validatedData['price_silver'] ?? 0) == 0 && ($validatedData['price_bronze'] ?? 0) == 0) {
            // Если цена не указана, возвращаем ошибку
            return redirect()->back()->withErrors(['price' => 'Необходимо указать цену предмета.'])->withInput();
        }

        // Создаем новый экземпляр ShopItem с валидированными данными
        ShopItem::create([
            'item_id' => $validatedData['item_id'],
            // Используем оператор null coalescing (??) для установки 0, если цена не указана
            'price_gold' => $validatedData['price_gold'] ?? 0,
            'price_silver' => $validatedData['price_silver'] ?? 0,
            'price_bronze' => $validatedData['price_bronze'] ?? 0,
            // Устанавливаем статус доступности (по умолчанию true, если не пришло значение false)
            'is_available' => $request->has('is_available') ? $request->boolean('is_available') : true,
            // Поле shop_category можно добавить позже, если потребуется
            // 'shop_category' => $validatedData['shop_category'] ?? null,
        ]);

        // Получаем имя добавленного предмета для сообщения
        $itemName = Item::find($validatedData['item_id'])->name ?? 'Предмет';

        // Перенаправляем администратора обратно на страницу списка предметов магазина
        // с сообщением об успешном добавлении
        return redirect()->route('admin.shop.index')
            ->with('success', "Предмет '{$itemName}' успешно добавлен в магазин.");
    }

    /**
     * Показывает форму для редактирования существующего предмета магазина.
     * Shows the form for editing the specified shop item.
     *
     * @param ShopItem $shopItem Экземпляр модели ShopItem (используется Route Model Binding)
     * @return \Illuminate\Contracts\View\View Представление с формой редактирования
     */
    public function edit(ShopItem $shopItem)
    {
        // Загружаем связанный глобальный предмет (Item), если он еще не загружен
        $shopItem->loadMissing('item');

        // Возвращаем представление 'admin.shop.edit', передавая туда редактируемый предмет магазина
        return view('admin.shop.edit', compact('shopItem'));
    }

    /**
     * Обновляет указанный предмет магазина в базе данных.
     * Updates the specified shop item in storage.
     *
     * @param Request $request Входящий HTTP-запрос с данными формы
     * @param ShopItem $shopItem Экземпляр модели ShopItem (используется Route Model Binding)
     * @return \Illuminate\Http\RedirectResponse Перенаправление на список предметов магазина с сообщением
     */
    public function update(Request $request, ShopItem $shopItem)
    {
        // Проводим валидацию входящих данных из формы
        $validatedData = $request->validate([
            // Цены необязательны, но должны быть целыми неотрицательными числами
            'price_gold' => 'nullable|integer|min:0',
            'price_silver' => 'nullable|integer|min:0|max:99',
            'price_bronze' => 'nullable|integer|min:0|max:99',
            // Статус доступности
            'is_available' => 'sometimes|boolean',
        ], [
            // Сообщения об ошибках валидации
            'price_gold.integer' => 'Цена (золото) должна быть целым числом.',
            'price_gold.min' => 'Цена (золото) не может быть отрицательной.',
            'price_silver.integer' => 'Цена (серебро) должна быть целым числом.',
            'price_silver.min' => 'Цена (серебро) не может быть отрицательной.',
            'price_silver.max' => 'Цена (серебро) не может быть больше 99.',
            'price_bronze.integer' => 'Цена (бронза) должна быть целым числом.',
            'price_bronze.min' => 'Цена (бронза) не может быть отрицательной.',
            'price_bronze.max' => 'Цена (бронза) не может быть больше 99.',
        ]);

        // Проверяем, указана ли хоть какая-то цена
        if (
            ($validatedData['price_gold'] ?? $shopItem->price_gold ?? 0) == 0 &&
            ($validatedData['price_silver'] ?? $shopItem->price_silver ?? 0) == 0 &&
            ($validatedData['price_bronze'] ?? $shopItem->price_bronze ?? 0) == 0
        ) {
            // Если цена не указана и не была указана ранее, возвращаем ошибку
            return redirect()->back()->withErrors(['price' => 'Необходимо указать цену предмета.'])->withInput();
        }

        // Обновляем данные предмета магазина
        $shopItem->update([
            // Обновляем цены, используя старое значение, если новое не пришло
            'price_gold' => $validatedData['price_gold'] ?? $shopItem->price_gold ?? 0,
            'price_silver' => $validatedData['price_silver'] ?? $shopItem->price_silver ?? 0,
            'price_bronze' => $validatedData['price_bronze'] ?? $shopItem->price_bronze ?? 0,
            // Обновляем статус доступности
            'is_available' => $request->has('is_available') ? $request->boolean('is_available') : $shopItem->is_available,
        ]);

        // Получаем имя обновленного предмета для сообщения
        $itemName = $shopItem->item->name ?? 'Предмет'; // Получаем имя из связанной модели Item

        // Перенаправляем администратора обратно на страницу списка предметов магазина
        // с сообщением об успешном обновлении
        return redirect()->route('admin.shop.index')
            ->with('success', "Цена для предмета '{$itemName}' успешно обновлена.");
    }

    /**
     * Удаляет указанный предмет из магазина (запись ShopItem).
     * Removes the specified shop item from storage.
     *
     * @param ShopItem $shopItem Экземпляр модели ShopItem (используется Route Model Binding)
     * @return \Illuminate\Http\RedirectResponse Перенаправление на список предметов магазина с сообщением
     */
    public function destroy(ShopItem $shopItem)
    {
        // Получаем имя предмета перед удалением для сообщения
        $itemName = $shopItem->item->name ?? 'Предмет'; // Получаем имя из связанной модели Item

        // Удаляем запись ShopItem из базы данных
        $shopItem->delete();

        // Перенаправляем администратора обратно на страницу списка предметов магазина
        // с сообщением об успешном удалении
        return redirect()->route('admin.shop.index')
            ->with('success', "Предмет '{$itemName}' успешно удален из магазина.");
    }

    /**
     * Сохраняет новый рецепт магазина в базе данных.
     * Stores a newly created shop recipe in storage.
     *
     * @param Request $request Входящий HTTP-запрос с данными формы
     * @return \Illuminate\Http\RedirectResponse Перенаправление на список предметов магазина с сообщением
     */
    public function storeRecipe(Request $request)
    {
        // Проводим валидацию входящих данных из формы
        $validatedData = $request->validate([
            // Поле 'potion_recipe_id' обязательно и должно существовать в таблице 'potion_recipes'
            'potion_recipe_id' => 'required|exists:potion_recipes,id|unique:shop_recipes,potion_recipe_id',
            // Поля цены не обязательны, но должны быть целыми числами не меньше 0
            'price_gold' => 'nullable|integer|min:0',
            'price_silver' => 'nullable|integer|min:0|max:99',
            'price_bronze' => 'nullable|integer|min:0|max:99',
            // Поле 'is_available' для статуса доступности, по умолчанию true
            'is_available' => 'sometimes|boolean',
            // Категория магазина (опционально)
            'shop_category' => 'nullable|string|max:255',
        ], [
            // Сообщения об ошибках валидации на русском языке
            'potion_recipe_id.required' => 'Необходимо выбрать рецепт.',
            'potion_recipe_id.exists' => 'Выбранный рецепт не существует.',
            'potion_recipe_id.unique' => 'Этот рецепт уже добавлен в магазин.',
            'price_gold.integer' => 'Цена (золото) должна быть целым числом.',
            'price_gold.min' => 'Цена (золото) не может быть отрицательной.',
            'price_silver.integer' => 'Цена (серебро) должна быть целым числом.',
            'price_silver.min' => 'Цена (серебро) не может быть отрицательной.',
            'price_silver.max' => 'Цена (серебро) не может быть больше 99.',
            'price_bronze.integer' => 'Цена (бронза) должна быть целым числом.',
            'price_bronze.min' => 'Цена (бронза) не может быть отрицательной.',
            'price_bronze.max' => 'Цена (бронза) не может быть больше 99.',
        ]);

        // Создаем новый экземпляр ShopRecipe с валидированными данными
        ShopRecipe::create([
            'potion_recipe_id' => $validatedData['potion_recipe_id'],
            // Используем оператор null coalescing (??) для установки 0, если цена не указана
            'price_gold' => $validatedData['price_gold'] ?? 0,
            'price_silver' => $validatedData['price_silver'] ?? 0,
            'price_bronze' => $validatedData['price_bronze'] ?? 0,
            // Устанавливаем статус доступности (по умолчанию true, если не пришло значение false)
            'is_available' => $request->has('is_available') ? $request->boolean('is_available') : true,
            // Категория магазина
            'shop_category' => $validatedData['shop_category'] ?? null,
        ]);

        // Получаем имя добавленного рецепта для сообщения
        $recipeName = PotionRecipe::find($validatedData['potion_recipe_id'])->name ?? 'Рецепт';

        // Перенаправляем администратора обратно на страницу списка предметов магазина
        // с сообщением об успешном добавлении
        return redirect()->route('admin.shop.index', ['tab' => 'recipes'])
            ->with('success', "Рецепт '{$recipeName}' успешно добавлен в магазин.");
    }

    /**
     * Удаляет указанный рецепт из магазина (запись ShopRecipe).
     * Removes the specified shop recipe from storage.
     *
     * @param ShopRecipe $shopRecipe Экземпляр модели ShopRecipe (используется Route Model Binding)
     * @return \Illuminate\Http\RedirectResponse Перенаправление на список предметов магазина с сообщением
     */
    public function destroyRecipe(ShopRecipe $shopRecipe)
    {
        // Получаем имя рецепта перед удалением для сообщения
        $recipeName = $shopRecipe->potionRecipe->name ?? 'Рецепт';

        // Удаляем запись ShopRecipe из базы данных
        $shopRecipe->delete();

        // Перенаправляем администратора обратно на страницу списка предметов магазина
        // с сообщением об успешном удалении
        return redirect()->route('admin.shop.index', ['tab' => 'recipes'])
            ->with('success', "Рецепт '{$recipeName}' успешно удален из магазина.");
    }
}