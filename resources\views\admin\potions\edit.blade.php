<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Редактирование зелья: {{ $potion->name }} - Админ-панель</title>
    {{-- Удаляем старые стили Material Dashboard и Font Awesome --}}
    {{--
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/material-dashboard.css') }}"> --}}
    {{--
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/nucleo-icons.css') }}"> --}}
    {{--
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/nucleo-svg.css') }}"> --}}
    {{--
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"> --}}
    {{--
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet"> --}}
    {{--
    <link rel="stylesheet" href="{{ asset('assets/js/plugins/color-picker/spectrum.min.css') }}"> --}}

    {{-- Добавляем директиву @vite для подключения ваших стилей --}}
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    {{-- Удаляем встроенные стили, если они больше не нужны или конфликтуют --}}
    {{-- <style>
        ...
    </style> --}}
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    <main class="flex-grow container mx-auto px-4 py-6">
        {{-- Верхний блок с информацией и управлением --}}
        <div
            class="container max-w-6xl mx-auto px-4 py-4 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg shadow-lg mb-6">
            <div class="flex justify-between items-center mb-4">
                {{-- Кнопка назад к списку зелий --}}
                <a href="{{ route('admin.potions.index') }}"
                    class="w-10 h-10 bg-[#a6925e] flex items-center justify-center rounded-lg hover:bg-[#e5b769] transition duration-300">
                    <span class="text-xl">←</span>
                </a>

                {{-- Заголовок панели --}}
                <h1 class="text-2xl font-bold text-[#e5b769] text-center">Редактирование зелья: {{ $potion->name }}</h1>

                {{-- Кнопка Просмотр (если нужен) --}}
                <a href="{{ route('admin.potions.show', $potion->id) }}"
                    class="px-4 py-2 bg-[#4a452c] text-[#e5b769] rounded hover:bg-[#5a5a4d] transition duration-300">
                    Просмотр
                </a>
            </div>

            {{-- Сообщения об ошибках --}}
            @if ($errors->any())
                <div class="bg-[#38352c] text-[#ff6b6b] p-4 rounded-lg border border-[#a6925e] mb-4">
                    <ul class="list-disc pl-5">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
        </div>

        {{-- Форма редактирования зелья --}}
        <div class="container max-w-6xl mx-auto mb-6">
            <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] rounded-lg p-6 border-2 border-[#a6925e]">
                <h2 class="text-xl font-bold text-[#e5b769] mb-6 text-center">Информация о зелье</h2>

                <form action="{{ route('admin.potions.update', $potion->id) }}" method="POST"
                    enctype="multipart/form-data" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @csrf
                    @method('PUT')

                    {{-- Левая колонка --}}
                    <div class="space-y-4">
                        {{-- Название зелья --}}
                        <div>
                            <label for="name" class="block text-sm text-[#d9d3b8] mb-2">Название зелья <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <input type="text" name="name" id="name" value="{{ old('name', $potion->name) }}" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                        </div>

                        {{-- Описание зелья --}}
                        <div>
                            <label for="description" class="block text-sm text-[#d9d3b8] mb-2">Описание <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <textarea name="description" id="description" rows="5" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">{{ old('description', $potion->description) }}</textarea>
                        </div>

                        {{-- Эффект зелья --}}
                        <div>
                            <label for="effect" class="block text-sm text-[#d9d3b8] mb-2">Тип эффекта <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <select name="effect" id="effect" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                @foreach ($potionEffects as $key => $value)
                                    <option value="{{ $key }}" {{ old('effect', $potion->effect) == $key ? 'selected' : '' }}>
                                        {{ $value }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        {{-- Значение эффекта --}}
                        <div>
                            <label for="effect_value" class="block text-sm text-[#d9d3b8] mb-2">Значение эффекта <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <input type="number" name="effect_value" id="effect_value"
                                value="{{ old('effect_value', $potion->effect_value) }}" step="0.1" min="0" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                            <p class="text-xs text-[#d9d3b8] mt-1">Базовое значение эффекта.</p>
                        </div>

                        {{-- Длительность эффекта --}}
                        <div>
                            <label for="effect_duration" class="block text-sm text-[#d9d3b8] mb-2">Длительность эффекта
                                (сек) <span class="text-[#ff6b6b]">*</span></label>
                            <input type="number" name="effect_duration" id="effect_duration"
                                value="{{ old('effect_duration', $potion->effect_duration) }}" min="0" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                            <p class="text-xs text-[#d9d3b8] mt-1">0 для мгновенных эффектов.</p>
                        </div>
                    </div>

                    {{-- Правая колонка --}}
                    <div class="space-y-4">
                        {{-- Уровень зелья --}}
                        <div>
                            <label for="level" class="block text-sm text-[#d9d3b8] mb-2">Уровень зелья <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <select name="level" id="level" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <option value="">-- Выберите уровень --</option>
                                @foreach ($potionLevels as $levelKey => $levelName)
                                    <option value="{{ $levelKey }}" {{ old('level', $potion->level) == $levelKey ? 'selected' : '' }}>
                                        {{ $levelName }}
                                    </option>
                                @endforeach
                            </select>
                            <p class="text-xs text-[#d9d3b8] mt-1">Выберите уровень зелья из доступных вариантов</p>
                        </div>

                        {{-- Качество зелья --}}
                        <div>
                            <label for="quality" class="block text-sm text-[#d9d3b8] mb-2">Качество зелья <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <select name="quality" id="quality" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                @foreach ($potionQualities as $quality)
                                    <option value="{{ $quality }}" {{ old('quality', $potion->quality) == $quality ? 'selected' : '' }}>
                                        {{ $quality }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        {{-- Количество использований --}}
                        <div>
                            <label for="uses_left" class="block text-sm text-[#d9d3b8] mb-2">Количество использований
                                <span class="text-[#ff6b6b]">*</span></label>
                            <input type="number" name="uses_left" id="uses_left"
                                value="{{ old('uses_left', $potion->uses_left) }}" min="0" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                            <p class="text-xs text-[#d9d3b8] mt-1">Текущее количество использований зелья</p>
                        </div>

                        {{-- Максимальное количество использований --}}
                        <div>
                            <label for="max_uses" class="block text-sm text-[#d9d3b8] mb-2">Максимальное количество
                                использований <span class="text-[#ff6b6b]">*</span></label>
                            <input type="number" name="max_uses" id="max_uses"
                                value="{{ old('max_uses', $potion->max_uses ?? $potion->uses_left) }}" min="1" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                            <p class="text-xs text-[#d9d3b8] mt-1">Максимальное количество использований зелья (для
                                отображения прогресса)</p>
                        </div>

                        {{-- Рецепт (опционально) --}}
                        <div>
                            <label for="recipe_id" class="block text-sm text-[#d9d3b8] mb-2">Рецепт (если есть)</label>
                            <select name="recipe_id" id="recipe_id"
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <option value="">-- Без рецепта --</option>
                                @foreach ($recipes as $id => $name)
                                    <option value="{{ $id }}" {{ old('recipe_id', $potion->recipe_id) == $id ? 'selected' : '' }}>
                                        {{ $name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        {{-- Иконка зелья --}}
                        <div>
                            <label class="block text-sm text-[#d9d3b8] mb-2">Текущая иконка</label>
                            <div class="mb-2">
                                @if ($potion->icon)
                                    <img src="{{ asset('assets/' . $potion->icon) }}" alt="{{ $potion->name }}"
                                        class="w-16 h-16 object-contain inline-block align-middle border border-[#8c784e] rounded p-1 bg-[#2a2721]">
                                    <span class="text-xs text-[#d9d3b8] ml-2 align-middle">{{ $potion->icon }}</span>
                                @else
                                    <span class="text-4xl inline-block align-middle">🧪</span>
                                    <span class="text-xs text-[#d9d3b8] ml-2 align-middle">(Иконка не задана)</span>
                                @endif
                            </div>

                            <label for="icon" class="block text-sm text-[#d9d3b8] mb-2">Иконка зелья <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <input type="text" name="icon" id="icon" value="{{ old('icon', $potion->icon) }}"
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]"
                                placeholder="potions/smallBottleHP.png">
                            <p class="text-xs text-[#d9d3b8] mt-1">Укажите только имя файла из директории potions,
                                например: potions/smallBottleHP.png</p>

                            <div class="mt-2">
                                <p class="text-xs text-[#d9d3b8] mb-1">Доступные иконки зелий:</p>
                                <div class="grid grid-cols-4 gap-2 mt-1">
                                    <div class="text-center">
                                        <img src="{{ asset('assets/potions/smallBottleHP.png') }}"
                                            alt="Малый флакон здоровья" class="w-8 h-8 mx-auto">
                                        <p class="text-xs mt-1">smallBottleHP.png</p>
                                    </div>
                                    <div class="text-center">
                                        <img src="{{ asset('assets/potions/mediumBottleHP.png') }}"
                                            alt="Средний флакон здоровья" class="w-8 h-8 mx-auto">
                                        <p class="text-xs mt-1">mediumBottleHP.png</p>
                                    </div>
                                    <div class="text-center">
                                        <img src="{{ asset('assets/potions/largeBottleHP.png') }}"
                                            alt="Большой флакон здоровья" class="w-8 h-8 mx-auto">
                                        <p class="text-xs mt-1">largeBottleHP.png</p>
                                    </div>
                                    <div class="text-center">
                                        <img src="{{ asset('assets/potions/smallBottleMP.png') }}"
                                            alt="Малый флакон маны" class="w-8 h-8 mx-auto">
                                        <p class="text-xs mt-1">smallBottleMP.png</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="color" id="color"
                            value="{{ old('color', $potion->color ?? '#6c757d') }}">

                        {{-- Владелец зелья (если нужно) --}}
                        <div>
                            <label for="user_id" class="block text-sm text-[#d9d3b8] mb-2">Владелец зелья</label>
                            <select name="user_id" id="user_id"
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <option value="">-- Нет владельца --</option>
                                @foreach ($users as $id => $name)
                                    <option value="{{ $id }}" {{ old('user_id', $potion->user_id) == $id ? 'selected' : '' }}>
                                        {{ $name }} (ID: {{ $id }})
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        {{-- Флаг шаблона --}}
                        <div class="mt-4">
                            <label class="flex items-center">
                                <input type="checkbox" name="is_template" id="is_template" value="1" {{ old('is_template', $potion->is_template) ? 'checked' : '' }}
                                    class="w-5 h-5 bg-[#2a2721] border border-[#8c784e] rounded text-[#a6925e] focus:outline-none focus:border-[#e5b769]">
                                <span class="ml-2 text-sm text-[#d9d3b8]">Это шаблон зелья</span>
                            </label>
                            <p class="text-xs text-[#d9d3b8] mt-1">Шаблоны используются для создания экземпляров зелий
                                для игроков</p>
                        </div>

                        {{-- Информация о связанном игровом предмете --}}
                        @if($potion->game_item_id)
                            <div class="mt-4 p-3 bg-[#38352c] border border-[#8c784e] rounded">
                                <h3 class="text-sm font-bold text-[#e5b769] mb-2">Связанный игровой предмет</h3>
                                <p class="text-xs text-[#d9d3b8]">ID: {{ $potion->game_item_id }}</p>
                                @if($potion->gameItem)
                                    <p class="text-xs text-[#d9d3b8]">Владелец:
                                        {{ $potion->gameItem->owner ? $potion->gameItem->owner->name : 'Нет' }}</p>
                                    <p class="text-xs text-[#d9d3b8]">Расположение: {{ $potion->gameItem->location }}</p>
                                @else
                                    <p class="text-xs text-[#ff6b6b]">Игровой предмет не найден</p>
                                @endif
                            </div>
                        @endif

                    </div>

                    {{-- Кнопки формы (внизу на всю ширину) --}}
                    <div class="md:col-span-2 flex justify-between mt-6">
                        <a href="{{ route('admin.potions.index') }}"
                            class="px-4 py-2 bg-[#4a4a3d] text-[#e5b769] rounded font-semibold hover:bg-[#5a5a4d] transition duration-300">
                            Отмена
                        </a>
                        <button type="submit"
                            class="px-6 py-2 bg-[#a6925e] text-[#2f2d2b] rounded-lg font-semibold hover:bg-[#e5b769] transition duration-300">
                            Обновить зелье
                        </button>
                    </div>
                </form>
            </div>
        </div>

        {{-- Футер --}}
        <footer
            class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] text-[#d9d3b8] py-3 mt-6 border-t-2 border-[#a6925e] rounded-lg">
            <div class="container max-w-6xl mx-auto px-6 text-center">
                <div class="flex justify-between items-center flex-wrap">
                    <div>
                        <p class="text-sm font-semibold">
                            <a href="{{ route('admin.dashboard') }}" class="text-[#e5b769] hover:underline">
                                Вернуться в админ-панель
                            </a>
                        </p>
                    </div>
                    <div>
                        <p class="text-sm font-semibold">
                            Время: <span class="text-[#e5b769]">{{ date('H:i') }}</span>
                        </p>
                    </div>
                </div>
                <p class="text-sm font-semibold mt-3">
                    © {{ date('Y') }} Echoes of Eternity. Все права защищены.
                </p>
            </div>
        </footer>
    </main>
</body>

</html>