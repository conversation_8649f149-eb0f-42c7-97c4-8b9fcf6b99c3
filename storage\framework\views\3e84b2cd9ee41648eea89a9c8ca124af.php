<!DOCTYPE html>
<html lang="ru">


<head>
    <meta charset="UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>Добавить рецепт в магазин - Админ панель</title>
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js', 'resources/js/admin/shop-recipe-preview.js']); ?>
    
</head>



<body class="bg-[#1a1814] text-[#d4cbb0] font-serif">
    
    <div class="min-h-screen flex flex-col">
        
        <main class="flex-grow container mx-auto px-4 py-6">
            
            <div
                class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] border-2 border-[#3b3629] rounded-lg shadow-lg mb-8 p-5">
                
                <div class="flex justify-between items-center mb-5">
                    
                    <a href="<?php echo e(route('admin.shop.index', ['tab' => 'recipes'])); ?>" title="Назад к списку рецептов магазина"
                        class="p-2 bg-[#3b3629] hover:bg-[#c1a96e] rounded-lg transition duration-300 border border-[#6e3f35]">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#c1a96e]" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M11 15l-3-3m0 0l3-3m-3 3h8M3 12a9 9 0 1118 0 9 9 0 01-18 0z" />
                        </svg>
                    </a>

                    
                    <h1 class="text-3xl font-bold text-[#e4d7b0]">Добавить Рецепт в Магазин</h1>

                    
                    <div class="w-10"></div>
                </div>

                
                <?php if($errors->any()): ?>
                    <div class="bg-gradient-to-r from-[#59372d] to-[#3c221b] border border-[#6e3f35] text-[#f8eac2] px-4 py-3 rounded relative mb-4" role="alert">
                        <strong class="font-bold">Ошибка валидации!</strong>
                        <ul class="mt-2">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="text-sm">• <?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>

            
            <form action="<?php echo e(route('admin.shop.store-recipe')); ?>" method="POST"
                class="bg-[#1a1814] border border-[#3b3629] rounded-lg shadow-md p-6">
                <?php echo csrf_field(); ?>
                

                
                <div class="mb-4">
                    <label for="potion_recipe_id" class="block text-sm font-medium text-[#e4d7b0] mb-1">Рецепт зелья</label>
                    
                    <select id="potion_recipe_id" name="potion_recipe_id" required
                        class="w-full px-3 py-2 bg-[#2a2722] border border-[#3b3629] rounded-md shadow-sm text-[#d4cbb0] focus:outline-none focus:ring-[#c1a96e] focus:border-[#c1a96e]">
                        
                        <option value="" disabled selected>-- Выберите рецепт --</option>
                        
                        <?php $__empty_1 = true; $__currentLoopData = $recipes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recipe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            
                            <option value="<?php echo e($recipe->id); ?>"
                                    data-quality="<?php echo e($recipe->quality); ?>"
                                    data-level="<?php echo e($recipe->level); ?>"
                                    data-icon="<?php echo e($recipe->icon); ?>"
                                    <?php echo e(old('potion_recipe_id') == $recipe->id ? 'selected' : ''); ?>>
                                
                                <?php echo e($recipe->name); ?> (<?php echo e($recipe->quality ?? 'N/A'); ?>, <?php echo e($recipe->level ?? 'N/A'); ?>)
                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <option value="" disabled>Нет доступных рецептов для добавления</option>
                            
                        <?php endif; ?>
                        
                    </select>
                </div>

                
                <div id="recipe-preview" class="hidden mb-6 bg-[#2a2722] border border-[#3b3629] rounded-lg p-4">
                    <h3 class="text-[#e4d7b0] font-semibold mb-3">Информация о рецепте:</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-[#998d66]">Качество:</span>
                            <span id="preview-quality" class="text-[#d4cbb0] ml-2"></span>
                        </div>
                        <div>
                            <span class="text-[#998d66]">Уровень:</span>
                            <span id="preview-level" class="text-[#d4cbb0] ml-2"></span>
                        </div>
                        <div>
                            <span class="text-[#998d66]">Иконка:</span>
                            <span id="preview-icon" class="text-[#c1a96e] ml-2 font-medium"></span>
                        </div>
                    </div>
                    <div class="mt-3 p-3 bg-[#1a1814] rounded border border-[#3b3629]">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#c1a96e] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="text-[#998d66] text-xs">Рецепт будет доступен для покупки игроками в магазине</span>
                        </div>
                    </div>
                </div>

                
                <div class="mb-6">
                    <label class="block text-sm font-medium text-[#e4d7b0] mb-2">Цена в магазине</label>
                    
                    <div class="grid grid-cols-3 gap-4">
                        
                        <div>
                            <label for="price_gold" class="block text-xs font-medium text-[#c1a96e] mb-1">Золото</label>
                            <input type="number" id="price_gold" name="price_gold" min="0"
                                value="<?php echo e(old('price_gold', 0)); ?>"
                                class="w-full px-3 py-2 bg-[#2a2722] border border-[#3b3629] rounded-md shadow-sm text-[#c1a96e] focus:outline-none focus:ring-[#c1a96e] focus:border-[#c1a96e] placeholder:text-[#998d66]"
                                placeholder="0">
                        </div>
                        
                        <div>
                            <label for="price_silver"
                                class="block text-xs font-medium text-[#998d66] mb-1">Серебро</label>
                            <input type="number" id="price_silver" name="price_silver" min="0" max="99"
                                value="<?php echo e(old('price_silver', 0)); ?>"
                                class="w-full px-3 py-2 bg-[#2a2722] border border-[#3b3629] rounded-md shadow-sm text-[#998d66] focus:outline-none focus:ring-[#c1a96e] focus:border-[#c1a96e] placeholder:text-[#998d66]"
                                placeholder="0">
                        </div>
                        
                        <div>
                            <label for="price_bronze"
                                class="block text-xs font-medium text-[#6e3f35] mb-1">Бронза</label>
                            <input type="number" id="price_bronze" name="price_bronze" min="0" max="99"
                                value="<?php echo e(old('price_bronze', 0)); ?>"
                                class="w-full px-3 py-2 bg-[#2a2722] border border-[#3b3629] rounded-md shadow-sm text-[#6e3f35] focus:outline-none focus:ring-[#c1a96e] focus:border-[#c1a96e] placeholder:text-[#998d66]"
                                placeholder="0">
                        </div>
                    </div>
                </div>

                
                <div class="mb-6">
                    <label for="shop_category" class="block text-sm font-medium text-[#e4d7b0] mb-1">Категория магазина (опционально)</label>
                    <input type="text" id="shop_category" name="shop_category" maxlength="255"
                        value="<?php echo e(old('shop_category')); ?>"
                        class="w-full px-3 py-2 bg-[#2a2722] border border-[#3b3629] rounded-md shadow-sm text-[#d4cbb0] focus:outline-none focus:ring-[#c1a96e] focus:border-[#c1a96e] placeholder:text-[#998d66]"
                        placeholder="Например: Лечебные зелья, Боевые зелья">
                </div>

                
                <div class="mb-6">
                    <label for="is_available" class="flex items-center">
                        <input type="checkbox" id="is_available" name="is_available" value="1" <?php echo e(old('is_available', true) ? 'checked' : ''); ?>

                            class="h-4 w-4 text-[#c1a96e] bg-[#2a2722] border-[#3b3629] rounded focus:ring-[#c1a96e]">
                        
                        <span class="ml-2 text-sm text-[#d4cbb0]">Доступен для покупки</span>
                        
                    </label>
                </div>

                
                <div class="flex justify-end space-x-4">
                    
                    <a href="<?php echo e(route('admin.shop.index', ['tab' => 'recipes'])); ?>"
                        class="py-2 px-4 bg-gradient-to-b from-[#59372d] to-[#3c221b] text-[#f8eac2] rounded-md hover:from-[#6e3f35] hover:to-[#59372d] border border-[#6e3f35] transition duration-300">
                        Отмена
                    </a>
                    
                    <button type="submit"
                        class="py-2 px-4 bg-gradient-to-b from-[#2f473c] to-[#1e2e27] text-[#f8eac2] rounded-md hover:from-[#3b5a4a] hover:to-[#2f473c] border border-[#2f473c] font-semibold transition duration-300">
                        Добавить рецепт
                    </button>
                </div>
            </form>

        </main>

        
        <footer class="bg-[#1a1814] text-[#998d66] py-4 mt-auto border-t border-[#3b3629]">
            <div class="container max-w-7xl mx-auto px-6 text-center text-sm">
                © <?php echo e(date('Y')); ?> Echoes of Eternity | Административная Панель
            </div>
        </footer>
    </div>



</body>

</html>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/shop/create-recipe.blade.php ENDPATH**/ ?>