/**
 * JavaScript для предварительного просмотра рецепта в админке магазина
 * JavaScript for recipe preview in shop admin panel
 */

document.addEventListener('DOMContentLoaded', function() {
    // Получаем элементы DOM
    const recipeSelect = document.getElementById('potion_recipe_id');
    const recipePreview = document.getElementById('recipe-preview');
    const previewQuality = document.getElementById('preview-quality');
    const previewLevel = document.getElementById('preview-level');
    const previewIcon = document.getElementById('preview-icon');

    // Проверяем, что все элементы найдены
    if (!recipeSelect || !recipePreview || !previewQuality || !previewLevel || !previewIcon) {
        console.warn('Не все элементы для предварительного просмотра рецепта найдены');
        return;
    }

    // Добавляем обработчик события изменения выбора рецепта
    recipeSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        
        if (selectedOption.value) {
            // Показываем предварительный просмотр
            recipePreview.classList.remove('hidden');
            
            // Заполняем данные из data-атрибутов
            previewQuality.textContent = selectedOption.dataset.quality || 'N/A';
            previewLevel.textContent = selectedOption.dataset.level || 'N/A';
            previewIcon.textContent = selectedOption.dataset.icon || 'N/A';
            
            // Добавляем плавную анимацию появления
            recipePreview.style.opacity = '0';
            setTimeout(() => {
                recipePreview.style.transition = 'opacity 0.3s ease-in-out';
                recipePreview.style.opacity = '1';
            }, 10);
        } else {
            // Скрываем предварительный просмотр с анимацией
            recipePreview.style.transition = 'opacity 0.3s ease-in-out';
            recipePreview.style.opacity = '0';
            
            setTimeout(() => {
                recipePreview.classList.add('hidden');
            }, 300);
        }
    });

    // Дополнительная валидация формы
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const selectedRecipe = recipeSelect.value;
            
            if (!selectedRecipe) {
                e.preventDefault();
                alert('Пожалуйста, выберите рецепт для добавления в магазин.');
                recipeSelect.focus();
                return false;
            }
            
            // Проверяем, что хотя бы одна цена указана
            const goldPrice = parseInt(document.getElementById('price_gold').value) || 0;
            const silverPrice = parseInt(document.getElementById('price_silver').value) || 0;
            const bronzePrice = parseInt(document.getElementById('price_bronze').value) || 0;
            
            if (goldPrice === 0 && silverPrice === 0 && bronzePrice === 0) {
                const confirmZeroPrice = confirm('Вы уверены, что хотите установить нулевую цену для этого рецепта?');
                if (!confirmZeroPrice) {
                    e.preventDefault();
                    document.getElementById('price_silver').focus();
                    return false;
                }
            }
        });
    }

    // Автоматическое ограничение ввода для полей цены
    const priceInputs = ['price_gold', 'price_silver', 'price_bronze'];
    priceInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', function() {
                // Убираем отрицательные значения
                if (this.value < 0) {
                    this.value = 0;
                }
                
                // Ограничиваем серебро и бронзу до 99
                if ((inputId === 'price_silver' || inputId === 'price_bronze') && this.value > 99) {
                    this.value = 99;
                }
            });
        }
    });
});
