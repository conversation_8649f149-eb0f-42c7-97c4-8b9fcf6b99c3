<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PotionRecipe;
use App\Models\ShopRecipe;

class TestShopRecipeSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:shop-recipes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Тестирует систему рецептов в магазине';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Тестирование системы рецептов в магазине...');

        // Проверяем количество рецептов
        $recipesCount = PotionRecipe::count();
        $this->info("📋 Всего рецептов в системе: {$recipesCount}");

        // Проверяем количество рецептов в магазине
        $shopRecipesCount = ShopRecipe::count();
        $this->info("🏪 Рецептов в магазине: {$shopRecipesCount}");

        // Показываем доступные для добавления рецепты
        $availableRecipes = PotionRecipe::whereDoesntHave('shopRecipe')
            ->where('is_active', true)
            ->get();

        $this->info("✅ Доступно для добавления в магазин: {$availableRecipes->count()}");

        if ($availableRecipes->count() > 0) {
            $this->info("📝 Список доступных рецептов:");
            foreach ($availableRecipes as $recipe) {
                $this->line("  - {$recipe->name} (ID: {$recipe->id}, Качество: {$recipe->quality})");
            }

            // Автоматически добавляем первый рецепт в магазин для тестирования
            $firstRecipe = $availableRecipes->first();
            if ($firstRecipe && $this->confirm("Добавить рецепт '{$firstRecipe->name}' в магазин для тестирования?")) {
                $shopRecipe = ShopRecipe::create([
                    'potion_recipe_id' => $firstRecipe->id,
                    'price_gold' => 0,
                    'price_silver' => 10,
                    'price_bronze' => 0,
                    'is_available' => true,
                    'shop_category' => 'Тестовые рецепты',
                ]);

                $this->info("✅ Рецепт '{$firstRecipe->name}' успешно добавлен в магазин!");
                $this->info("💰 Цена: {$shopRecipe->price_gold} золота, {$shopRecipe->price_silver} серебра, {$shopRecipe->price_bronze} бронзы");
            }
        }

        // Показываем рецепты в магазине
        $shopRecipes = ShopRecipe::with('potionRecipe')->get();
        if ($shopRecipes->count() > 0) {
            $this->info("🏪 Рецепты в магазине:");
            foreach ($shopRecipes as $shopRecipe) {
                $status = $shopRecipe->is_available ? '✅ Доступен' : '❌ Недоступен';
                $this->line("  - {$shopRecipe->potionRecipe->name} | {$shopRecipe->price_gold}g {$shopRecipe->price_silver}s {$shopRecipe->price_bronze}b | {$status}");
            }
        }

        $this->info('🎉 Тестирование завершено!');

        return Command::SUCCESS;
    }
}
