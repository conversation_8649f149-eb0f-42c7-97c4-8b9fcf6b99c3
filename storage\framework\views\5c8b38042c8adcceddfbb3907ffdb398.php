<?php $__env->startSection('content'); ?>
    
    <div id="notification-container"></div>

    
    <?php if (isset($component)) { $__componentOriginalebd36c10d138d0536742a29111ee94d4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalebd36c10d138d0536742a29111ee94d4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.inventory.tabs','data' => ['tab' => $tab ?? 'item','counts' => $counts ?? []]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('inventory.tabs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tab' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($tab ?? 'item'),'counts' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($counts ?? [])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalebd36c10d138d0536742a29111ee94d4)): ?>
<?php $attributes = $__attributesOriginalebd36c10d138d0536742a29111ee94d4; ?>
<?php unset($__attributesOriginalebd36c10d138d0536742a29111ee94d4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalebd36c10d138d0536742a29111ee94d4)): ?>
<?php $component = $__componentOriginalebd36c10d138d0536742a29111ee94d4; ?>
<?php unset($__componentOriginalebd36c10d138d0536742a29111ee94d4); ?>
<?php endif; ?>


    
    <div class="mt-0">

        
        <?php if(($tab ?? 'item') === 'item'): ?>
            
            <?php if(isset($inventory) && !$inventory->isEmpty()): ?>
                
                <h3 class="text-[#e5b769] text-lg font-bold text-center mb-2 border-t border-b border-[#a6925e] py-1 bg-[#2a2621]">
                    Ваши Предметы</h3>
                
                <div class="space-y-4 max-w-md mx-auto">
                    <?php $__currentLoopData = $inventory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $inventoryItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            // Получаем глобальный шаблон предмета
                            $item = $inventoryItem->item;
                            // Определение цвета качества предмета
                            $qualityColors = [
                                'Обычное' => 'text-gray-200',
                                'Необычное' => 'text-green-400',
                                'Редкое' => 'text-blue-400',
                                'Эпическое' => 'text-purple-400',
                                'Легендарное' => 'text-orange-400',
                            ];
                            $qualityColor = $qualityColors[$item->quality] ?? 'text-gray-200';
                            $qualityBgColor = str_replace('text-', 'bg-', $qualityColor);
                            // Русский комментарий: Определение класса для цвета свечения
                            $glowColorClass = str_replace('text-', 'glow-color-', $qualityColor);
                        ?>
                        
                        <div class="relative rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-[1.02]"
                            id="inventory-item-<?php echo e($inventoryItem->id); ?>">
                            
                            <div class="absolute inset-0 bg-gradient-to-br from-[#2a2621] to-[#1a1814] opacity-95"></div>
                            
                            <div class="absolute inset-0 rounded-lg border border-[#a6925e] pointer-events-none"></div>
                            
                            <div
                                class="absolute inset-x-0 top-0 h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
                            </div>
                            <div
                                class="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
                            </div>

                            
                            <div class="relative p-3">
                                
                                <div class="flex items-start mb-3">
                                    
                                    <div class="relative flex-shrink-0 animate-breathing-glow <?php echo e($glowColorClass); ?> rounded-md mr-3">
                                        
                                        <div
                                            class="relative w-16 h-16 bg-[#252117] rounded-md border border-[#a6925e] overflow-hidden flex items-center justify-center shadow-md">
                                            
                                            <div
                                                class="absolute inset-0 bg-gradient-to-br from-[#3b3a33]/30 to-[#252117]/50 blur-[1px]">
                                            </div>
                                            <img src="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>" alt="<?php echo e($item->name); ?>"
                                                class="relative z-10 w-14 h-14 object-contain" style="image-rendering: crisp-edges;">
                                            
                                            <?php if($inventoryItem->current_power): ?>
                                                <p
                                                    class="absolute -bottom-1 right-1 text-xs font-bold text-green-300 bg-[#1f1c18] px-1 rounded border border-green-700 shadow-sm">
                                                    (+<?php echo e($inventoryItem->current_power); ?>)
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    
                                    <div class="flex-1">
                                        
                                        <h4 class="text-base font-bold <?php echo e($qualityColor); ?> leading-tight mb-1.5"
                                            style="text-shadow: 0px 1px 3px rgba(0,0,0,0.8);"><?php echo e($item->name); ?></h4>

                                        
                                        <div class="flex items-center text-xs mb-2 text-[#d9d3b8]">
                                            <span
                                                class="px-1.5 py-0.5 bg-[#38352c] rounded border border-[#514b3c] mr-1.5"><?php echo e($item->type ?? 'Предмет'); ?></span>
                                            <span class="px-1.5 py-0.5 bg-[#38352c] rounded border border-[#514b3c]">Ур:
                                                <?php echo e($item->level_required ?? '?'); ?></span>
                                        </div>

                                        
                                        <div class="flex items-center text-xs mb-1 text-[#d9d3b8]">
                                            <div class="w-2 h-2 rounded-full mr-1.5 <?php echo e($qualityBgColor); ?>"></div>
                                            <span class="<?php echo e($qualityColor); ?> font-semibold"><?php echo e($item->quality); ?></span>
                                        </div>
                                    </div>
                                </div>

                                
                                <div class="mb-3">
                                    <div class="flex justify-between items-center text-xs mb-1">
                                        <span class="text-[#c0b9a3] font-medium">Прочность:</span>
                                        <span class="text-[#d9d3b8] font-mono">
                                            <?php echo e($inventoryItem->durability); ?>/<?php echo e($inventoryItem->max_durability); ?>

                                        </span>
                                    </div>
                                    <div class="w-full bg-[#1f1c18] rounded-full h-2 border border-[#4a463b]">
                                        <?php
                                            $durabilityPercentage = ($inventoryItem->max_durability > 0) ? ($inventoryItem->durability / $inventoryItem->max_durability) * 100 : 0;
                                            $durabilityColorClass = 'bg-red-600'; // По умолчанию красный
                                            if ($durabilityPercentage > 60) {
                                                $durabilityColorClass = 'bg-green-500'; // Зеленый
                                            } elseif ($durabilityPercentage > 30) {
                                                $durabilityColorClass = 'bg-yellow-500'; // Желтый
                                            }
                                        ?>
                                        <div class="<?php echo e($durabilityColorClass); ?> h-full rounded-full transition-all duration-300"
                                            style="width: <?php echo e($durabilityPercentage); ?>%"></div>
                                    </div>
                                </div>

                                
                                <div class="grid grid-cols-4 gap-2">
                                    
                                    <button onclick="equipItem(<?php echo e($inventoryItem->id); ?>)"
                                        class="w-full bg-gradient-to-b from-[#c4a76d] to-[#a6925e] text-[#2f2d2b] py-1.5 px-3 text-xs font-medium rounded shadow-md hover:from-[#d4b781] hover:to-[#b7a36f] transition-all duration-300 border border-[#d4b781] flex items-center justify-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                                        </svg>
                                        Надеть
                                    </button>
                                    
                                    <button onclick="showItemInfo(<?php echo e($inventoryItem->id); ?>)"
                                        class="w-full bg-gradient-to-b from-[#514b3c] to-[#38352c] text-[#d9d3b8] py-1.5 px-3 text-xs font-medium rounded shadow-md hover:from-[#5d5745] hover:to-[#433f35] transition-all duration-300 border border-[#514b3c] flex items-center justify-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Инфо
                                    </button>
                                    
                                    <form action="<?php echo e(route('bank.move-to-bank')); ?>" method="POST" class="w-full">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="item_id" value="<?php echo e($inventoryItem->id); ?>">
                                        <input type="hidden" name="tab" value="items">
                                        <button type="submit"
                                            class="w-full bg-gradient-to-b from-[#38352c] to-[#252117] text-[#e5b769] py-1.5 px-3 text-xs font-medium rounded shadow-md hover:from-[#443f33] hover:to-[#2e2921] transition-all duration-300 border border-[#514b3c] flex items-center justify-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4">
                                                </path>
                                            </svg>
                                            В банк
                                        </button>
                                    </form>
                                    
                                    <button type="button"
                                        class="drop-button w-full bg-gradient-to-b from-[#4a3a3a] to-[#382c2c] text-red-300 py-1.5 px-3 text-xs font-medium rounded shadow-md hover:from-[#5e4a4a] hover:to-[#4a3535] transition-all duration-300 border border-[#a65e5e] flex items-center justify-center"
                                        data-item-id="<?php echo e($inventoryItem->id); ?>" data-item-type="item" data-item-name="<?php echo e($item->name); ?>"
                                        data-item-icon="<?php echo e(asset($item->icon ?? 'assets/placeholder.png')); ?>">
                                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                            </path>
                                        </svg>
                                        Выбросить
                                    </button>
                                </div>

                                
                                <p class="text-[9px] text-[#7b7564] mt-2 text-right">ID: <?php echo e($inventoryItem->id); ?></p>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <?php if($inventory->hasPages()): ?>
                    <div class="mt-4 flex justify-center">
                        <?php echo e($inventory->appends(['tab' => 'item'])->links()); ?>

                    </div>
                <?php endif; ?>

            <?php else: ?>
                <p class="text-gray-500 text-center">У вас нет предметов.</p>
            <?php endif; ?>
        <?php elseif(($tab ?? 'item') === 'resource'): ?>
            
            <?php if(isset($resources) && !$resources->isEmpty()): ?>
                
                <h3 class="text-[#e5b769] text-lg font-bold text-center mb-2 border-t border-b border-[#a6925e] py-1 bg-[#2a2621]">
                    Ваши Ресурсы</h3>
                
                <div class="space-y-4 max-w-md mx-auto">
                    <?php $__currentLoopData = $resources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userResource): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $resource = $userResource->resource; // Получаем связанный ресурс
                            // Определяем цвет индикатора количества в зависимости от близости к максимуму
                            $qty = $userResource->quantity;
                            $max = App\Models\UserResource::MAX_STACK_SIZE;
                            $isFullStack = $qty >= $max; // Флаг полного стака

                            // Русский комментарий: Устанавливаем красный цвет только для полного стака
                            $qtyColor = $isFullStack ? 'text-red-500 font-bold' : 'text-[#d9d3b8]';
                            // Цвет и текст качества ресурса
                            $rarityColors = [
                                'common' => 'text-gray-300',
                                'uncommon' => 'text-green-400',
                                'rare' => 'text-blue-400',
                                'epic' => 'text-purple-400',
                                'legendary' => 'text-orange-400',
                            ];
                            $rarityNames = [
                                'common' => 'Обычный',
                                'uncommon' => 'Необычный',
                                'rare' => 'Редкий',
                                'epic' => 'Эпический',
                                'legendary' => 'Легендарный',
                            ];
                            $rarity = $resource->rarity ?? 'common';
                            $rarityColor = $rarityColors[$rarity] ?? 'text-gray-300';
                            $rarityName = $rarityNames[$rarity] ?? 'Обычный';
                            // Русский комментарий: Определяем класс для цвета свечения ресурса
                            $rarityGlowClass = str_replace('text-', 'glow-color-', $rarityColor);
                        ?>
                        
                        <div class="relative rounded-lg overflow-hidden shadow-lg" id="resource-card-<?php echo e($userResource->id); ?>">
                            
                            <div class="absolute inset-0 bg-gradient-to-br from-[#2f2b24] to-[#1e1a15] opacity-95"></div>
                            
                            <div class="absolute inset-0 rounded-lg border border-[#7a6c51] pointer-events-none opacity-70">
                            </div>
                            
                            <div class="absolute inset-1 rounded border border-[#2a2621] pointer-events-none opacity-50 shadow-inner">
                            </div>

                            <div class="relative p-3 flex items-center space-x-3">
                                
                                <div class="relative flex-shrink-0 animate-breathing-glow <?php echo e($rarityGlowClass); ?> rounded-md">
                                    
                                    <div
                                        class="relative w-12 h-12 bg-[#252117] rounded-md border border-[#6b6658] overflow-hidden flex items-center justify-center shadow-inner">
                                        
                                        <?php if($resource && !empty($resource->icon_path)): ?>
                                            
                                            <img src="<?php echo e($resource->icon_path); ?>" alt="<?php echo e($resource->name); ?>"
                                                class="w-10 h-10 object-contain" style="image-rendering: crisp-edges;">
                                        <?php else: ?>
                                            
                                            <img src="<?php echo e(asset('assets/icons/resources/default.png')); ?>" alt="Нет иконки"
                                                class="w-10 h-10 object-contain opacity-60">
                                        <?php endif; ?>
                                    </div>
                                </div>

                                
                                <div class="flex-1 min-w-0"> 
                                    
                                    <h4 class="text-sm font-semibold <?php echo e($rarityColor); ?> leading-tight truncate mb-0.5"
                                        title="<?php echo e($resource->name); ?>"><?php echo e($resource->name); ?></h4>
                                    
                                    <p class="text-xs text-[#c0b9a3] mb-1.5 truncate" title="<?php echo e($resource->description); ?>">
                                        <?php echo e($resource->description); ?>

                                    </p>
                                    
                                    <div class="flex items-center justify-between text-xs">
                                        
                                        <span class="<?php echo e($rarityColor); ?> font-medium"><?php echo e($rarityName); ?></span>
                                        
                                        <div class="flex items-center">
                                            <span id="resource-qty-<?php echo e($userResource->id); ?>"
                                                class="font-mono <?php echo e($qtyColor); ?>"><?php echo e($userResource->quantity); ?></span>
                                            <span class="text-[#7b7564]">/<?php echo e($max); ?></span>
                                            
                                            <?php if($isFullStack): ?>
                                                <span class="ml-1 text-red-500 font-bold" title="Полный стак">✓</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                
                                <div class="flex space-x-2">
                                    
                                    <form action="<?php echo e(route('bank.move-to-bank')); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="resource_id" value="<?php echo e($userResource->id); ?>">
                                        <input type="hidden" name="tab" value="resources">
                                        <input type="hidden" name="quantity" value="<?php echo e($userResource->quantity); ?>">
                                        <button type="submit"
                                            class="w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#38352c] to-[#252117] text-[#e5b769] text-xs font-medium rounded shadow-md hover:from-[#443f33] hover:to-[#2e2921] transition-all duration-300 border border-[#514b3c] flex items-center justify-center"
                                            title="В банк">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4">
                                                </path>
                                            </svg>
                                        </button>
                                    </form>

                                    
                                    <button type="button"
                                        class="drop-button w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#4a3a3a] to-[#382c2c] text-red-300 text-xs font-medium rounded shadow-md hover:from-[#5e4a4a] hover:to-[#4a3535] transition-all duration-300 border border-[#a65e5e] flex items-center justify-center"
                                        data-item-id="<?php echo e($userResource->id); ?>" data-item-type="resource"
                                        data-item-name="<?php echo e($resource->name); ?>" data-item-max-quantity="<?php echo e($userResource->quantity); ?>">
                                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                            </path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <?php if($resources->hasPages()): ?>
                    <div class="mt-4 flex justify-center">
                        <?php echo e($resources->appends(['tab' => 'resource'])->links()); ?>

                    </div>
                <?php endif; ?>

            <?php else: ?>
                <p class="text-gray-500 text-center">У вас нет ресурсов.</p>
            <?php endif; ?>
        <?php elseif(($tab ?? 'item') === 'ingredient'): ?>
            
            <?php if(isset($alchemyIngredients) && !$alchemyIngredients->isEmpty()): ?>
                <h3 class="text-[#e5b769] text-lg font-bold text-center mb-2 border-t border-b border-[#a6925e] py-1 bg-[#2a2621]">
                    Алхимические Ингредиенты</h3>
                <div class="space-y-4 max-w-md mx-auto">
                    <?php $__currentLoopData = $alchemyIngredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userIngredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $ingredient = $userIngredient->ingredient; // Получаем связанный ингредиент
                            $maxStackIngredient = App\Models\UserAlchemyIngredient::MAX_STACK_SIZE; // Максимальный стак для ингредиентов
                            $isFullStack = $userIngredient->quantity >= $maxStackIngredient; // Проверка на полный стак
                            // Русский комментарий: Устанавливаем красный цвет только для полного стака
                            $qtyColorIngredient = $isFullStack ? 'text-red-500 font-bold' : 'text-[#d9d3b8]';
                            // Определение цвета качества ингредиента
                            $qualityColors = [
                                'Обычное' => 'text-gray-200',
                                'Необычное' => 'text-green-400',
                                'Редкое' => 'text-blue-400',
                                'Эпическое' => 'text-purple-400',
                                'Легендарное' => 'text-orange-400',
                            ];
                            $qualityColor = $qualityColors[$ingredient->quality] ?? 'text-gray-200';
                            $qualityBgColor = str_replace('text-', 'bg-', $qualityColor); // Для рамки или фона иконки
                            // Русский комментарий: Определяем класс для цвета свечения ингредиента
                            $ingredientGlowClass = str_replace('text-', 'glow-color-', $qualityColor);
                        ?>
                        
                        <div class="relative rounded-lg overflow-hidden shadow-lg" id="ingredient-card-<?php echo e($userIngredient->id); ?>">
                            <div class="absolute inset-0 bg-gradient-to-br from-[#2a2621] to-[#1a1814] opacity-95"></div>
                            <div class="absolute inset-0 rounded-lg border border-[#a6925e] pointer-events-none"></div>
                            <div class="relative p-3 flex items-center space-x-3">
                                
                                <div class="relative flex-shrink-0 animate-breathing-glow <?php echo e($ingredientGlowClass); ?> rounded-md">
                                    
                                    <div
                                        class="relative w-12 h-12 bg-[#252117] rounded-md border border-[#a6925e] overflow-hidden flex items-center justify-center shadow-sm">
                                        
                                        <?php if($ingredient && !empty($ingredient->icon_path)): ?>
                                            
                                            <img src="<?php echo e($ingredient->icon_path); ?>" alt="<?php echo e($ingredient->name); ?>"
                                                class="w-10 h-10 object-contain" style="image-rendering: crisp-edges;">
                                        <?php else: ?>
                                            
                                            <img src="<?php echo e(asset('assets/icons/alchemy/default.png')); ?>" alt="Нет иконки"
                                                class="w-10 h-10 object-contain opacity-60">
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold <?php echo e($qualityColor); ?> leading-tight mb-0.5">
                                        <?php echo e($ingredient->name); ?>

                                    </h4>
                                    <p class="text-xs text-[#c0b9a3] mb-1.5"><?php echo e($ingredient->description); ?></p>
                                    <div class="flex items-center justify-between">
                                        <span class="<?php echo e($qualityColor); ?> text-xs font-medium"><?php echo e($ingredient->quality); ?></span>
                                        <div class="flex items-center">
                                            <span id="ingredient-qty-<?php echo e($userIngredient->id); ?>"
                                                class="text-sm font-mono <?php echo e($qtyColorIngredient); ?>"><?php echo e($userIngredient->quantity); ?>/<?php echo e($maxStackIngredient); ?></span>
                                            <?php if($isFullStack): ?>
                                                <span class="ml-1.5 text-red-500 text-xs font-bold" title="Полный стак">✓</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex space-x-2">
                                    
                                    <form action="<?php echo e(route('bank.move-to-bank')); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="ingredient_id" value="<?php echo e($userIngredient->id); ?>">
                                        <input type="hidden" name="tab" value="ingredients">
                                        <input type="hidden" name="quantity" value="<?php echo e($userIngredient->quantity); ?>">
                                        <button type="submit"
                                            class="w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#38352c] to-[#252117] text-[#e5b769] text-xs font-medium rounded shadow-md hover:from-[#443f33] hover:to-[#2e2921] transition-all duration-300 border border-[#514b3c] flex items-center justify-center"
                                            title="В банк">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4">
                                                </path>
                                            </svg>
                                        </button>
                                    </form>

                                    
                                    <button type="button"
                                        class="drop-button w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#4a3a3a] to-[#382c2c] text-red-300 text-xs font-medium rounded shadow-md hover:from-[#5e4a4a] hover:to-[#4a3535] transition-all duration-300 border border-[#a65e5e] flex items-center justify-center"
                                        data-item-id="<?php echo e($userIngredient->id); ?>" data-item-type="ingredient"
                                        data-item-name="<?php echo e($ingredient->name); ?>"
                                        data-item-max-quantity="<?php echo e($userIngredient->quantity); ?>">
                                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                            </path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <?php if($alchemyIngredients->hasPages()): ?>
                    <div class="mt-4 flex justify-center">
                        <?php echo e($alchemyIngredients->appends(['tab' => 'ingredient'])->links()); ?>

                    </div>
                <?php endif; ?>

            <?php else: ?>
                <p class="text-gray-500 text-center">У вас нет алхимических ингредиентов.</p>
            <?php endif; ?>
        <?php elseif(($tab ?? 'item') === 'potion'): ?>
            
            <?php if(isset($potions) && !$potions->isEmpty()): ?>
                <h3 class="text-[#e5b769] text-lg font-bold text-center mb-2 border-t border-b border-[#a6925e] py-1 bg-[#2a2621]">
                    Ваши Зелья
                </h3>
                <div class="space-y-4 max-w-md mx-auto">
                    <?php $__currentLoopData = $potions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $potion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            // Определение цвета качества зелья
                            $qualityColors = [
                                'Обычное' => 'text-gray-200',
                                'Улучшенное' => 'text-green-400',
                                'Превосходное' => 'text-blue-400',
                                'Легендарное' => 'text-orange-400',
                            ];
                            $qualityColor = $qualityColors[$potion->quality] ?? 'text-gray-200';
                            $qualityBgColor = str_replace('text-', 'bg-', $qualityColor);

                            // Русский комментарий: Определяем класс для цвета свечения зелья
                            $potionGlowClass = str_replace('text-', 'glow-color-', $qualityColor);
                        ?>

                        
                        <div class="relative rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-[1.02]"
                            id="potion-card-<?php echo e($potion->id); ?>">
                            <div class="absolute inset-0 bg-gradient-to-br from-[#2a2621] to-[#1a1814] opacity-95"></div>
                            <div class="absolute inset-0 rounded-lg border border-[#a6925e] pointer-events-none"></div>
                            <div class="relative p-3">
                                
                                <div class="flex items-center space-x-3 mb-2">
                                    
                                    <div class="relative flex-shrink-0 animate-breathing-glow <?php echo e($potionGlowClass); ?> rounded-md">
                                        
                                        <div
                                            class="relative w-12 h-12 bg-[#252117] rounded-md border border-[#a6925e] overflow-hidden flex items-center justify-center shadow-sm">
                                            
                                            <?php if($potion && !empty($potion->icon_path)): ?>
                                                
                                                <img src="<?php echo e($potion->icon_path); ?>" alt="<?php echo e($potion->name); ?>"
                                                    class="w-10 h-10 object-contain" style="image-rendering: crisp-edges;">
                                            <?php else: ?>
                                                
                                                <img src="<?php echo e(asset('assets/potions/smallBottleHP.png')); ?>" alt="Зелье"
                                                    class="w-10 h-10 object-contain opacity-60">
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="text-sm font-semibold <?php echo e($qualityColor); ?> leading-tight mb-0.5">
                                            <?php echo e($potion->name); ?>

                                        </h4>
                                        <p class="text-xs text-[#c0b9a3] line-clamp-2 mb-1"><?php echo e($potion->description); ?></p>
                                        <div class="flex items-center justify-between">
                                            <span class="<?php echo e($qualityColor); ?> text-xs font-medium"><?php echo e($potion->quality); ?></span>
                                            <div class="flex items-center">
                                                <span class="text-sm font-mono text-[#d9d3b8]">
                                                    Использований: <?php echo e($potion->uses_left); ?>

                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                
                                <div class="flex items-center mb-2 bg-[#252117] rounded-md p-1.5 border border-[#514b3c]">
                                    <span class="text-xs text-[#9a9483] mr-1">Эффект:</span>
                                    <span class="text-xs text-[#e5b769] font-medium">
                                        <?php
                                            // Русский комментарий: Форматируем отображение эффекта зелья в понятном виде
                                            $effectValue = $potion->getActualEffectValue();
                                            $formattedEffect = match ($potion->effect) {
                                                'health' => "+{$effectValue} жизни",
                                                'mana' => "+{$effectValue} маны",
                                                'strength' => "+{$effectValue} к силе",
                                                'agility' => "+{$effectValue} к ловкости",
                                                'intelligence' => "+{$effectValue} к интеллекту",
                                                'stamina' => "+{$effectValue} к выносливости",
                                                'speed' => "+{$effectValue} к скорости",
                                                default => "{$potion->effect} ({$effectValue})"
                                            };
                                        ?>
                                        <?php echo e($formattedEffect); ?><?php echo e($potion->effect_duration > 0 ? ", {$potion->effect_duration} сек." : ""); ?>

                                    </span>
                                </div>

                                
                                <div class="flex justify-between items-center">
                                    
                                    <button type="button"
                                        class="use-potion-button py-1 px-3 mr-2 bg-gradient-to-b from-[#38352c] to-[#252117] text-[#e5b769] text-xs font-medium rounded shadow-md hover:from-[#443f33] hover:to-[#2e2921] transition-all duration-300 border border-[#514b3c] flex items-center justify-center flex-grow"
                                        data-potion-id="<?php echo e($potion->id); ?>">
                                        Использовать
                                    </button>

                                    
                                    <button type="button"
                                        class="add-to-belt-btn w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#3a4a3a] to-[#2c382c] text-green-300 text-xs font-medium rounded shadow-md hover:from-[#4a5e4a] hover:to-[#354a35] transition-all duration-300 border border-[#5ea65e] flex items-center justify-center mr-2"
                                        title="На пояс" data-potion-id="<?php echo e($potion->id); ?>" data-potion-name="<?php echo e($potion->name); ?>">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </button>

                                    
                                    <button type="button"
                                        class="potion-details-button py-1 px-2 mr-2 bg-gradient-to-b from-[#38352c] to-[#252117] text-[#e5b769] text-xs font-medium rounded shadow-md hover:from-[#443f33] hover:to-[#2e2921] transition-all duration-300 border border-[#514b3c] flex items-center justify-center"
                                        data-potion-id="<?php echo e($potion->id); ?>">
                                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Подробнее
                                    </button>

                                    
                                    <?php if(get_class($potion) === 'App\Models\UserPotion'): ?>
                                        <form action="<?php echo e(route('inventory.drop-user-potion', $potion->id)); ?>" method="POST">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit"
                                                class="w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#4a3a3a] to-[#382c2c] text-red-300 text-xs font-medium rounded shadow-md hover:from-[#5e4a4a] hover:to-[#4a3535] transition-all duration-300 border border-[#a65e5e] flex items-center justify-center"
                                                title="Выбросить"
                                                onclick="return confirm('Вы уверены, что хотите выбросить это зелье? Это действие нельзя отменить.')">
                                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                    </path>
                                                </svg>
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <form action="<?php echo e(route('inventory.drop-potion', $potion->id)); ?>" method="POST">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit"
                                                class="w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#4a3a3a] to-[#382c2c] text-red-300 text-xs font-medium rounded shadow-md hover:from-[#5e4a4a] hover:to-[#4a3535] transition-all duration-300 border border-[#a65e5e] flex items-center justify-center"
                                                title="Выбросить"
                                                onclick="return confirm('Вы уверены, что хотите выбросить это зелье? Это действие нельзя отменить.')">
                                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                    </path>
                                                </svg>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <?php if($potions->hasPages()): ?>
                    <div class="mt-4 flex justify-center">
                        <?php echo e($potions->appends(['tab' => 'potion'])->links()); ?>

                    </div>
                <?php endif; ?>

            <?php else: ?>
                <p class="text-gray-500 text-center">У вас нет зелий.</p>
            <?php endif; ?>
        <?php elseif(($tab ?? 'item') === 'belt'): ?>
            <?php if (isset($component)) { $__componentOriginale927bad327055b230176756ace17f795 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale927bad327055b230176756ace17f795 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.potion-belt','data' => ['potions' => $beltPotions ?? collect(),'hasBelt' => $hasBelt ?? false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('potion-belt'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['potions' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($beltPotions ?? collect()),'hasBelt' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBelt ?? false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale927bad327055b230176756ace17f795)): ?>
<?php $attributes = $__attributesOriginale927bad327055b230176756ace17f795; ?>
<?php unset($__attributesOriginale927bad327055b230176756ace17f795); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale927bad327055b230176756ace17f795)): ?>
<?php $component = $__componentOriginale927bad327055b230176756ace17f795; ?>
<?php unset($__componentOriginale927bad327055b230176756ace17f795); ?>
<?php endif; ?>

        <?php elseif(($tab ?? 'item') === 'recipe'): ?>
            
            <?php if(isset($recipes) && !$recipes->isEmpty()): ?>
                <h3 class="text-[#e5b769] text-lg font-bold text-center mb-2 border-t border-b border-[#a6925e] py-1 bg-[#2a2621]">
                    Ваши Рецепты</h3>
                <div class="space-y-4 max-w-md mx-auto">
                    <?php $__currentLoopData = $recipes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userRecipe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $recipe = $userRecipe->recipe; // Получаем связанный рецепт
                            $maxStackRecipe = App\Models\UserRecipe::MAX_STACK_SIZE; // Максимальный стак для рецептов
                            $isFullStack = $userRecipe->quantity >= $maxStackRecipe; // Проверка на полный стак
                            // Русский комментарий: Устанавливаем красный цвет только для полного стака
                            $qtyColorRecipe = $isFullStack ? 'text-red-500 font-bold' : 'text-[#d9d3b8]';
                            // Определение цвета качества рецепта
                            $qualityColors = [
                                'Обычное' => 'text-gray-200',
                                'Необычное' => 'text-green-400',
                                'Редкое' => 'text-blue-400',
                                'Эпическое' => 'text-purple-400',
                                'Легендарное' => 'text-orange-400',
                            ];
                            $qualityColor = $qualityColors[$recipe->quality] ?? 'text-gray-200';
                            $qualityBgColor = str_replace('text-', 'bg-', $qualityColor);
                            // Русский комментарий: Определяем класс для цвета свечения рецепта
                            $recipeGlowClass = str_replace('text-', 'glow-color-', $qualityColor);
                        ?>
                        
                        <div class="relative rounded-lg overflow-hidden shadow-lg" id="recipe-card-<?php echo e($userRecipe->id); ?>">
                            <div class="absolute inset-0 bg-gradient-to-br from-[#2a2621] to-[#1a1814] opacity-95"></div>
                            <div class="absolute inset-0 rounded-lg border border-[#a6925e] pointer-events-none"></div>
                            <div class="relative p-3 flex items-center space-x-3">
                                
                                <div class="relative flex-shrink-0 animate-breathing-glow <?php echo e($recipeGlowClass); ?> rounded-md">
                                    
                                    <div
                                        class="relative w-12 h-12 bg-[#252117] rounded-md border border-[#a6925e] overflow-hidden flex items-center justify-center shadow-sm">
                                        
                                        <?php if($recipe && !empty($recipe->icon_path)): ?>
                                            
                                            <img src="<?php echo e($recipe->icon_path); ?>" alt="<?php echo e($recipe->name); ?>" class="w-10 h-10 object-contain"
                                                style="image-rendering: crisp-edges;">
                                        <?php else: ?>
                                            
                                            <img src="<?php echo e(asset('assets/placeholder.png')); ?>" alt="Нет иконки"
                                                class="w-10 h-10 object-contain opacity-60">
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold <?php echo e($qualityColor); ?> leading-tight mb-0.5">
                                        <?php echo e($recipe->name); ?>

                                    </h4>
                                    <p class="text-xs text-[#c0b9a3] mb-1.5"><?php echo e($recipe->description); ?></p>
                                    <div class="flex items-center justify-between">
                                        <span class="<?php echo e($qualityColor); ?> text-xs font-medium"><?php echo e($recipe->quality); ?></span>
                                        <div class="flex items-center">
                                            <span id="recipe-qty-<?php echo e($userRecipe->id); ?>"
                                                class="text-sm font-mono <?php echo e($qtyColorRecipe); ?>"><?php echo e($userRecipe->quantity); ?>/<?php echo e($maxStackRecipe); ?></span>
                                            <?php if($isFullStack): ?>
                                                <span class="ml-1.5 text-red-500 text-xs font-bold" title="Полный стак">✓</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex space-x-2">
                                    
                                    <form action="<?php echo e(route('bank.move-to-bank')); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="recipe_id" value="<?php echo e($userRecipe->id); ?>">
                                        <input type="hidden" name="tab" value="recipes">
                                        <input type="hidden" name="quantity" value="<?php echo e($userRecipe->quantity); ?>">
                                        <button type="submit"
                                            class="w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#38352c] to-[#252117] text-[#e5b769] text-xs font-medium rounded shadow-md hover:from-[#443f33] hover:to-[#2e2921] transition-all duration-300 border border-[#514b3c] flex items-center justify-center"
                                            title="В банк">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4">
                                                </path>
                                            </svg>
                                        </button>
                                    </form>

                                    
                                    <button type="button"
                                        class="drop-button w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#4a3a3a] to-[#382c2c] text-red-300 text-xs font-medium rounded shadow-md hover:from-[#5e4a4a] hover:to-[#4a3535] transition-all duration-300 border border-[#a65e5e] flex items-center justify-center"
                                        data-item-id="<?php echo e($userRecipe->id); ?>" data-item-type="recipe"
                                        data-item-name="<?php echo e($recipe->name); ?>" data-item-max-quantity="<?php echo e($userRecipe->quantity); ?>">
                                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                            </path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <?php if($recipes->hasPages()): ?>
                    <div class="mt-4 flex justify-center">
                        <?php echo e($recipes->appends(['tab' => 'recipe'])->links()); ?>

                    </div>
                <?php endif; ?>

            <?php else: ?>
                <p class="text-gray-500 text-center">У вас нет рецептов.</p>
            <?php endif; ?>
        <?php endif; ?>

        
        <?php if(($tab ?? 'item') === 'harvest'): ?>
            
            <?php if(isset($harvests) && !$harvests->isEmpty()): ?>
                
                <h3 class="text-[#e5b769] text-lg font-bold text-center mb-2 border-t border-b border-[#a6925e] py-1 bg-[#2a2621]">
                    Ваш Урожай</h3>

                
                <div class="bg-[#2a2721] border border-[#514b3c] rounded-md p-2 mb-3">
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-[#9a9483]">Секции склада:</span>
                            <span
                                class="text-[#d9d3b8]"><?php echo e(isset($farmStorage) ? $farmStorage->getUsedSectionsCount() : 0); ?>/<?php echo e(isset($farmStorage) ? $farmStorage->max_sections : 3); ?></span>
                        </div>
                        <div>
                            <span class="text-[#9a9483]">Вместимость секции:</span>
                            <span
                                class="text-[#d9d3b8]"><?php echo e(isset($farmStorage) ? $farmStorage->max_capacity_per_section : 15); ?></span>
                        </div>
                    </div>
                </div>

                
                <div class="mb-3">
                    <a href="<?php echo e(route('farming.storage')); ?>"
                        class="block w-full bg-gradient-to-r from-[#3b3a33] to-[#2c2a24] p-2 rounded-md border border-[#514b3c] hover:border-[#a6925e] transition-all duration-300 text-center">
                        <div class="text-[#e5b769] text-sm font-medium">Перейти на склад урожая</div>
                        <div class="text-[#9a9483] text-xs">Для хранения и продажи урожая</div>
                    </a>
                </div>

                
                <div class="space-y-3 max-w-md mx-auto">
                    <?php $__currentLoopData = $harvests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $harvest): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            // Определяем цвет редкости урожая
                            $rarityClass = $harvest->getRarityColorClass();

                            // Определяем максимальный стак для урожая
                            $maxStack = 20; // Максимальный стак для урожая
                            $isFullStack = $harvest->quantity >= $maxStack;
                            $qtyColor = $isFullStack ? 'text-red-500' : 'text-[#d9d3b8]';
                        ?>

                        
                        <div class="bg-[#252117] border border-[#514b3c] rounded-md p-2 flex items-center justify-between"
                            id="harvest-card-<?php echo e($harvest->id); ?>">
                            
                            <div class="flex items-center">
                                <div
                                    class="w-12 h-12 flex items-center justify-center bg-[#1a1814] rounded-md border border-[#514b3c] mr-3 overflow-hidden">
                                    <img src="<?php echo e(asset($harvest->getHarvestImagePath())); ?>" alt="<?php echo e($harvest->getHarvestName()); ?>"
                                        class="w-10 h-10 object-contain">
                                </div>
                                <div>
                                    <div class="flex items-center">
                                        <span class="<?php echo e($rarityClass); ?> text-sm font-medium"><?php echo e($harvest->getHarvestName()); ?></span>
                                    </div>
                                    <div class="flex flex-col">
                                        <div class="flex items-center space-x-1">
                                            <span class="text-xs text-[#9a9483]">Цена за 1 ед.:</span>
                                            <?php echo $harvest->getFormattedUnitPrice(); ?>

                                        </div>
                                        <div class="flex items-center space-x-1">
                                            <span class="text-xs text-[#9a9483]">Всего:</span>
                                            <?php echo $harvest->getFormattedTotalPrice(); ?>

                                        </div>
                                    </div>
                                    <div class="flex items-center mt-1">
                                        <span id="harvest-qty-<?php echo e($harvest->id); ?>"
                                            class="text-sm font-mono <?php echo e($qtyColor); ?>"><?php echo e($harvest->quantity); ?>/<?php echo e($maxStack); ?></span>
                                        <?php if($isFullStack): ?>
                                            <span class="ml-1.5 text-red-500 text-xs font-bold" title="Полный стак">✓</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            
                            <div class="flex space-x-2">
                                
                                <form action="<?php echo e(route('farming.storage.move-to-storage')); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="harvest_id" value="<?php echo e($harvest->id); ?>">
                                    <button type="submit"
                                        class="w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#514b3c] to-[#38352c] text-[#d9d3b8] text-xs font-medium rounded shadow-md hover:from-[#5d5745] hover:to-[#433f35] transition-all duration-300 border border-[#514b3c] flex items-center justify-center"
                                        title="Переместить на склад">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                            stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                        </svg>
                                    </button>
                                </form>

                                
                                <form action="<?php echo e(route('bank.move-to-bank')); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="harvest_id" value="<?php echo e($harvest->id); ?>">
                                    <input type="hidden" name="tab" value="harvests">
                                    <input type="hidden" name="quantity" value="<?php echo e($harvest->quantity); ?>">
                                    <button type="submit"
                                        class="w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#38352c] to-[#252117] text-[#e5b769] text-xs font-medium rounded shadow-md hover:from-[#443f33] hover:to-[#2e2921] transition-all duration-300 border border-[#514b3c] flex items-center justify-center"
                                        title="В банк">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4">
                                            </path>
                                        </svg>
                                    </button>
                                </form>

                                
                                <button type="button"
                                    class="drop-button w-8 h-8 flex-shrink-0 bg-gradient-to-b from-[#4a3a3a] to-[#382c2c] text-red-300 text-xs font-medium rounded shadow-md hover:from-[#5e4a4a] hover:to-[#4a3535] transition-all duration-300 border border-[#a65e5e] flex items-center justify-center"
                                    data-item-id="<?php echo e($harvest->id); ?>" data-item-type="harvest"
                                    data-item-name="<?php echo e($harvest->getHarvestName()); ?>" data-item-max-quantity="<?php echo e($harvest->quantity); ?>">
                                    <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <?php if($harvests->hasPages()): ?>
                    <div class="mt-4 flex justify-center">
                        <?php echo e($harvests->appends(['tab' => 'harvest'])->links()); ?>

                    </div>
                <?php endif; ?>
            <?php else: ?>
                <p class="text-gray-500 text-center">У вас нет урожая.</p>
            <?php endif; ?>
        <?php endif; ?>

        
        <?php
            $noItems = !isset($inventory) || $inventory->isEmpty();
            $noResources = !isset($resources) || $resources->isEmpty();
            $noIngredients = !isset($alchemyIngredients) || $alchemyIngredients->isEmpty();
            $noRecipes = !isset($recipes) || $recipes->isEmpty();
            $noHarvests = !isset($harvests) || $harvests->isEmpty();
        ?>
        <?php if($noItems && $noResources && $noIngredients && $noRecipes && $noHarvests): ?>
            <p class="text-gray-500 text-center mt-4">Ваш рюкзак пуст.</p>
        <?php endif; ?>
    </div>
    </div>




    </div> 

    

    
    <div id="usePotionModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center hidden p-4">
        <div
            class="bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-xl p-6 max-w-md mx-auto relative transform transition-all">
            
            <button type="button" id="closeUsePotionButton"
                class="absolute top-2 right-2 text-[#a6925e] hover:text-[#e5b769] transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>

            
            <h3 class="text-xl font-bold text-[#e5b769] mb-4 text-center border-b border-[#514b3c] pb-2">
                Использовать зелье
            </h3>

            
            <div class="mb-6">
                <p class="text-[#d9d3b8] text-center mb-4">Вы уверены, что хотите использовать это зелье?</p>
                <div id="potionInfoContainer" class="flex items-center justify-center mb-4">
                    <div
                        class="w-16 h-16 bg-[#252117] rounded-md border border-[#a6925e] overflow-hidden flex items-center justify-center mr-4">
                        <img id="usePotionImage" src="" alt="Зелье" class="w-12 h-12 object-contain">
                    </div>
                    <div>
                        <h4 id="usePotionName" class="text-[#e5b769] font-medium mb-1">Название зелья</h4>
                        <p id="usePotionEffect" class="text-[#c0b9a3] text-sm">Эффект зелья</p>
                    </div>
                </div>
            </div>

            
            <div class="flex justify-center space-x-4">
                <button type="button" id="cancelUsePotionButton"
                    class="px-4 py-2 bg-gradient-to-b from-[#38352c] to-[#252117] text-[#d9d3b8] rounded border border-[#514b3c] hover:from-[#443f33] hover:to-[#2e2921] transition-all">
                    Отмена
                </button>
                <form id="usePotionForm" method="POST">
                    <?php echo csrf_field(); ?>
                    <button type="submit"
                        class="px-4 py-2 bg-gradient-to-b from-[#514b3c] to-[#38352c] text-[#e5b769] rounded border border-[#a6925e] hover:from-[#5d5745] hover:to-[#433f35] transition-all">
                        Использовать
                    </button>
                </form>
            </div>
        </div>
    </div>

    
    <div id="itemInfoModal" class="fixed inset-0 z-50 hidden" aria-labelledby="itemInfoModal-title" role="dialog"
        aria-modal="true">
        
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="fixed inset-0 bg-black bg-opacity-75 transition-opacity duration-300 ease-out opacity-100"
                id="itemInfoModal-backdrop" style="backdrop-filter: blur(3px);"></div>

            
            <div class="relative bg-[#252117] rounded-lg border border-[#514b3c] text-left overflow-hidden shadow-xl transform transition-all duration-300 ease-out scale-100 opacity-100 w-full max-w-lg flex flex-col"
                id="itemInfoModal-content" style="max-height: 90vh;">

                
                <div class="bg-[#2a2721] px-4 py-3 border-b border-[#514b3c] flex-shrink-0">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                
                                <div
                                    class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-[#3d3a2e] border border-[#514b3c]">
                                    <svg class="h-6 w-6 text-[#a6925e]" fill="none" viewBox="0 0 24 24"
                                        stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg leading-6 font-medium text-[#d9d3b8]" id="itemInfoModal-title">
                                    <span id="itemInfoTitle">Информация о предмете</span>
                                </h3>
                            </div>
                        </div>

                        
                        <button type="button" id="closeItemInfoButton"
                            class="text-[#9a9483] hover:text-[#d9d3b8] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#a6925e] focus:ring-offset-2 focus:ring-offset-[#2a2721] rounded-md p-1">
                            <span class="sr-only">Закрыть</span>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                
                <div class="bg-[#252117] flex-1 overflow-hidden">
                    <div id="itemInfoContent" class="text-[#b0a890] overflow-y-auto px-4 py-3 custom-modal-scrollbar"
                        style="max-height: calc(90vh - 140px); min-height: 300px;">
                        
                        <div class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#a6925e]"></div>
                            <div class="mt-2 text-sm text-[#9a9483]">Загрузка информации о предмете...</div>
                        </div>
                    </div>
                </div>

                
                <div class="bg-[#2a2721] px-4 py-3 border-t border-[#514b3c] flex justify-end flex-shrink-0">
                    <button type="button" id="closeItemInfoButtonBottom"
                        class="inline-flex justify-center px-4 py-2 text-sm font-medium text-[#d9d3b8] bg-[#3d3a2e] border border-[#514b3c] rounded-md hover:bg-[#4a452c] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#a6925e] focus:ring-offset-[#2a2721] transition-colors duration-200">
                        Закрыть
                    </button>
                </div>
            </div>
        </div>
    </div>

    
    
    <div id="dropModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div
            class="bg-gradient-to-b from-[#2a2621] to-[#1a1814] border border-[#a6925e] rounded-lg p-4 max-w-sm w-full mx-4 shadow-lg">
            
            <h3 id="dropModalTitle"
                class="text-[#e5b769] text-lg font-bold mb-2 text-center border-b border-[#514b3c] pb-2"></h3>

            
            <div class="flex justify-center mb-3">
                <div
                    class="relative w-16 h-16 bg-[#252117] rounded-md border border-[#a6925e] overflow-hidden flex items-center justify-center shadow-md">
                    <div class="absolute inset-0 bg-gradient-to-br from-[#3b3a33]/30 to-[#252117]/50 blur-[1px]"></div>
                    <img id="dropItemIcon" src="" alt="Предмет" class="relative z-10 w-14 h-14 object-contain"
                        style="image-rendering: crisp-edges;">
                </div>
            </div>

            
            <p id="dropModalMessage" class="text-[#d9d3b8] text-sm mb-4 text-center"></p>

            
            <div id="dropQuantityContainer" class="mb-4 hidden">
                <div class="flex items-center justify-between mb-2">
                    <label for="dropQuantityInput" class="text-[#c0b9a3] text-sm">Количество:</label>
                    <span class="text-[#c0b9a3] text-sm">
                        <span id="dropCurrentQuantityValue">1</span> / <span id="dropMaxQuantityValue">0</span>
                    </span>
                </div>
                <div class="flex items-center space-x-2">
                    <input type="range" id="dropQuantityInput" min="1" max="20" value="1"
                        class="w-full h-2 bg-[#38352c] rounded-lg appearance-none cursor-pointer">
                    <div class="flex items-center space-x-1">
                        <button type="button" id="decrementQuantity"
                            class="w-6 h-6 flex items-center justify-center bg-[#38352c] text-[#c0b9a3] rounded border border-[#514b3c] hover:bg-[#443f33] transition-all">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4">
                                </path>
                            </svg>
                        </button>
                        <input type="number" id="dropQuantityNumberInput" min="1" max="20" value="1"
                            class="w-12 h-6 bg-[#252117] border border-[#514b3c] rounded text-center text-[#d9d3b8] text-sm">
                        <button type="button" id="incrementQuantity"
                            class="w-6 h-6 flex items-center justify-center bg-[#38352c] text-[#c0b9a3] rounded border border-[#514b3c] hover:bg-[#443f33] transition-all">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            
            <form id="dropItemForm" method="POST">
                <?php echo csrf_field(); ?>
                
                <input type="hidden" id="dropFormMethodInput" name="_method" value="DELETE">
                <input type="hidden" id="dropFormQuantityInput" name="quantity" value="1">

                
                <div class="flex justify-center space-x-4">
                    <button type="button" id="cancelDropButton"
                        class="bg-gradient-to-b from-[#38352c] to-[#252117] text-[#c0b9a3] py-2 px-4 rounded shadow-md hover:from-[#443f33] hover:to-[#2e2921] transition-all duration-300 border border-[#514b3c]">
                        Отмена
                    </button>
                    <button type="submit" id="confirmDropButton"
                        class="bg-gradient-to-b from-[#4a3a3a] to-[#382c2c] text-red-300 py-2 px-4 rounded shadow-md hover:from-[#5e4a4a] hover:to-[#4a3535] transition-all duration-300 border border-[#a65e5e]">
                        Выбросить
                    </button>
                </div>
            </form>
        </div>
    </div>

    
    <style>
        /* Стили для кастомного скроллбара модального окна */
        .custom-modal-scrollbar::-webkit-scrollbar {
            width: 8px;
            background: #232218;
            border-radius: 6px;
        }

        .custom-modal-scrollbar::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #a6925e 0%, #e5b769 100%);
            border-radius: 6px;
            border: 2px solid #232218;
        }

        .custom-modal-scrollbar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #e5b769 0%, #a6925e 100%);
        }

        /* Для Firefox */
        .custom-modal-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #e5b769 #232218;
        }

        /* Адаптивные стили для мобильных устройств */
        @media (max-width: 640px) {
            #itemInfoModal-content {
                max-height: 95vh !important;
                max-height: 95dvh !important;
                /* Динамическая высота viewport для мобильных */
                margin: 0.5rem !important;
                max-width: calc(100vw - 1rem) !important;
            }

            #itemInfoContent {
                max-height: calc(95vh - 160px) !important;
                max-height: calc(95dvh - 160px) !important;
                min-height: 250px !important;
            }

            .custom-modal-scrollbar {
                padding-left: 1rem !important;
                padding-right: 1rem !important;
            }

            /* Увеличиваем размер скроллбара на мобильных для лучшего touch-взаимодействия */
            .custom-modal-scrollbar::-webkit-scrollbar {
                width: 12px !important;
            }
        }

        /* Для планшетов */
        @media (max-width: 768px) and (min-width: 641px) {
            #itemInfoModal-content {
                max-height: 92vh !important;
                max-height: 92dvh !important;
                max-width: calc(100vw - 2rem) !important;
            }

            #itemInfoContent {
                max-height: calc(92vh - 150px) !important;
                max-height: calc(92dvh - 150px) !important;
                min-height: 280px !important;
            }
        }

        /* Для десктопа - обеспечиваем видимость скроллбара */
        @media (min-width: 769px) {
            #itemInfoContent {
                max-height: calc(90vh - 140px) !important;
                min-height: 300px !important;
            }
        }

        /* Улучшенная прокрутка на touch устройствах */
        .custom-modal-scrollbar {
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
            scroll-behavior: smooth;
        }

        /* Предотвращение масштабирования на iOS при фокусе */
        @media (max-width: 640px) {

            input,
            select,
            textarea {
                font-size: 16px !important;
            }
        }

        /* Улучшение видимости кнопки закрытия на мобильных */
        @media (max-width: 640px) {

            #closeItemInfoButton,
            #closeItemInfoButtonBottom {
                min-height: 44px;
                min-width: 44px;
                padding: 0.75rem !important;
            }
        }
    </style>

    <script>
        // Функция для отображения информации о предмете с улучшенной анимацией
        function showItemInfo(itemId) {
            // Получаем CSRF-токен
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Показываем модальное окно с индикатором загрузки
            const modal = document.getElementById('itemInfoModal');
            const content = document.getElementById('itemInfoContent');
            const backdrop = document.getElementById('itemInfoModal-backdrop');
            const modalContent = document.getElementById('itemInfoModal-content');

            // Отображаем модальное окно с анимацией
            modal.classList.remove('hidden');
            animateModalShow(backdrop, modalContent);

            // Загружаем данные о предмете через AJAX
            fetch(`/inventory/item-info/${itemId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                }
            })
                .then(response => {
                    // Проверяем, что ответ успешный
                    if (!response.ok) {
                        throw new Error('Ошибка сети: ' + response.status);
                    }
                    // Проверяем, что ответ в формате JSON
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Получен неверный формат данных');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Обновляем заголовок модального окна
                        document.getElementById('itemInfoTitle').textContent = data.item.name;

                        // Заполняем контент модального окна
                        content.innerHTML = data.html;
                    } else {
                        // Показываем сообщение об ошибке
                        content.innerHTML = `<div class="text-center py-4">
                                                                                                                                                        <div class="text-red-500 text-lg mb-2">Произошла ошибка при загрузке информации о предмете.</div>
                                                                                                                                                        <div class="text-[#d9d3b8] text-sm">${data.message || 'Не удалось загрузить данные.'}</div>
                                                                                                                                                        <button onclick="document.getElementById('itemInfoModal').classList.add('hidden')" class="mt-4 px-4 py-2 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#4a452c]">
                                                                                                                                                            Закрыть
                                                                                                                                                        </button>
                                                                                                                                                                       </div>                   `;
                    }
                })
                .catch(error => {
                    console.error('Ошибка при загрузке информации о предмете:', error);
                    content.innerHTML = `<div class="text-center py-4">
                                                                                                                                                    <div class="text-red-500 text-lg mb-2">Произошла ошибка при загрузке информации о предмете.</div>
                                                                                                                                                    <div class="text-[#d9d3b8] text-sm">${error.message}</div>
                                                                                                                                                    <button onclick="document.getElementById('itemInfoModal').classList.add('hidden')" class="mt-4 px-4 py-2 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#4a452c]">
                                                                                                                                                        Закрыть
                                                                                                                                                    </button>
                                                                                                                                                </div>`;
                });
        }

        // Обработчики событий для закрытия модального окна с информацией о предмете
        document.getElementById('closeItemInfoButton').addEventListener('click', () => {
            closeItemInfoModal();
        });

        document.getElementById('closeItemInfoButtonBottom').addEventListener('click', () => {
            closeItemInfoModal();
        });

        // Закрытие по клику на фон
        document.getElementById('itemInfoModal-backdrop').addEventListener('click', () => {
            closeItemInfoModal();
        });

        // Закрытие по Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !document.getElementById('itemInfoModal').classList.contains('hidden')) {
                closeItemInfoModal();
            }
        });

        // Улучшение работы с прокруткой на мобильных устройствах
        function initMobileScrollFix() {
            const modal = document.getElementById('itemInfoModal');
            const content = document.getElementById('itemInfoContent');

            if (!modal || !content) return;

            // Предотвращаем прокрутку фона при открытом модальном окне
            modal.addEventListener('touchmove', function (e) {
                if (e.target === modal || e.target === document.getElementById('itemInfoModal-backdrop')) {
                    e.preventDefault();
                }
            }, { passive: false });

            // Обеспечиваем правильную работу прокрутки внутри контента
            content.addEventListener('touchstart', function (e) {
                // Сохраняем начальную позицию прокрутки
                this.startScrollTop = this.scrollTop;
            });

            content.addEventListener('touchmove', function (e) {
                // Разрешаем прокрутку только внутри контента
                e.stopPropagation();
            });
        }

        // Инициализируем улучшения для мобильных устройств
        document.addEventListener('DOMContentLoaded', initMobileScrollFix);

        // Функция для анимации появления модального окна
        function animateModalShow(backdrop, content) {
            if (backdrop && content) {
                // Начальное состояние
                backdrop.style.opacity = '0';
                content.style.transform = 'scale(0.95)';
                content.style.opacity = '0';

                // Анимация появления
                requestAnimationFrame(() => {
                    backdrop.style.transition = 'opacity 300ms ease-out';
                    content.style.transition = 'all 300ms ease-out';

                    backdrop.style.opacity = '1';
                    content.style.transform = 'scale(1)';
                    content.style.opacity = '1';
                });
            }
        }

        // Функция для закрытия модального окна с анимацией
        function closeItemInfoModal() {
            const modal = document.getElementById('itemInfoModal');
            const backdrop = document.getElementById('itemInfoModal-backdrop');
            const content = document.getElementById('itemInfoModal-content');

            // Анимация закрытия
            backdrop.style.opacity = '0';
            content.style.transform = 'scale(0.95)';
            content.style.opacity = '0';

            // Скрываем модальное окно после анимации
            setTimeout(() => {
                modal.classList.add('hidden');
                // Восстанавливаем стили для следующего открытия
                backdrop.style.opacity = '1';
                content.style.transform = 'scale(1)';
                content.style.opacity = '1';
                backdrop.style.transition = '';
                content.style.transition = '';
            }, 300);
        }

        // Функционал для модального окна использования зелья
        document.addEventListener('DOMContentLoaded', function () {
            // Находим все кнопки "Использовать" для зелий
            const usePotionButtons = document.querySelectorAll('.use-potion-button');
            const usePotionModal = document.getElementById('usePotionModal');
            const usePotionForm = document.getElementById('usePotionForm');
            const closeUsePotionButton = document.getElementById('closeUsePotionButton');
            const cancelUsePotionButton = document.getElementById('cancelUsePotionButton');

            // Добавляем обработчики для всех кнопок "Использовать"
            usePotionButtons.forEach(button => {
                button.addEventListener('click', function () {
                    const potionId = this.getAttribute('data-potion-id');
                    const potionCard = this.closest('.relative'); // Находим карточку зелья

                    // Получаем информацию о зелье из карточки
                    const potionName = potionCard.querySelector('h4').textContent.trim();
                    // Используем более надежный селектор для эффекта зелья
                    const potionEffect = potionCard.querySelector('.flex.items-center.mb-2 > span').textContent.trim();
                    const potionImage = potionCard.querySelector('img').getAttribute('src');

                    // Заполняем модальное окно информацией о зелье
                    document.getElementById('usePotionName').textContent = potionName;
                    document.getElementById('usePotionEffect').textContent = potionEffect;
                    document.getElementById('usePotionImage').setAttribute('src', potionImage);

                    // Устанавливаем URL для формы
                    usePotionForm.action = `/inventory/use-potion/${potionId}`;

                    // Показываем модальное окно
                    usePotionModal.classList.remove('hidden');
                });
            });

            // Обработчики для закрытия модального окна
            if (closeUsePotionButton) {
                closeUsePotionButton.addEventListener('click', () => {
                    usePotionModal.classList.add('hidden');
                });
            }

            if (cancelUsePotionButton) {
                cancelUsePotionButton.addEventListener('click', () => {
                    usePotionModal.classList.add('hidden');
                });
            }

            // Закрытие по клику вне окна
            usePotionModal.addEventListener('click', (e) => {
                if (e.target === usePotionModal) {
                    usePotionModal.classList.add('hidden');
                }
            });
        });

        // --- КОД ДЛЯ МОДАЛЬНОГО ОКНА ВЫБРАСЫВАНИЯ ПРЕДМЕТОВ ---
        document.addEventListener('DOMContentLoaded', function () {
            // Получаем элементы модального окна
            const dropModal = document.getElementById('dropModal');
            const dropModalTitle = document.getElementById('dropModalTitle');
            const dropModalMessage = document.getElementById('dropModalMessage');
            const dropQuantityContainer = document.getElementById('dropQuantityContainer');
            const dropQuantityInput = document.getElementById('dropQuantityInput');
            const dropCurrentQuantityValue = document.getElementById('dropCurrentQuantityValue');
            const dropMaxQuantityValue = document.getElementById('dropMaxQuantityValue');
            const dropItemForm = document.getElementById('dropItemForm');
            const dropFormMethodInput = document.getElementById('dropFormMethodInput');
            const dropFormQuantityInput = document.getElementById('dropFormQuantityInput');
            const cancelDropButton = document.getElementById('cancelDropButton');
            const confirmDropButton = document.getElementById('confirmDropButton');
            const dropButtons = document.querySelectorAll('.drop-button');

            let currentItemId = null;
            let currentItemType = null;

            // Функция для открытия модального окна
            function openDropModal(itemId, itemType, itemName, maxQuantity, itemIcon) {
                currentItemId = itemId;
                currentItemType = itemType;

                // Устанавливаем иконку предмета
                const dropItemIconElement = document.getElementById('dropItemIcon');
                if (dropItemIconElement && itemIcon) {
                    dropItemIconElement.src = itemIcon;
                    dropItemIconElement.alt = itemName;
                }

                dropModalTitle.textContent = `Выбросить ${itemName}?`;
                dropItemForm.action = '#'; // Сбрасываем action
                dropFormMethodInput.value = 'POST'; // По умолчанию POST

                if (itemType === 'item') {
                    // Обычный предмет - простое подтверждение
                    dropModalMessage.textContent = 'Вы уверены, что хотите выбросить этот предмет? Это действие необратимо.';
                    dropQuantityContainer.classList.add('hidden');

                    // Устанавливаем URL с использованием route() для предотвращения ошибок
                    const itemUrl = '<?php echo e(route('inventory.drop-item', '')); ?>/' + itemId;
                    console.log('URL для выбрасывания предмета:', itemUrl);
                    dropItemForm.action = itemUrl;

                    dropFormMethodInput.value = 'DELETE'; // Метод DELETE для dropItem
                    dropFormQuantityInput.value = 1; // Для формы, не используется контроллером
                } else {
                    // Стакающийся предмет - ввод количества
                    dropModalMessage.textContent = `Сколько "${itemName}" вы хотите выбросить?`;
                    dropMaxQuantityValue.textContent = maxQuantity;
                    dropQuantityContainer.classList.remove('hidden');

                    // Используем новую функцию для обновления элементов управления количеством
                    if (window.updateDropQuantityControls) {
                        window.updateDropQuantityControls(maxQuantity);
                    }

                    // Устанавливаем URL и метод в зависимости от типа
                    if (itemType === 'resource') {
                        const resourceUrl = '<?php echo e(route('inventory.drop-resource', '')); ?>/' + itemId;
                        console.log('URL для выбрасывания ресурса:', resourceUrl);
                        dropItemForm.action = resourceUrl;
                        dropFormMethodInput.value = 'DELETE'; // Устанавливаем метод DELETE для ресурсов
                    } else if (itemType === 'ingredient') {
                        const ingredientUrl = '<?php echo e(route('inventory.drop-ingredient', '')); ?>/' + itemId;
                        console.log('URL для выбрасывания ингредиента:', ingredientUrl);
                        dropItemForm.action = ingredientUrl;
                        dropFormMethodInput.value = 'POST'; // Метод POST для ингредиентов
                    } else if (itemType === 'recipe') {
                        // Используем абсолютный URL без конкатенации
                        const recipeUrl = '/inventory/drop-recipe/' + itemId;
                        console.log('URL для выбрасывания рецепта:', recipeUrl);
                        dropItemForm.action = recipeUrl;
                        dropFormMethodInput.value = 'POST'; // Метод POST для рецептов
                    } else if (itemType === 'harvest') {
                        const harvestUrl = '<?php echo e(route('inventory.drop-harvest', '')); ?>/' + itemId;
                        console.log('URL для выбрасывания урожая:', harvestUrl);
                        dropItemForm.action = harvestUrl;
                        dropFormMethodInput.value = 'POST'; // Метод POST для урожая
                    }
                }

                dropModal.classList.remove('hidden');
            }

            // Функция для закрытия модального окна
            function closeDropModal() {
                dropModal.classList.add('hidden');
            }

            // Обновление скрытого поля количества при изменении ползунка
            if (dropQuantityInput) {
                dropQuantityInput.addEventListener('input', function () {
                    dropFormQuantityInput.value = this.value;
                    dropCurrentQuantityValue.textContent = this.value;
                });
            }

            // Слушатели событий для кнопок выбрасывания
            dropButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const itemId = button.dataset.itemId;
                    const itemType = button.dataset.itemType;
                    const itemName = button.dataset.itemName;
                    const maxQuantity = button.dataset.itemMaxQuantity; // Будет undefined для item
                    const itemIcon = button.dataset.itemIcon || button.closest('.relative')?.querySelector('img')?.src || ''; // Получаем иконку предмета
                    openDropModal(itemId, itemType, itemName, maxQuantity, itemIcon);
                });
            });

            // Кнопка отмены
            if (cancelDropButton) {
                cancelDropButton.addEventListener('click', closeDropModal);
            }

            // Кнопка подтверждения (дополнительный обработчик для надежности)
            if (confirmDropButton) {
                confirmDropButton.addEventListener('click', function () {
                    // Обновляем количество перед отправкой
                    if (currentItemType !== 'item' && dropQuantityInput) {
                        dropFormQuantityInput.value = dropQuantityInput.value;
                    }

                    // Отправляем форму
                    dropItemForm.submit();
                });
            }

            // Закрытие по клику вне окна
            if (dropModal) {
                dropModal.addEventListener('click', (e) => {
                    if (e.target === dropModal) {
                        closeDropModal();
                    }
                });
            }

            // Обработка отправки формы (БЕЗ AJAX - обычная отправка формы)
            if (dropItemForm) {
                dropItemForm.addEventListener('submit', function (e) {
                    // Обновляем количество перед отправкой
                    if (currentItemType !== 'item' && dropQuantityInput) {
                        const quantity = parseInt(dropQuantityInput.value, 10);
                        const max = parseInt(dropQuantityInput.max, 10);

                        // Проверка на корректность количества
                        if (isNaN(quantity) || quantity < 1) {
                            e.preventDefault();
                            alert('Пожалуйста, укажите корректное количество (минимум 1)');
                            return;
                        }

                        // Проверка на максимальное количество
                        if (quantity > max) {
                            e.preventDefault();
                            alert(`Нельзя выбросить больше, чем у вас есть (максимум ${max})`);
                            return;
                        }

                        // Устанавливаем проверенное значение
                        dropFormQuantityInput.value = quantity;
                    }

                    // Проверяем, что URL установлен
                    if (!this.action || this.action === '#' || this.action === window.location.href) {
                        e.preventDefault();
                        alert('Ошибка: URL для отправки формы не установлен');
                        return;
                    }

                    // Логируем информацию о запросе для отладки
                    console.log('Отправка формы:', {
                        url: this.action,
                        method: dropFormMethodInput.value,
                        itemId: currentItemId,
                        itemType: currentItemType,
                        quantity: dropFormQuantityInput.value
                    });

                    // Закрываем модальное окно перед отправкой
                    closeDropModal();

                    // Форма отправится обычным способом (без AJAX)
                });
            }
        });

        // Обработка элементов управления количеством
        if (dropQuantityInput) {
            const dropQuantityNumberInput = document.getElementById('dropQuantityNumberInput');
            const decrementQuantityBtn = document.getElementById('decrementQuantity');
            const incrementQuantityBtn = document.getElementById('incrementQuantity');

            // Функция для синхронизации значений
            function syncQuantityValues(value, source) {
                const max = parseInt(dropQuantityInput.max, 10);

                // Проверка и коррекция значения
                let validValue = parseInt(value, 10);
                if (isNaN(validValue) || validValue < 1) {
                    validValue = 1;
                } else if (validValue > max) {
                    validValue = max;
                }

                // Обновляем все элементы, кроме источника изменения
                if (source !== 'range') dropQuantityInput.value = validValue;
                if (source !== 'number') dropQuantityNumberInput.value = validValue;

                // Обновляем отображаемое значение и скрытое поле формы
                document.getElementById('dropCurrentQuantityValue').textContent = validValue;
                document.getElementById('dropFormQuantityInput').value = validValue;

                return validValue;
            }

            // Обработчик изменения ползунка
            dropQuantityInput.addEventListener('input', () => {
                syncQuantityValues(dropQuantityInput.value, 'range');
            });

            // Обработчик изменения числового поля
            dropQuantityNumberInput.addEventListener('input', () => {
                syncQuantityValues(dropQuantityNumberInput.value, 'number');
            });

            // Обработчик кнопки уменьшения
            decrementQuantityBtn.addEventListener('click', () => {
                const currentValue = parseInt(dropQuantityNumberInput.value, 10);
                const newValue = Math.max(1, currentValue - 1);
                syncQuantityValues(newValue, 'button');
            });

            // Обработчик кнопки увеличения
            incrementQuantityBtn.addEventListener('click', () => {
                const currentValue = parseInt(dropQuantityNumberInput.value, 10);
                const max = parseInt(dropQuantityInput.max, 10);
                const newValue = Math.min(max, currentValue + 1);
                syncQuantityValues(newValue, 'button');
            });

            // Синхронизация при открытии модального окна
            function updateQuantityControls(maxValue) {
                dropQuantityInput.max = maxValue;
                dropQuantityNumberInput.max = maxValue;
                syncQuantityValues(1, null);
            }

            // Добавляем функцию в глобальную область видимости для доступа из openDropModal
            window.updateDropQuantityControls = updateQuantityControls;
        }
        // --- КОНЕЦКОДА МОДАЛЬНОГО ОКНА ---

    </script>

    
    <script>
        // Русский комментарий: Обработчик для кнопок "Подробнее" у зелий
        document.addEventListener('DOMContentLoaded', function () {
            // Находим все кнопки "Подробнее" для зелий
            const potionDetailsButtons = document.querySelectorAll('.potion-details-button');
            const itemInfoModal = document.getElementById('itemInfoModal');
            const itemInfoTitle = document.getElementById('itemInfoTitle');
            const itemInfoContent = document.getElementById('itemInfoContent');
            const closeButtons = [
                document.getElementById('closeItemInfoButton'),
                document.getElementById('closeItemInfoButtonBottom')
            ];

            // Добавляем обработчики для всех кнопок "Подробнее"
            potionDetailsButtons.forEach(button => {
                button.addEventListener('click', function () {
                    const potionId = this.getAttribute('data-potion-id');

                    // Показываем модальное окно с загрузкой
                    itemInfoTitle.textContent = 'Информация о зелье';
                    itemInfoContent.innerHTML = `
                                                                                                                                                    <div class="flex justify-center items-center py-8">
                                                                                                                                                        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#e5b769]"></div>
                                                                                                                                                        <span class="ml-2 text-[#d9d3b8]">Загрузка...</span>
                                                                                                                                                    </div>
                                                                                                                                                `;
                    itemInfoModal.classList.remove('hidden');

                    // Находим данные о зелье прямо из карточки
                    const potionCard = document.getElementById('potion-card-' + potionId);
                    if (potionCard) {
                        // Получаем название зелья
                        const potionName = potionCard.querySelector('h4').textContent.trim();

                        // Получаем описание зелья
                        const potionDescription = potionCard.querySelector('p').textContent.trim();

                        // Получаем качество зелья
                        const potionQuality = potionCard.querySelector('.flex-1 > div > span:first-child').textContent.trim();

                        // Получаем количество использований
                        const potionUses = potionCard.querySelector('.flex-1 > div > div > span').textContent.trim();

                        // Получаем эффект зелья (используем более надежный селектор)
                        const potionEffect = potionCard.querySelector('.flex.items-center.mb-2 > span:last-child').textContent.trim();

                        // Получаем изображение зелья
                        const potionImage = potionCard.querySelector('img').getAttribute('src');

                        // Извлекаем тип эффекта и значение из текста эффекта
                        let effectType = '';
                        let effectValue = '';
                        let effectDuration = '';

                        // Парсим эффект для получения деталей
                        console.log('Эффект зелья:', potionEffect); // Для отладки

                        // Пробуем разные форматы регулярных выражений для совместимости

                        // Формат с длительностью: "+100 жизни, 30 сек."
                        let effectMatch = potionEffect.match(/\+(\d+)\s+(жизни|маны|к силе|к ловкости|к интеллекту|к выносливости|к скорости)(?:,\s+(\d+)\s+сек\.)?/);

                        // Формат без длительности: "+100 жизни"
                        if (!effectMatch) {
                            effectMatch = potionEffect.match(/\+(\d+)\s+(жизни|маны|к силе|к ловкости|к интеллекту|к выносливости|к скорости)/);
                            if (effectMatch) {
                                effectValue = effectMatch[1];
                                effectType = effectMatch[2];
                                effectDuration = '0'; // Мгновенный эффект
                            }
                        }

                        // Старый технический формат с длительностью: "health (100), 30 сек."
                        if (!effectMatch) {
                            effectMatch = potionEffect.match(/(health|mana|strength|agility|intelligence|stamina|speed)\s+\((\d+)\)(?:,\s+(\d+)\s+сек\.)?/);
                            if (effectMatch) {
                                // Преобразуем технические названия в русские
                                const effectTypeMap = {
                                    'health': 'жизни',
                                    'mana': 'маны',
                                    'strength': 'к силе',
                                    'agility': 'к ловкости',
                                    'intelligence': 'к интеллекту',
                                    'stamina': 'к выносливости',
                                    'speed': 'к скорости'
                                };
                                effectType = effectTypeMap[effectMatch[1]] || effectMatch[1];
                                effectValue = effectMatch[2];
                                effectDuration = effectMatch[3] || '0'; // Если длительность не указана, считаем эффект мгновенным
                            }
                        } else if (effectMatch) {
                            effectValue = effectMatch[1];
                            effectType = effectMatch[2];
                            effectDuration = effectMatch[3] || '0'; // Если длительность не указана, считаем эффект мгновенным
                        }

                        // Если не удалось распарсить, используем значения по умолчанию
                        if (!effectMatch) {
                            console.log('Не удалось распарсить эффект:', potionEffect);
                            effectValue = '100';
                            effectType = 'жизни';
                            effectDuration = '30';
                        }

                        // Определяем описание эффекта в зависимости от типа
                        let effectDescription = '';
                        switch (effectType) {
                            case 'жизни':
                                effectDescription = `Восстанавливает ${effectValue} единиц здоровья мгновенно.`;
                                break;
                            case 'маны':
                                effectDescription = `Восстанавливает ${effectValue} единиц маны мгновенно.`;
                                break;
                            case 'к силе':
                                effectDescription = effectDuration > 0
                                    ? `Увеличивает силу персонажа на ${effectValue} единиц на ${effectDuration} секунд. Повышает физический урон и грузоподъемность.`
                                    : `Увеличивает силу персонажа на ${effectValue} единиц. Повышает физический урон и грузоподъемность.`;
                                break;
                            case 'к ловкости':
                                effectDescription = effectDuration > 0
                                    ? `Увеличивает ловкость персонажа на ${effectValue} единиц на ${effectDuration} секунд. Повышает шанс уклонения и критического удара.`
                                    : `Увеличивает ловкость персонажа на ${effectValue} единиц. Повышает шанс уклонения и критического удара.`;
                                break;
                            case 'к интеллекту':
                                effectDescription = effectDuration > 0
                                    ? `Увеличивает интеллект персонажа на ${effectValue} единиц на ${effectDuration} секунд. Повышает магический урон и максимальное количество маны.`
                                    : `Увеличивает интеллект персонажа на ${effectValue} единиц. Повышает магический урон и максимальное количество маны.`;
                                break;
                            case 'к выносливости':
                                effectDescription = effectDuration > 0
                                    ? `Увеличивает восстановление персонажа на ${effectValue} единиц на ${effectDuration} секунд. Повышает максимальное здоровье и сопротивление урону.`
                                    : `Увеличивает восстановление персонажа на ${effectValue} единиц. Повышает максимальное здоровье и сопротивление урону.`;
                                break;
                            case 'к скорости':
                                effectDescription = effectDuration > 0
                                    ? `Увеличивает скорость персонажа на ${effectValue} единиц на ${effectDuration} секунд. Ускоряет передвижение и снижает время восстановления навыков.`
                                    : `Увеличивает скорость персонажа на ${effectValue} единиц. Ускоряет передвижение и снижает время восстановления навыков.`;
                                break;
                            default:
                                effectDescription = `Применяет эффект ${potionEffect}`;
                        }

                        // Определяем множитель качества для отображения
                        let qualityMultiplier = '1.0';
                        switch (potionQuality) {
                            case 'Обычное':
                                qualityMultiplier = '1.0';
                                break;
                            case 'Улучшенное':
                                qualityMultiplier = '1.5';
                                break;
                            case 'Превосходное':
                                qualityMultiplier = '2.0';
                                break;
                            case 'Легендарное':
                                qualityMultiplier = '3.0';
                                break;
                        }

                        // Формируем HTML для модального окна
                        const detailsHTML = `
                                                                                                                                                        <div class="flex flex-col items-center">
                                                                                                                                                            <div class="relative w-24 h-24 bg-[#252117] rounded-md border border-[#a6925e] overflow-hidden flex items-center justify-center shadow-md mb-4">
                                                                                                                                                                <img src="${potionImage}" alt="${potionName}" class="w-20 h-20 object-contain" style="image-rendering: crisp-edges;">
                                                                                                                                                            </div>

                                                                                                                                                            <h3 class="text-xl font-bold ${potionCard.querySelector('h4').className.replace('text-sm', 'text-xl')} mb-2">${potionName}</h3>

                                                                                                                                                            <div class="w-full bg-[#252117] rounded-md p-3 border border-[#514b3c] mb-3">
                                                                                                                                                                <p class="text-[#c0b9a3] mb-2">${potionDescription}</p>
                                                                                                                                                            </div>

                                                                                                                                                            <div class="w-full grid grid-cols-2 gap-2 mb-3">
                                                                                                                                                                <div class="bg-[#252117] rounded-md p-2 border border-[#514b3c]">
                                                                                                                                                                    <span class="text-[#9a9483] text-sm">Качество:</span>
                                                                                                                                                                    <span class="${potionCard.querySelector('.flex-1 > div > span:first-child').className} block">${potionQuality}</span>
                                                                                                                                                                    <span class="text-[#9a9483] text-xs">Множитель: ×${qualityMultiplier}</span>
                                                                                                                                                                </div>
                                                                                                                                                                <div class="bg-[#252117] rounded-md p-2 border border-[#514b3c]">
                                                                                                                                                                    <span class="text-[#9a9483] text-sm">Использований:</span>
                                                                                                                                                                    <span class="text-[#d9d3b8] block">${potionUses.replace('Использований: ', '')}</span>
                                                                                                                                                                </div>
                                                                                                                                                            </div>

                                                                                                                                                            <div class="w-full bg-[#252117] rounded-md p-3 border border-[#514b3c] mb-3">
                                                                                                                                                                <h4 class="text-[#e5b769] font-medium mb-1">Эффект зелья:</h4>
                                                                                                                                                                <p class="text-[#d9d3b8] mb-2">${potionEffect}</p>
                                                                                                                                                                <p class="text-[#c0b9a3] text-sm">${effectDescription}</p>
                                                                                                                                                            </div>

                                                                                                                                                            <div class="w-full bg-[#252117] rounded-md p-3 border border-[#514b3c]">
                                                                                                                                                                <h4 class="text-[#e5b769] font-medium mb-1">Характеристики:</h4>
                                                                                                                                                                <ul class="text-[#c0b9a3] text-sm space-y-1">
                                                                                                                                                                    <li class="flex justify-between">
                                                                                                                                                                        <span>Базовая сила эффекта:</span>
                                                                                                                                                                        <span class="text-[#d9d3b8]">${effectValue}</span>
                                                                                                                                                                    </li>
                                                                                                                                                                    ${effectDuration > 0 ? `
                                                                                                                                                                    <li class="flex justify-between">
                                                                                                                                                                        <span>Длительность эффекта:</span>
                                                                                                                                                                        <span class="text-[#d9d3b8]">${effectDuration} сек.</span>
                                                                                                                                                                    </li>` : ''}
                                                                                                                                                                    <li class="flex justify-between">
                                                                                                                                                                        <span>Тип эффекта:</span>
                                                                                                                                                                        <span class="text-[#d9d3b8]">${effectType}</span>
                                                                                                                                                                    </li>
                                                                                                                                                                </ul>
                                                                                                                                                            </div>

                                                                                                                                                            <div class="w-full mt-4 bg-[#2a2621] rounded-md p-3 border border-[#514b3c]">
                                                                                                                                                                <h4 class="text-[#e5b769] font-medium mb-1">Применение:</h4>
                                                                                                                                                                <p class="text-[#c0b9a3] mb-2">Зелье можно использовать в бою или вне боя для получения временного эффекта. Эффект не суммируется с другими зельями того же типа.</p>
                                                                                                                                                                <p class="text-[#9a9483] text-xs italic">Для использования зелья перейдите в бой или на страницу персонажа.</p>
                                                                                                                                                            </div>
                                                                                                                                                        </div>
                                                                                                                                                    `;

                        // Обновляем содержимое модального окна
                        itemInfoContent.innerHTML = detailsHTML;
                    } else {
                        // Если карточка не найдена, показываем сообщение об ошибке
                        itemInfoContent.innerHTML = `
                                                                                                                                                        <div class="text-center py-4">
                                                                                                                                                            <p class="text-red-400 mb-2">Не удалось загрузить информацию о зелье.</p>
                                                                                                                                                            <p class="text-[#9a9483]">Пожалуйста, попробуйте еще раз или обновите страницу.</p>
                                                                                                                                                        </div>
                                                                                                                                                    `;
                    }
                });
            });

            // Добавляем обработчики для закрытия модального окна
            closeButtons.forEach(button => {
                if (button) {
                    button.addEventListener('click', function () {
                        itemInfoModal.classList.add('hidden');
                    });
                }
            });

            // Закрытие по клику вне содержимого модального окна
            itemInfoModal.addEventListener('click', function (e) {
                if (e.target === itemInfoModal) {
                    itemInfoModal.classList.add('hidden');
                }
            });
        });
    </script>

    

    
    <div id="addToBeltModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-[#252117] border border-[#514b3c] rounded-md p-4 max-w-md w-full mx-4">
            <h3 class="text-[#e5b769] text-lg font-bold text-center mb-2">Выберите слот на поясе</h3>
            <p class="text-[#d9d3b8] text-center mb-4">Куда поместить <span id="addPotionName" class="font-medium"></span>?
            </p>

            <div class="grid grid-cols-5 gap-2 mb-4">
                <?php for($i = 1; $i <= 5; $i++): ?>
                    <button type="button"
                        class="belt-slot-btn w-12 h-12 bg-[#1a1814] rounded-md border border-[#514b3c] flex items-center justify-center hover:bg-[#252117] transition-colors duration-300"
                        data-slot="<?php echo e($i); ?>">
                        <span class="text-[#9a9483]"><?php echo e($i); ?></span>
                    </button>
                <?php endfor; ?>
            </div>

            <div class="flex justify-center space-x-4">
                <button type="button" id="cancelAddToBelt"
                    class="bg-[#514b3c] text-[#d9d3b8] py-1 px-3 rounded shadow-md hover:bg-[#5d5745] transition duration-300">
                    Отмена
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Обработчики для кнопок добавления зелья на пояс
            const addButtons = document.querySelectorAll('.add-to-belt-btn');
            const modal = document.getElementById('addToBeltModal');
            const potionNameSpan = document.getElementById('addPotionName');
            const slotButtons = document.querySelectorAll('.belt-slot-btn');
            const cancelBtn = document.getElementById('cancelAddToBelt');

            let currentPotionId = null;

            // Обработчик нажатия на кнопку "На пояс"
            addButtons.forEach(button => {
                button.addEventListener('click', function () {
                    currentPotionId = this.dataset.potionId;
                    potionNameSpan.textContent = this.dataset.potionName;
                    console.log('Нажата кнопка "На пояс" для зелья ID:', currentPotionId, 'Название:', this.dataset.potionName);
                    modal.classList.remove('hidden');

                    // Получаем информацию о занятых слотах на поясе
                    fetch('<?php echo e(route("inventory.index")); ?>?tab=belt', {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                        .then(response => response.text())
                        .then(html => {
                            // Создаем временный элемент для парсинга HTML
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = html;

                            // Находим все занятые слоты
                            const occupiedSlots = [];
                            tempDiv.querySelectorAll('[data-slot]').forEach(el => {
                                if (!el.classList.contains('remove-from-belt-btn')) return;
                                occupiedSlots.push(el.dataset.slot);
                            });

                            // Обновляем внешний вид кнопок слотов
                            slotButtons.forEach(btn => {
                                const slotNumber = btn.dataset.slot;
                                if (occupiedSlots.includes(slotNumber)) {
                                    btn.classList.add('opacity-50', 'cursor-not-allowed');
                                    btn.disabled = true;
                                    btn.title = 'Слот занят';
                                } else {
                                    btn.classList.remove('opacity-50', 'cursor-not-allowed');
                                    btn.disabled = false;
                                    btn.title = '';
                                }
                            });
                        })
                        .catch(error => {
                            console.error('Ошибка при получении информации о поясе:', error);
                        });
                });
            });

            // Обработчик нажатия на кнопку слота
            slotButtons.forEach(button => {
                button.addEventListener('click', function () {
                    if (this.disabled) return;

                    const slotPosition = this.dataset.slot;

                    // Проверяем, есть ли экипированный пояс
                    fetch('<?php echo e(route("inventory.index")); ?>?tab=belt', {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                        .then(response => response.text())
                        .then(html => {
                            // Создаем временный элемент для парсинга HTML
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = html;

                            // Проверяем, есть ли сообщение о необходимости экипировать пояс
                            // Используем более общий селектор, так как text-[#9a9483] не является валидным CSS-селектором
                            const noBeltMessage = tempDiv.querySelector('p');
                            if (noBeltMessage && noBeltMessage.textContent.includes('экипировать пояс')) {
                                alert('Вы должны экипировать пояс, чтобы использовать его для зелий.');
                                return Promise.reject('Пояс не экипирован');
                            }

                            // Отправляем запрос на добавление зелья на пояс
                            return fetch('<?php echo e(route("inventory.potion-belt.add")); ?>', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                },
                                body: JSON.stringify({
                                    potion_id: currentPotionId,
                                    slot_position: slotPosition
                                })
                            });
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Перезагружаем страницу для обновления интерфейса
                                window.location.reload();
                            } else {
                                alert(data.message || 'Произошла ошибка при добавлении зелья на пояс');
                            }
                        })
                        .catch(error => {
                            console.error('Ошибка:', error);
                            if (error !== 'Пояс не экипирован') {
                                alert('Произошла ошибка при добавлении зелья на пояс');
                            }
                        })
                        .finally(() => {
                            modal.classList.add('hidden');
                            currentPotionId = null;
                        });
                });
            });

            // Обработчик нажатия на кнопку "Отмена"
            cancelBtn.addEventListener('click', function () {
                modal.classList.add('hidden');
                currentPotionId = null;
            });

            // Закрытие по клику вне окна
            modal.addEventListener('click', function (e) {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                    currentPotionId = null;
                }
            });
        });
    </script>

    
    <style>
        /* Кастомный скроллбар для контента модального окна */
        #itemInfoContent::-webkit-scrollbar {
            width: 8px;
            background: #232218;
            border-radius: 6px;
        }

        #itemInfoContent::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #a6925e 0%, #e5b769 100%);
            border-radius: 6px;
            border: 2px solid #232218;
        }

        #itemInfoContent::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #e5b769 0%, #a6925e 100%);
        }

        /* Для Firefox */
        #itemInfoContent {
            scrollbar-width: thin;
            scrollbar-color: #e5b769 #232218;
        }
    </style>

<?php $__env->stopSection(); ?>


<?php $__env->startPush('scripts'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/eqItem.js', 'resources/js/inventory/inventory-manager.js']); ?>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.inventory', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/user/inventory.blade.php ENDPATH**/ ?>