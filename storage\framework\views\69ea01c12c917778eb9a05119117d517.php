<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Управление зельями - Админ панель</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    
    <div class="min-h-screen flex flex-col">
        
        <main class="flex-grow container mx-auto px-4 py-6">
            
            <div
                class="container max-w-6xl mx-auto px-4 py-4 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg shadow-lg mb-6">
                
                <div class="flex justify-between items-center mb-4">
                    
                    <a href="<?php echo e(route('admin.dashboard')); ?>"
                        class="w-10 h-10 bg-[#a6925e] flex items-center justify-center rounded-lg hover:bg-[#e5b769] transition duration-300">
                        <span class="text-xl">←</span>
                    </a>

                    
                    <h1 class="text-2xl font-bold text-[#e5b769] text-center">Управление зельями</h1>

                    
                    <button onclick="location.reload()"
                        class="w-10 h-10 bg-[#a6925e] flex items-center justify-center rounded-lg hover:bg-[#e5b769] transition duration-300">
                        <span class="text-xl">⟳</span>
                    </button>
                </div>

                
                <?php if(session('success')): ?>
                    <div class="bg-[#38352c] text-[#7cfc00] p-4 rounded-lg border border-[#a6925e] mb-4">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>

                
                <div class="bg-[#38352c] p-4 rounded-lg border border-[#8c784e] mb-4">
                    <h3 class="text-lg font-bold text-[#e5b769] mb-3">Фильтры</h3>
                    <form action="<?php echo e(route('admin.potions.index')); ?>" method="GET"
                        class="grid grid-cols-1 md:grid-cols-4 gap-3">
                        
                        <div>
                            <label for="name" class="block text-sm text-[#d9d3b8] mb-1">Название</label>
                            <input type="text" name="name" id="name" value="<?php echo e(request('name')); ?>"
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                        </div>

                        
                        <div>
                            <label for="effect" class="block text-sm text-[#d9d3b8] mb-1">Эффект</label>
                            <select name="effect" id="effect"
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <option value="">Все эффекты</option>
                                <?php $__currentLoopData = $potionEffects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>" <?php echo e(request('effect') == $key ? 'selected' : ''); ?>>
                                        <?php echo e($value); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        
                        <div>
                            <label for="quality" class="block text-sm text-[#d9d3b8] mb-1">Качество</label>
                            <select name="quality" id="quality"
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <option value="">Все качества</option>
                                <?php $__currentLoopData = $potionQualities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quality): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($quality); ?>" <?php echo e(request('quality') == $quality ? 'selected' : ''); ?>>
                                        <?php echo e($quality); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        
                        <div class="flex items-end space-x-2">
                            <button type="submit"
                                class="px-4 py-2 bg-[#a6925e] text-[#2f2d2b] rounded font-semibold hover:bg-[#e5b769] transition duration-300 flex-grow">
                                Применить
                            </button>
                            <a href="<?php echo e(route('admin.potions.index')); ?>"
                                class="px-4 py-2 bg-[#4a4a3d] text-[#e5b769] rounded font-semibold hover:bg-[#5a5a4d] transition duration-300">
                                Сбросить
                            </a>
                        </div>
                    </form>
                </div>

                
                <div class="flex justify-between items-center mb-4">
                    <div>
                        <a href="<?php echo e(route('admin.potions.create')); ?>"
                            class="px-4 py-2 bg-[#a6925e] text-[#2f2d2b] rounded font-semibold hover:bg-[#e5b769] transition duration-300 inline-flex items-center">
                            <span class="mr-1">+</span> Создать новое зелье
                        </a>
                    </div>

                    <div class="text-[#d9d3b8]">
                        Всего зелий: <span class="text-[#e5b769] font-bold"><?php echo e($potions->total()); ?></span>
                    </div>
                </div>
            </div>

            
            <div class="container max-w-6xl mx-auto mb-6">
                <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] rounded-lg p-4 border-2 border-[#a6925e]">
                    <h2 class="text-xl font-bold text-[#e5b769] mb-4 text-center">Список зелий</h2>

                    <?php if($potions->isEmpty()): ?>
                        <div class="bg-[#38352c] text-[#d9d3b8] p-4 rounded-lg border border-[#8c784e] text-center">
                            Зелья не найдены. <a href="<?php echo e(route('admin.potions.create')); ?>"
                                class="text-[#e5b769] hover:underline">Создать новое зелье</a>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-[#38352c] rounded-lg border border-[#8c784e]">
                                <thead>
                                    <tr class="border-b border-[#8c784e]">
                                        <th class="px-4 py-3 text-left text-sm text-[#e5b769]">ID</th>
                                        <th class="px-4 py-3 text-left text-sm text-[#e5b769]">Иконка</th>
                                        <th class="px-4 py-3 text-left text-sm text-[#e5b769]">Название</th>
                                        <th class="px-4 py-3 text-left text-sm text-[#e5b769]">Эффект</th>
                                        <th class="px-4 py-3 text-left text-sm text-[#e5b769]">Качество</th>
                                        <th class="px-4 py-3 text-left text-sm text-[#e5b769]">Уровень</th>
                                        <th class="px-4 py-3 text-left text-sm text-[#e5b769]">Тип</th>
                                        <th class="px-4 py-3 text-left text-sm text-[#e5b769]">Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $potions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $potion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="border-b border-[#8c784e] hover:bg-[#4a452c]">
                                            <td class="px-4 py-3 text-sm text-[#d9d3b8]"><?php echo e($potion->id); ?></td>
                                            <td class="px-4 py-3 text-sm">
                                                <?php if($potion->icon): ?>
                                                    <?php if(strpos($potion->icon, '/') !== false): ?>
                                                        <img src="<?php echo e(asset($potion->icon)); ?>" alt="<?php echo e($potion->name); ?>"
                                                            class="w-8 h-8 object-contain">
                                                    <?php else: ?>
                                                        <span class="text-2xl"><?php echo e($potion->icon); ?></span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-2xl">🧪</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-4 py-3 text-sm text-[#e5b769]"><?php echo e($potion->name); ?></td>
                                            <td class="px-4 py-3 text-sm text-[#d9d3b8]">
                                                <?php echo e($potionEffects[$potion->effect] ?? $potion->effect ?? 'Неизвестно'); ?>

                                                <span class="text-[#e5b769]">+<?php echo e($potion->effect_value); ?></span>
                                            </td>
                                            <td class="px-4 py-3 text-sm text-[#d9d3b8]"><?php echo e($potion->quality); ?></td>
                                            <td class="px-4 py-3 text-sm text-[#d9d3b8]">
                                                <?php echo e($potionLevels[$potion->level] ?? $potion->level ?? 'Неизвестно'); ?>

                                            </td>
                                            <td class="px-4 py-3 text-sm">
                                                <?php if($potion->is_template || $potion->user_id === null): ?>
                                                    <span
                                                        class="px-2 py-1 bg-green-800 text-green-200 rounded text-xs">Шаблон</span>
                                                <?php else: ?>
                                                    <span
                                                        class="px-2 py-1 bg-blue-800 text-blue-200 rounded text-xs">Экземпляр</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-4 py-3 text-sm space-x-2">
                                                <a href="<?php echo e(route('admin.potions.show', $potion->id)); ?>"
                                                    class="px-2 py-1 bg-[#4a452c] text-[#e5b769] rounded hover:bg-[#5a5a4d] transition duration-300">
                                                    Просмотр
                                                </a>
                                                <a href="<?php echo e(route('admin.potions.edit', $potion->id)); ?>"
                                                    class="px-2 py-1 bg-[#a6925e] text-[#2f2d2b] rounded hover:bg-[#e5b769] transition duration-300">
                                                    Изменить
                                                </a>
                                                <form action="<?php echo e(route('admin.potions.destroy', $potion->id)); ?>" method="POST"
                                                    class="inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" onclick="return confirm('Вы уверены?')"
                                                        class="px-2 py-1 bg-[#8b3a3a] text-[#f5f5f5] rounded hover:bg-[#a84747] transition duration-300">
                                                        Удалить
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        
                        <div class="mt-4">
                            <?php echo e($potions->appends(request()->query())->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>

            
            <footer
                class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] text-[#d9d3b8] py-3 mt-6 border-t-2 border-[#a6925e] rounded-lg">
                <div class="container max-w-6xl mx-auto px-6 text-center">
                    <div class="flex justify-between items-center flex-wrap">
                        <div>
                            <p class="text-sm font-semibold">
                                <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-[#e5b769] hover:underline">
                                    Вернуться в админ-панель
                                </a>
                            </p>
                        </div>

                        <div>
                            <p class="text-sm font-semibold">
                                Время: <span class="text-[#e5b769]"><?php echo e(date('H:i')); ?></span>
                            </p>
                        </div>

                        <div>
                            <form action="<?php echo e(route('logout')); ?>" method="POST" class="inline-block">
                                <?php echo csrf_field(); ?>
                                <button type="submit"
                                    class="bg-[#a6925e] text-[#2f2d2b] py-1 px-3 rounded font-semibold hover:bg-[#e5b769] transition duration-300">
                                    Выйти
                                </button>
                            </form>
                        </div>
                    </div>

                    <p class="text-sm font-semibold mt-3">
                        © <?php echo e(date('Y')); ?> Echoes of Eternity. Все права защищены.
                    </p>
                </div>
            </footer>
        </main>
    </div>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/potions/index.blade.php ENDPATH**/ ?>