<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Models\Mob;
use App\Models\ObeliskMark;
use App\Services\BattleLogService;
use App\Services\SkillService;
use App\Services\LogFormattingService;
use App\Services\ObeliskMarkService;
use App\Services\ObeliskCacheService;
use App\Services\PlayerHealthService;
use App\Services\CombatFormulaService;
use App\Services\ObeliskMobAttackService;
use App\Services\MineDetectionService;
use App\Services\Mine\MobSkillIntegrationService;
use App\Models\MineMark;
use App\Events\MobAttackedPlayer;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

/**
 * Job для автоматической атаки мобов на игроков с метками обелисков
 * Адаптирован с логики OutpostBotAttackService для работы с мобами
 */
class MobAutoAttackJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Максимальное количество попыток выполнения задачи
     *
     * @var int
     */
    public $tries = 3;

    /**
     * Таймаут выполнения задачи в секундах
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * Выполнить задачу
     *
     * @param BattleLogService $battleLogService
     * @param SkillService $skillService
     * @param LogFormattingService $logFormatter
     * @param ObeliskMarkService $markService
     * @param ObeliskCacheService $cacheService
     * @return void
     */
    public function handle(
        BattleLogService $battleLogService,
        SkillService $skillService,
        LogFormattingService $logFormatter,
        ObeliskMarkService $markService = null,
        ObeliskCacheService $cacheService = null
    ): void {
        $lockKey = 'mob_auto_attack_lock';
        $lockTimeout = 60; // 1 минута

        try {
            // Используем Redis блокировку для предотвращения одновременного выполнения
            $lock = Redis::set($lockKey, time(), 'EX', $lockTimeout, 'NX');

            if (!$lock) {
                Log::info('MobAutoAttackJob: Другая задача автоатаки уже выполняется, пропускаем');
                return;
            }

            Log::info('MobAutoAttackJob: Начинаем обработку автоатак мобов');

            // Получаем все активные метки обелисков
            $activeMarks = $this->getActiveObeliskMarks($markService);

            // Получаем всех игроков с дебафом "Замечен" в рудниках
            $mineDetectionService = app(MineDetectionService::class);
            $detectedPlayers = $this->getDetectedPlayersInMines($mineDetectionService);

            if (empty($activeMarks) && empty($detectedPlayers)) {
                Log::info('MobAutoAttackJob: Нет активных меток обелисков или замеченных игроков в рудниках для обработки');
                return;
            }

            $attacksProcessed = 0;
            $playersHealthService = app(PlayerHealthService::class);
            $combatFormulaService = app(CombatFormulaService::class);
            $mobAttackService = app(ObeliskMobAttackService::class);

            foreach ($activeMarks as $markData) {
                try {
                    $result = $this->processMobAttack(
                        $markData,
                        $battleLogService,
                        $logFormatter,
                        $playersHealthService,
                        $combatFormulaService,
                        $markService,
                        $mobAttackService
                    );

                    if ($result) {
                        $attacksProcessed++;
                    }
                } catch (\Exception $e) {
                    Log::error('MobAutoAttackJob: Ошибка при обработке атаки моба', [
                        'mark_data' => $markData,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            // Обрабатываем атаки мобов на замеченных игроков в рудниках
            foreach ($detectedPlayers as $detectedPlayerData) {
                try {
                    $result = $this->processMineDetectionAttack(
                        $detectedPlayerData,
                        $battleLogService,
                        $logFormatter,
                        $playersHealthService,
                        $combatFormulaService,
                        $mineDetectionService
                    );

                    if ($result) {
                        $attacksProcessed++;
                    }
                } catch (\Exception $e) {
                    Log::error('MobAutoAttackJob: Ошибка при обработке атаки моба на замеченного игрока', [
                        'detected_player_data' => $detectedPlayerData,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            Log::info('MobAutoAttackJob: Завершена обработка автоатак мобов', [
                'total_obelisk_marks' => count($activeMarks),
                'total_detected_players' => count($detectedPlayers),
                'attacks_processed' => $attacksProcessed
            ]);

        } catch (\Exception $e) {
            Log::error('MobAutoAttackJob: Критическая ошибка при выполнении задачи', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        } finally {
            // Освобождаем блокировку
            Redis::del($lockKey);
        }
    }

    /**
     * Получить все активные метки обелисков
     *
     * @param ObeliskMarkService|null $markService
     * @return array
     */
    private function getActiveObeliskMarks(?ObeliskMarkService $markService): array
    {
        if (!$markService) {
            $markService = app(ObeliskMarkService::class);
        }

        // Получаем все активные метки из БД
        // ИСПРАВЛЕНИЕ: Убираем загрузку 'mob' так как теперь выбираем моба динамически
        $marks = ObeliskMark::where('is_active', true)
            ->where('expires_at', '>', now())
            ->with(['player.profile', 'player.statistics'])
            ->get();

        $activeMarks = [];
        foreach ($marks as $mark) {
            // Проверяем, что игрок существует
            if (!$mark->player) {
                continue;
            }

            // Проверяем, что игрок жив
            if ($mark->player->profile && $mark->player->profile->current_hp <= 0) {
                continue;
            }

            // Проверяем, что игрок в правильной локации
            if (!$mark->player->statistics || $mark->player->statistics->current_location !== $mark->location_name) {
                continue;
            }

            $activeMarks[] = [
                'mark_id' => $mark->id,
                'player_id' => $mark->player_id,
                'location_name' => $mark->location_name,
                'player' => $mark->player,
                'expires_at' => $mark->expires_at,
                'attack_count' => $mark->attack_count ?? 0
            ];
        }

        return $activeMarks;
    }

    /**
     * Обработать атаку моба на игрока
     *
     * @param array $markData
     * @param BattleLogService $battleLogService
     * @param LogFormattingService $logFormatter
     * @param PlayerHealthService $playerHealthService
     * @param CombatFormulaService $combatFormulaService
     * @param ObeliskMarkService|null $markService
     * @param ObeliskMobAttackService $mobAttackService
     * @return bool
     */
    private function processMobAttack(
        array $markData,
        BattleLogService $battleLogService,
        LogFormattingService $logFormatter,
        PlayerHealthService $playerHealthService,
        CombatFormulaService $combatFormulaService,
        ?ObeliskMarkService $markService,
        ObeliskMobAttackService $mobAttackService
    ): bool {
        $player = $markData['player'];
        $locationName = $markData['location_name'];

        // ИСПРАВЛЕНИЕ: Выбираем случайного моба из локации вместо фиксированного из метки
        $mob = $mobAttackService->getRandomMobForAttack($locationName, $player);

        if (!$mob) {
            Log::debug('MobAutoAttackJob: Нет доступных мобов для атаки в локации', [
                'player_id' => $player->id,
                'player_name' => $player->name,
                'location' => $locationName
            ]);
            return false;
        }

        // Проверяем, что игрок онлайн (активность в последние 20 минут)
        $onlineThreshold = now()->timestamp - (20 * 60);
        if (!$player->is_online && $player->last_activity_timestamp <= $onlineThreshold) {
            Log::debug('MobAutoAttackJob: Игрок не онлайн, пропускаем атаку', [
                'player_id' => $player->id,
                'player_name' => $player->name
            ]);
            return false;
        }

        // Проверяем текущее HP игрока
        $currentHP = $playerHealthService->getCurrentHP($player);
        if ($currentHP <= 0) {
            Log::debug('MobAutoAttackJob: Игрок мертв, пропускаем атаку', [
                'player_id' => $player->id,
                'current_hp' => $currentHP
            ]);
            return false;
        }

        // Рассчитываем урон
        $damage = $this->calculateMobDamage($mob, $player, $combatFormulaService);

        // Применяем урон
        $damageResult = $playerHealthService->applyDamage($player, $damage, "mob:{$mob->id}");

        // Обрабатываем скиллы моба при атаке
        $mobSkillService = app(MobSkillIntegrationService::class);
        $activatedSkills = $mobSkillService->processMobAttackSkills($mob, $player, [
            'location_name' => $locationName,
            'damage_dealt' => $damageResult['damage'],
            'attack_type' => 'obelisk_mark'
        ]);

        // Обновляем статистику метки
        if ($markService) {
            $markService->updateLastAttack($player->id, $locationName);
        }

        // Добавляем лог в боевой журнал игрока
        $this->addBattleLog($player, $mob, $damageResult, $battleLogService, $logFormatter, $activatedSkills);

        // Генерируем событие атаки
        event(new MobAttackedPlayer($mob, $player, $damageResult['damage']));

        Log::info('MobAutoAttackJob: Моб атаковал игрока', [
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'player_id' => $player->id,
            'player_name' => $player->name,
            'damage' => $damageResult['damage'],
            'location' => $locationName,
            'player_hp_after' => $damageResult['new_hp']
        ]);

        return true;
    }

    /**
     * Рассчитать урон моба по игроку
     *
     * @param Mob $mob
     * @param User $player
     * @param CombatFormulaService $combatFormulaService
     * @return int
     */
    private function calculateMobDamage(Mob $mob, User $player, CombatFormulaService $combatFormulaService): int
    {
        // Получаем силу моба и броню игрока
        $mobStrength = $mob->strength ?? 1;
        $playerArmor = $player->profile->getEffectiveStats()['armor'] ?? 0;

        // Используем CombatFormulaService для расчета базового урона
        $baseDamage = $combatFormulaService->calculateDamage($mobStrength, $playerArmor);

        // Добавляем случайность ±10%
        $randomFactor = rand(90, 110) / 100;
        $finalDamage = $baseDamage * $randomFactor;

        // Проверяем на критический удар (5% шанс)
        $isCrit = rand(1, 100) <= 5;
        if ($isCrit) {
            $finalDamage = ceil($finalDamage * 1.5);
        }

        return max(1, intval($finalDamage));
    }

    /**
     * Добавить запись в боевой лог игрока
     *
     * @param User $player
     * @param Mob $mob
     * @param array $damageResult
     * @param BattleLogService $battleLogService
     * @param LogFormattingService $logFormatter
     * @return void
     */
    private function addBattleLog(
        User $player,
        Mob $mob,
        array $damageResult,
        BattleLogService $battleLogService,
        LogFormattingService $logFormatter,
        array $activatedSkills = []
    ): void {
        $battleLogKey = $battleLogService->getBattleLogKey($player->id);

        // Форматируем сообщение об атаке моба с учетом активированных скиллов
        $attackMessage = $logFormatter->formatMobAttack(
            $mob,
            $player,
            $damageResult['damage'],
            $damageResult['is_dead'] ?? false,
            $activatedSkills
        );

        $battleLogService->addLog($battleLogKey, $attackMessage, 'danger');

        // Если игрок умер, добавляем дополнительное сообщение
        if ($damageResult['is_dead'] ?? false) {
            $deathMessage = $logFormatter->formatGenericMessage(
                "💀",
                "Вы погибли от атаки моба {$mob->name}!",
                "",
                "text-red-500"
            );
            $battleLogService->addLog($battleLogKey, $deathMessage, 'danger');
        }
    }

    /**
     * Применить урон игроку (для обратной совместимости с тестами)
     *
     * @param User $player
     * @param int $damage
     * @return int Фактически нанесенный урон
     */
    public function applyDamageToPlayer(User $player, int $damage): int
    {
        $playerHealthService = app(PlayerHealthService::class);
        $damageResult = $playerHealthService->applyDamage($player, $damage, "mob_test");

        return $damageResult['damage'];
    }

    /**
     * Получить все активные метки рудников
     *
     * @param MineDetectionService $mineDetectionService
     * @return array
     */
    private function getActiveMineMarks(MineDetectionService $mineDetectionService): array
    {
        try {
            // Получаем все активные метки из БД
            $marks = MineMark::with(['player.profile', 'mineLocation'])
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->get();

            $activeMarks = [];
            foreach ($marks as $mark) {
                // Проверки валидности игрока
                if (
                    !$mark->player ||
                    ($mark->player->profile && $mark->player->profile->current_hp <= 0)
                ) {
                    continue;
                }

                // Проверяем локацию игрока через statistics
                $playerLocation = $mark->player->statistics->current_location ?? null;
                $expectedLocation = $mark->location_name;

                if ($playerLocation !== $expectedLocation) {
                    Log::debug('MobAutoAttackJob: Игрок не в нужной локации', [
                        'player_id' => $mark->player_id,
                        'player_location' => $playerLocation,
                        'expected_location' => $expectedLocation
                    ]);
                    continue;
                }

                $activeMarks[] = [
                    'mark_id' => $mark->id,
                    'player_id' => $mark->player_id,
                    'mine_location_id' => $mark->mine_location_id,
                    'location_id' => $mark->location_id,
                    'location_name' => $mark->location_name,
                    'player' => $mark->player,
                    'mine_location' => $mark->mineLocation,
                    'expires_at' => $mark->expires_at,
                    'attack_count' => $mark->attack_count ?? 0,
                    'source_type' => 'mine_mark'
                ];
            }

            Log::info('MobAutoAttackJob: Найдено активных меток рудников', [
                'active_marks_count' => count($activeMarks),
                'total_marks_checked' => count($marks)
            ]);

            return $activeMarks;

        } catch (\Exception $e) {
            Log::error('MobAutoAttackJob: Ошибка при получении активных меток рудников', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [];
        }
    }

    /**
     * Получить всех замеченных игроков в рудниках (обратная совместимость)
     */
    private function getDetectedPlayersInMines(MineDetectionService $mineDetectionService): array
    {
        return $this->getActiveMineMarks($mineDetectionService);
    }

    /**
     * Обработать атаку моба на замеченного игрока в руднике
     *
     * @param array $detectedPlayerData
     * @param BattleLogService $battleLogService
     * @param LogFormattingService $logFormatter
     * @param PlayerHealthService $playersHealthService
     * @param CombatFormulaService $combatFormulaService
     * @param MineDetectionService $mineDetectionService
     * @return bool
     */
    private function processMineDetectionAttack(
        array $detectedPlayerData,
        BattleLogService $battleLogService,
        LogFormattingService $logFormatter,
        PlayerHealthService $playersHealthService,
        CombatFormulaService $combatFormulaService,
        MineDetectionService $mineDetectionService
    ): bool {
        try {
            $playerId = $detectedPlayerData['player_id'];
            $mineLocation = $detectedPlayerData['mine_location'];

            // Находим игрока
            $player = User::find($playerId);
            if (!$player) {
                Log::warning('MobAutoAttackJob: Игрок не найден', ['player_id' => $playerId]);
                return false;
            }

            // Проверяем, что игрок все еще в руднике
            $playerLocation = $player->statistics->current_location ?? null;
            $expectedLocation = $mineLocation->name;

            if ($playerLocation !== $expectedLocation) {
                Log::info('MobAutoAttackJob: Игрок покинул рудник, удаляем дебаф', [
                    'player_id' => $playerId,
                    'current_location' => $playerLocation,
                    'expected_location' => $expectedLocation
                ]);

                $mineDetectionService->removeDetectionDebuff($playerId, $mineLocation->location_id);
                return false;
            }

            // Проверяем, что дебаф еще активен
            if (!$mineDetectionService->isPlayerDetected($playerId, $mineLocation->location_id, $mineLocation->id)) {
                Log::info('MobAutoAttackJob: Дебаф обнаружения больше не активен', [
                    'player_id' => $playerId,
                    'mine_location_id' => $mineLocation->id
                ]);
                return false;
            }

            // Находим случайного моба в этом руднике
            $mob = Mob::where('location_id', $mineLocation->location_id)
                ->where('mine_location_id', $mineLocation->id)
                ->where('hp', '>', 0)
                ->inRandomOrder()
                ->first();

            if (!$mob) {
                Log::info('MobAutoAttackJob: Нет доступных мобов в руднике', [
                    'mine_location_id' => $mineLocation->id,
                    'location_id' => $mineLocation->location_id
                ]);
                return false;
            }

            // Рассчитываем урон
            $damage = $this->calculateMobDamage($mob, $player, $combatFormulaService);

            // Применяем урон
            $damageResult = $playersHealthService->applyDamage($player, $damage, "mine_mob_detection:{$mob->id}");

            // Обрабатываем скиллы моба при атаке в руднике
            $mobSkillService = app(MobSkillIntegrationService::class);
            $activatedSkills = $mobSkillService->processMobAttackSkills($mob, $player, [
                'mine_location_id' => $mineLocation->id,
                'location_id' => $mineLocation->location_id,
                'damage_dealt' => $damageResult['damage'],
                'attack_type' => 'mine_detection'
            ]);

            // Добавляем запись в боевой лог
            $this->addMineDetectionBattleLog(
                $player,
                $mob,
                $mineLocation,
                $damageResult,
                $battleLogService,
                $logFormatter,
                $activatedSkills
            );

            // Запускаем событие атаки моба
            event(new MobAttackedPlayer($mob, $player, $damageResult['damage']));

            Log::info('MobAutoAttackJob: Моб атаковал замеченного игрока в руднике', [
                'mob_id' => $mob->id,
                'mob_name' => $mob->name,
                'player_id' => $player->id,
                'player_name' => $player->name,
                'mine_location_id' => $mineLocation->id,
                'damage' => $damageResult['damage'],
                'player_health_after' => $damageResult['health_after'],
                'is_dead' => $damageResult['is_dead'] ?? false
            ]);

            // Если игрок умер, удаляем дебаф обнаружения
            if ($damageResult['is_dead'] ?? false) {
                $mineDetectionService->removeDetectionDebuff($playerId, $mineLocation->location_id);
            }

            return true;

        } catch (\Exception $e) {
            Log::error('MobAutoAttackJob: Ошибка при обработке атаки моба на замеченного игрока', [
                'detected_player_data' => $detectedPlayerData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Добавить запись в боевой лог для атаки в руднике
     *
     * @param User $player
     * @param Mob $mob
     * @param \App\Models\MineLocation $mineLocation
     * @param array $damageResult
     * @param BattleLogService $battleLogService
     * @param LogFormattingService $logFormatter
     * @return void
     */
    private function addMineDetectionBattleLog(
        User $player,
        Mob $mob,
        \App\Models\MineLocation $mineLocation,
        array $damageResult,
        BattleLogService $battleLogService,
        LogFormattingService $logFormatter,
        array $activatedSkills = []
    ): void {
        $battleLogKey = $battleLogService->getBattleLogKey($player->id);

        // Форматируем сообщение об атаке моба в руднике
        $attackMessage = $logFormatter->formatGenericMessage(
            "⚔️",
            "Моб {$mob->name} атаковал вас в руднике {$mineLocation->name}!",
            "Урон: {$damageResult['damage']} 💔 HP: {$damageResult['health_after']}/{$player->profile->max_health}",
            "text-red-400"
        );

        $battleLogService->addLog($battleLogKey, $attackMessage, 'danger');

        // Добавляем сообщения о примененных скиллах
        foreach ($activatedSkills as $skillResult) {
            if ($skillResult['success'] && !empty($skillResult['message'])) {
                $skillMessage = $logFormatter->formatGenericMessage(
                    "⚡",
                    $skillResult['message'],
                    "",
                    "text-yellow-400"
                );
                $battleLogService->addLog($battleLogKey, $skillMessage, 'warning');
            }
        }

        // Если игрок умер, добавляем дополнительное сообщение
        if ($damageResult['is_dead'] ?? false) {
            $deathMessage = $logFormatter->formatGenericMessage(
                "💀",
                "Вы погибли от атаки моба в руднике!",
                "Дебаф обнаружения снят.",
                "text-red-500"
            );
            $battleLogService->addLog($battleLogKey, $deathMessage, 'danger');
        }
    }
}
