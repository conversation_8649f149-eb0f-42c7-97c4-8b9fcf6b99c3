<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['obelisk' => null, 'user' => null, 'routePrefix' => 'battle.outposts']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['obelisk' => null, 'user' => null, 'routePrefix' => 'battle.outposts']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<div class="flex flex-col items-center justify-center py-2 px-2 w-full">
    <?php if($obelisk): ?>
        <div class="flex flex-col items-center justify-center w-full">
            <form action="<?php echo e(route($routePrefix . '.select-random-mob', request()->route('id'))); ?>" method="POST"
                class="flex flex-col items-center justify-center w-full">
                <?php echo csrf_field(); ?>
                <button type="submit"
                    class="bg-transparent border-0 p-0 cursor-pointer hover:opacity-80 transition-opacity flex flex-col items-center justify-center mx-auto">
                    <div class="flex justify-center w-full">
                        <div class="obelisk-container relative mx-auto">
                            <img src="<?php echo e(asset('assets/obelisk.png')); ?>" alt="Обелиск" class="w-12 h-16 mx-auto">
                        </div>
                    </div>
                </button>
            </form>

            
            <?php
                $progressPercent = ($obelisk->current_state / $obelisk->max_value) * 100;
                $progressColor = $progressPercent > 70 ? 'bg-gradient-to-r from-[#4a5c2f] to-[#5a6d3f]' :
                    ($progressPercent > 30 ? 'bg-gradient-to-r from-[#8c7a45] to-[#9c8a55]' :
                        'bg-gradient-to-r from-[#8a4a4a] to-[#9a5a5a]');
                $textColor = $progressPercent > 70 ? 'text-[#a3e635]' :
                    ($progressPercent > 30 ? 'text-[#e5b769]' : 'text-[#ff6b6b]');
                $formattedCurrent = number_format($obelisk->current_state, 0, ',', ' ');
                $formattedMax = number_format($obelisk->max_value, 0, ',', ' ');
            ?>

            <div class="flex justify-center w-full">
                <div
                    class="w-full max-w-xs bg-[#2a2721] border border-[#514b3c] rounded-md relative overflow-hidden h-5 mx-auto">
                    <div class="<?php echo e($progressColor); ?>" style="width: <?php echo e($progressPercent); ?>%; height: 100%;"></div>
                    <div class="absolute inset-0 flex justify-center items-center text-[11px] font-bold <?php echo e($textColor); ?>">
                        <?php echo e($formattedCurrent); ?>/<?php echo e($formattedMax); ?>

                    </div>
                </div>
            </div>


        </div>
    <?php else: ?>
        <div class="flex flex-col items-center py-4">
            <div class="text-[#7a7666] text-center">
                <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                <p>Обелиск не найден</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
    /* Контейнер для обелиска */
    .obelisk-container {
        width: 48px;
        /* w-12 */
        height: 64px;
        /* h-16 */
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }
</style><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/outposts/obelisk-block.blade.php ENDPATH**/ ?>