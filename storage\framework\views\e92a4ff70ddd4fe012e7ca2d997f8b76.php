<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    
    <title>Редактирование рецепта зелья - Админка</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>



<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    
    
    <div class="min-h-screen flex flex-col">
        
        
        <main class="flex-grow p-2 md:p-4">
            
            
            <div class="w-full bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg">
                
                
                <div class="flex items-center justify-between px-4 py-2">
                    
                    
                    <a href="<?php echo e(route('admin.dashboard')); ?>"
                        class="w-8 h-8 bg-[#a6925e] flex items-center justify-center rounded hover:bg-[#e5b769]">
                        🏠
                    </a>
                    <div class="flex items-center space-x-4">
                        
                        
                        <div class="flex items-center space-x-4">
                            <span class="text-[#e5b769] font-bold">Редактирование рецепта: <?php echo e($potionRecipe->name); ?></span>
                        </div>
                    </div>
                    
                    
                    <a href="<?php echo e(route('admin.potion-recipes.index')); ?>"
                        class="w-8 h-8 bg-[#a6925e] flex items-center justify-center rounded hover:bg-[#e5b769]">
                        ↩
                    </a>
                </div>

                
                
                <?php if(session('success')): ?>
                    <div class="bg-[#5e7ba6] text-white p-2 m-2 rounded">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>

                
                
                <div class="p-4">
                    
                    
                    <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#a6925e] rounded-lg p-4">
                        
                        
                        <form action="<?php echo e(route('admin.potion-recipes.update', $potionRecipe)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>

                            
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                
                                
                                
                                <div>
                                    
                                    
                                    <label for="name" class="block text-[#e5b769] mb-1">Название рецепта <span
                                            class="text-red-500">*</span></label>
                                    <input type="text" name="name" id="name" value="<?php echo e(old('name', $potionRecipe->name)); ?>"
                                        required
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                

                                <div>
                                    
                                    
                                    <label for="quality" class="block text-[#e5b769] mb-1">Качество рецепта <span
                                            class="text-red-500">*</span></label>
                                    <select name="quality" id="quality" required
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                        <?php $__currentLoopData = $qualities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $qualityOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($qualityOption); ?>" <?php echo e(old('quality', $potionRecipe->quality) == $qualityOption ? 'selected' : ''); ?>>
                                                <?php echo e($qualityOption); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['quality'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    
                                    
                                    <label for="effect" class="block text-[#e5b769] mb-1">Эффект</label>
                                    <input type="text" name="effect" id="effect"
                                        value="<?php echo e(old('effect', $potionRecipe->effect)); ?>"
                                        placeholder="например, heal, stamina_regen"
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    <?php $__errorArgs = ['effect'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    
                                    
                                    <label for="brewing_time_modifier" class="block text-[#e5b769] mb-1">Модификатор времени варки <span class="text-red-500">*</span></label>
                                    <input type="number" name="brewing_time_modifier" id="brewing_time_modifier"
                                        value="<?php echo e(old('brewing_time_modifier', $potionRecipe->brewing_time_modifier)); ?>" step="0.01" min="0.1" max="10" required
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    <p class="text-xs text-gray-400 mt-1">Множитель для базового времени. 1.0 = 100%, 0.8 = 80%.</p>
                                    <?php $__errorArgs = ['brewing_time_modifier'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div>
                                    
                                    
                                    <label for="effect_value" class="block text-[#e5b769] mb-1">Сила эффекта <span
                                            class="text-red-500">*</span></label>
                                    <input type="number" name="effect_value" id="effect_value"
                                        value="<?php echo e(old('effect_value', $potionRecipe->effect_value)); ?>" min="0" required
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    <?php $__errorArgs = ['effect_value'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    
                                    
                                    <label for="effect_duration" class="block text-[#e5b769] mb-1">Длительность эффекта
                                        (сек) <span class="text-red-500">*</span></label>
                                    <input type="number" name="effect_duration" id="effect_duration"
                                        value="<?php echo e(old('effect_duration', $potionRecipe->effect_duration)); ?>" min="0"
                                        required
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    <?php $__errorArgs = ['effect_duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    
                                    
                                    <label for="brewing_time" class="block text-[#e5b769] mb-1">Базовое время варки (сек) <span
                                            class="text-red-500">*</span></label>
                                    <input type="number" name="brewing_time" id="brewing_time"
                                        value="<?php echo e(old('brewing_time', $potionRecipe->brewing_time)); ?>" min="10" required
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    <?php $__errorArgs = ['brewing_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div>
                                    
                                    
                                    <label for="level" class="block text-[#e5b769] mb-1">Уровень рецепта <span
                                            class="text-red-500">*</span></label>
                                    <input type="number" name="level" id="level" value="<?php echo e(old('level', $potionRecipe->level)); ?>"
                                        min="1" max="7" required
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    <?php $__errorArgs = ['level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    
                                    
                                    <label for="icon" class="block text-[#e5b769] mb-1">Иконка <span
                                            class="text-red-500">*</span></label>
                                    <input type="text" name="icon" id="icon"
                                        value="<?php echo e(old('icon', $potionRecipe->icon)); ?>" required
                                        placeholder="например, icons/recipes/heal_potion.png"
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    <?php if($potionRecipe->icon_path): ?>
                                        <img src="<?php echo e($potionRecipe->icon_path); ?>" alt="Иконка" class="mt-2 h-8 w-8">
                                    <?php endif; ?>
                                    <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    
                                    
                                    <label for="color" class="block text-[#e5b769] mb-1">Цвет зелья <span
                                            class="text-red-500">*</span></label>
                                    <input type="color" name="color" id="color"
                                        value="<?php echo e(old('color', $potionRecipe->color)); ?>" required
                                        class="w-full h-10 bg-[#2a2721] border border-[#a6925e] rounded px-1 focus:outline-none focus:border-[#e5b769] cursor-pointer">
                                    <?php $__errorArgs = ['color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    
                                    
                                    <label for="min_alchemy_level" class="block text-[#e5b769] mb-1">Мин. уровень
                                        алхимии <span class="text-red-500">*</span></label>
                                    <input type="number" name="min_alchemy_level" id="min_alchemy_level"
                                        value="<?php echo e(old('min_alchemy_level', $potionRecipe->min_alchemy_level)); ?>" min="1"
                                        required
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    <?php $__errorArgs = ['min_alchemy_level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="flex items-center">
                                    <label class="inline-flex items-center mt-6">
                                        <input type="hidden" name="is_active" value="0">
                                        <input type="checkbox" name="is_active" value="1" <?php echo e(old('is_active', $potionRecipe->is_active) ? 'checked' : ''); ?>

                                            class="bg-[#2a2721] border border-[#a6925e] rounded h-5 w-5 text-[#a6925e] focus:ring-[#a6925e]">
                                        <span class="ml-2 text-[#e5b769]">Активен</span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-4">
                                
                                
                                <label for="description" class="block text-[#e5b769] mb-1">Описание</label>
                                <textarea name="description" id="description" rows="3"
                                    class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]"><?php echo e(old('description', $potionRecipe->description)); ?></textarea>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div>
                                    
                                    
                                    <label for="price_gold" class="block text-[#e5b769] mb-1">Цена (золото)</label>
                                    <input type="number" name="price_gold" id="price_gold"
                                        value="<?php echo e(old('price_gold', $potionRecipe->price_gold)); ?>" min="0"
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    
                                    
                                    <?php $__errorArgs = ['price_gold'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    
                                    
                                    <label for="price_silver" class="block text-[#e5b769] mb-1">Цена (серебро)</label>
                                    <input type="number" name="price_silver" id="price_silver"
                                        value="<?php echo e(old('price_silver', $potionRecipe->price_silver)); ?>" min="0"
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    
                                    
                                    <?php $__errorArgs = ['price_silver'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div>
                                    
                                    
                                    <label for="price_bronze" class="block text-[#e5b769] mb-1">Цена (бронза)</label>
                                    <input type="number" name="price_bronze" id="price_bronze"
                                        value="<?php echo e(old('price_bronze', $potionRecipe->price_bronze)); ?>" min="0"
                                        class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]">
                                    
                                    
                                    <?php $__errorArgs = ['price_bronze'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            
                            
                            <div class="mt-6 mb-4">
                                <h3 class="text-[#e5b769] font-bold mb-3">Ингредиенты для рецепта <span
                                        class="text-red-500">*</span></h3>

                                
                                
                                <div id="ingredients-container" class="space-y-3">
                                    <?php
                                        // Получаем старые данные или существующие ингредиенты рецепта
                                        // Get old data or existing recipe ingredients
                                        $currentIngredients = old('ingredients', $potionRecipe->ingredients->map(function($ing) {
                                            return ['id' => $ing->id, 'quantity' => $ing->pivot->quantity];
                                        })->toArray());
                                    ?>

                                    <?php $__empty_1 = true; $__currentLoopData = $currentIngredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $recipeIngredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <div class="ingredient-row flex flex-wrap items-center space-x-2">
                                            <div class="w-full md:w-3/5 mb-2 md:mb-0">
                                                
                                                
                                                <select name="ingredients[<?php echo e($index); ?>][id]"
                                                    class="ingredient-select w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]"
                                                    required>
                                                    <option value="">Выберите ингредиент</option>
                                                    <?php $__currentLoopData = $ingredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($ingredient->id); ?>"
                                                                <?php echo e($recipeIngredient['id'] == $ingredient->id ? 'selected' : ''); ?>

                                                                data-icon="<?php echo e($ingredient->icon_path); ?>">
                                                            <?php echo e($ingredient->name); ?> (<?php echo e($ingredient->rarity_name); ?>)
                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                            <div class="w-1/4 md:w-1/5">
                                                
                                                
                                                <input type="number" name="ingredients[<?php echo e($index); ?>][quantity]"
                                                    placeholder="Кол-во" value="<?php echo e($recipeIngredient['quantity']); ?>"
                                                    min="1"
                                                    class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]"
                                                    required>
                                            </div>
                                            <div class="md:w-1/5 flex justify-end items-center">
                                                
                                                
                                                <button type="button"
                                                    class="remove-ingredient bg-[#a65e5e] hover:bg-[#bd7171] text-white px-2 py-1 rounded">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                
                                                
                                                <button type="button"
                                                    class="add-ingredient bg-[#5e7ba6] hover:bg-[#718ebd] text-white px-2 py-1 rounded ml-1">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        
                                        
                                        <div class="ingredient-row flex flex-wrap items-center space-x-2">
                                            <div class="w-full md:w-3/5 mb-2 md:mb-0">
                                                <select name="ingredients[0][id]"
                                                    class="ingredient-select w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]"
                                                    required>
                                                    <option value="">Выберите ингредиент</option>
                                                    <?php $__currentLoopData = $ingredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($ingredient->id); ?>" data-icon="<?php echo e($ingredient->icon_path); ?>">
                                                            <?php echo e($ingredient->name); ?> (<?php echo e($ingredient->rarity_name); ?>)
                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                            <div class="w-1/4 md:w-1/5">
                                                <input type="number" name="ingredients[0][quantity]" placeholder="Кол-во"
                                                    value="1" min="1"
                                                    class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]"
                                                    required>
                                            </div>
                                            <div class="md:w-1/5 flex justify-end items-center">
                                                <button type="button"
                                                    class="remove-ingredient bg-[#a65e5e] hover:bg-[#bd7171] text-white px-2 py-1 rounded"
                                                    style="visibility: hidden;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button type="button"
                                                    class="add-ingredient bg-[#5e7ba6] hover:bg-[#718ebd] text-white px-2 py-1 rounded ml-1">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                
                                
                                <?php $__errorArgs = ['ingredients'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <?php $__errorArgs = ['ingredients.*.id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1">Выбран недопустимый ингредиент.</p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <?php $__errorArgs = ['ingredients.*.quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1">Количество ингредиента должно быть не меньше 1.</p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            
                            
                            <div class="flex justify-end mt-6">
                                
                                
                                <a href="<?php echo e(route('admin.potion-recipes.index')); ?>"
                                    class="bg-[#607D8B] hover:bg-[#78909C] text-white py-2 px-4 rounded mr-2">
                                    Отмена
                                </a>
                                
                                
                                <button type="submit"
                                    class="bg-[#5e7ba6] hover:bg-[#718ebd] text-white py-2 px-4 rounded">
                                    <i class="fas fa-sync-alt mr-1"></i> Обновить
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    
    
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const container = document.getElementById('ingredients-container');
            // Начинаем индекс со следующего номера после существующих/старых данных
            // Start index from the next number after existing/old data
            let ingredientIndex = <?php echo e(count($currentIngredients)); ?>;

            // Функция для добавления новой строки ингредиента
            // Function to add a new ingredient row
            function addIngredientRow() {
                const newRow = document.createElement('div');
                newRow.className = 'ingredient-row flex flex-wrap items-center space-x-2 mt-2';
                const currentIndex = ingredientIndex++;

                // Генерируем HTML для новой строки
                // Generate HTML for the new row
                newRow.innerHTML = `
                    <div class="w-full md:w-3/5 mb-2 md:mb-0">
                        <select name="ingredients[${currentIndex}][id]" class="ingredient-select w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]" required>
                            <option value="">Выберите ингредиент</option>
                            <?php $__currentLoopData = $ingredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($ingredient->id); ?>" data-icon="<?php echo e($ingredient->icon_path); ?>"><?php echo e($ingredient->name); ?> (<?php echo e($ingredient->rarity_name); ?>)</option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="w-1/4 md:w-1/5">
                        <input type="number" name="ingredients[${currentIndex}][quantity]" placeholder="Кол-во" value="1" min="1" class="w-full bg-[#2a2721] border border-[#a6925e] rounded px-3 py-2 focus:outline-none focus:border-[#e5b769]" required>
                    </div>
                    <div class="md:w-1/5 flex justify-end items-center">
                        <button type="button" class="remove-ingredient bg-[#a65e5e] hover:bg-[#bd7171] text-white px-2 py-1 rounded">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button type="button" class="add-ingredient bg-[#5e7ba6] hover:bg-[#718ebd] text-white px-2 py-1 rounded ml-1">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                `;
                container.appendChild(newRow);
                updateRemoveButtonsVisibility(); // Обновляем видимость кнопок удаления // Update visibility of remove buttons
            }

            // Функция для обновления видимости кнопок удаления
            // Function to update visibility of remove buttons
            function updateRemoveButtonsVisibility() {
                const rows = container.querySelectorAll('.ingredient-row');
                rows.forEach((row, index) => {
                    const removeButton = row.querySelector('.remove-ingredient');
                    // Показываем кнопку удаления для всех строк, кроме первой, если ингредиентов больше одного
                    // Show remove button for all rows except the first one if there is more than one ingredient
                    removeButton.style.visibility = rows.length > 1 ? 'visible' : 'hidden';
                });
            }

            // Обработчик клика для добавления и удаления строк
            // Click handler for adding and removing rows
            container.addEventListener('click', function (e) {
                // Если нажата кнопка добавления
                // If the add button is clicked
                if (e.target.closest('.add-ingredient')) {
                    addIngredientRow();
                }
                // Если нажата кнопка удаления
                // If the remove button is clicked
                else if (e.target.closest('.remove-ingredient')) {
                    const rowToRemove = e.target.closest('.ingredient-row');
                    // Не удаляем последнюю строку
                    // Do not remove the last row
                    if (container.querySelectorAll('.ingredient-row').length > 1) {
                        rowToRemove.remove();
                        updateRemoveButtonsVisibility(); // Обновляем видимость после удаления // Update visibility after removal
                    }
                }
            });

            // Инициализация видимости кнопок при загрузке страницы
            // Initialize button visibility on page load
            updateRemoveButtonsVisibility();
        });
    </script>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/alchemy/recipes/edit.blade.php ENDPATH**/ ?>