<?php
    // Определение цветов и текста для качества // Define colors and text for quality
    $qualityClasses = [
        'Обычное' => 'text-gray-300 border-gray-500',
        'Необычное' => 'text-green-400 border-green-500',
        'Редкое' => 'text-blue-400 border-blue-500',
        'Эпическое' => 'text-purple-400 border-purple-500',
        'Легендарное' => 'text-orange-400 border-orange-500',
    ];
    $qualityClass = $qualityClasses[$recipe->quality] ?? 'text-gray-300 border-gray-500'; // Класс CSS для качества // CSS class for quality

    // Уровень рецепта как число // Recipe level as number
    $levelInt = intval($recipe->level);

    // Базовое время варки в секундах // Base brewing time in seconds
    $baseBrewingTimeSeconds = $recipe->brewing_time ?? 0;
    // Модификатор времени варки // Brewing time modifier
    $modifier = $recipe->brewing_time_modifier ?? 1;
    // Реальное время варки с модификатором // Actual brewing time with modifier
    $actualBrewingTimeSeconds = ceil($baseBrewingTimeSeconds * $modifier);
    // Уменьшение времени варки в зависимости от уровня (мин) // Brewing time reduction based on level (min)
    $brewingTimeReductionMinutes = max(30, 120 - ($levelInt - 1) * 15);
    // Окончательное время варки в секундах // Final brewing time in seconds
    $finalBrewingTimeSeconds = min($actualBrewingTimeSeconds, $brewingTimeReductionMinutes * 60);

    // Функция форматирования секунд в ЧЧ:ММ:СС или ММ:СС // Function to format seconds into HH:MM:SS or MM:SS
    function formatDetailedTime($seconds)
    {
        if ($seconds < 0)
            $seconds = 0; // Предотвращаем отрицательное время // Prevent negative time
        $h = floor($seconds / 3600); // Часы // Hours
        $m = floor(($seconds % 3600) / 60); // Минуты // Minutes
        $s = $seconds % 60; // Секунды // Seconds
        $parts = [];
        if ($h > 0)
            $parts[] = $h . ' ч'; // Добавляем часы, если есть // Add hours if present
        if ($m > 0)
            $parts[] = $m . ' мин'; // Добавляем минуты, если есть // Add minutes if present
        if ($s > 0 || empty($parts))
            $parts[] = $s . ' сек'; // Добавляем секунды, если есть или если нет других частей // Add seconds if present or if no other parts
        return implode(' ', $parts); // Объединяем части // Combine parts
    }
    $formattedBrewingTime = formatDetailedTime($finalBrewingTimeSeconds); // Форматированное время варки // Formatted brewing time

    // Расчет скорректированных значений эффекта и длительности // Calculate adjusted effect value and duration
    $effectMultiplier = 1 + ($levelInt - 1) * 0.1667; // Множитель силы эффекта // Effect strength multiplier
    $durationMultiplier = 1 + ($levelInt - 1) * 0.25; // Множитель длительности // Duration multiplier
    $adjustedEffectValue = round(($recipe->effect_value ?? 0) * $effectMultiplier); // Скорректированное значение // Adjusted value
    $adjustedDurationSeconds = round(($recipe->effect_duration ?? 0) * $durationMultiplier); // Скорректированная длительность в секундах // Adjusted duration in seconds
    $formattedDuration = formatDetailedTime($adjustedDurationSeconds); // Форматированная длительность // Formatted duration

?>



<h2 class="text-xl font-bold mb-4 <?php echo e($qualityClass); ?>"><?php echo e($recipe->name); ?></h2>



<div class="flex items-center mb-4">
    
    
    <div
        class="relative w-16 h-16 mr-4 bg-[#252117] rounded-md border-2 border-[#a6925e] overflow-hidden flex items-center justify-center shadow-md flex-shrink-0">
        
        <img src="<?php echo e($recipe->icon_path); ?>" alt="<?php echo e($recipe->name); ?>" class="w-14 h-14 object-contain"
            style="image-rendering: crisp-edges;">
    </div>
    
    
    <div class="text-sm">
        
        
        <p><span class="font-semibold text-[#bda56a]">Качество:</span> <span
                class="<?php echo e($qualityClass); ?>"><?php echo e($recipe->quality); ?></span></p>
        
        
        <p><span class="font-semibold text-[#bda56a]">Уровень:</span> <?php echo e($levelInt); ?></p>
        
        
        <p><span class="font-semibold text-[#bda56a]">Треб. алхимия:</span> <?php echo e($recipe->min_alchemy_level); ?></p>
        
        
        <p><span class="font-semibold text-[#bda56a]">Время варки:</span> <?php echo e($formattedBrewingTime); ?></p>
    </div>
</div>



<p class="text-sm mb-4 italic">"<?php echo e($recipe->description ?? 'Описание отсутствует.'); ?>"</p>



<div class="mb-4 p-3 bg-[#2a2621] rounded border border-[#514b3c]">
    <h3 class="text-md font-semibold mb-2 text-[#e5b769]">Эффекты готового зелья:</h3>
    <ul class="list-disc list-inside text-sm space-y-1">
        
        
        <li><?php echo e($recipe->effect ?? 'Неизвестный эффект'); ?>: <?php echo e($adjustedEffectValue); ?> 
            <?php if($adjustedDurationSeconds > 0): ?> 
                <span class="text-gray-400">(Длительность: <?php echo e($formattedDuration); ?>)</span> 
            <?php endif; ?>
        </li>
        
        
    </ul>
</div>



<div class="mb-4">
    <h3 class="text-md font-semibold mb-2 text-[#e5b769]">Ингредиенты:</h3>
    <?php if($recipe->ingredients->isNotEmpty()): ?> 
        <ul class="space-y-2">
            <?php $__currentLoopData = $recipe->ingredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="flex items-center text-sm p-2 bg-[#2f2d2b] rounded border border-[#4a452c]">
                    
                    
                    <img src="<?php echo e($ingredient->icon_path); ?>" alt="<?php echo e($ingredient->name); ?>"
                        class="w-6 h-6 mr-2 object-contain flex-shrink-0" style="image-rendering: crisp-edges;">
                    
                    
                    <span><?php echo e($ingredient->name); ?></span>
                    <span class="ml-auto text-[#e5b769] font-medium"><?php echo e($ingredient->pivot->quantity); ?> шт.</span> 
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php else: ?>
        
        
        <p class="text-sm text-gray-400">Ингредиенты не указаны.</p>
    <?php endif; ?>
</div>



<div class="mt-4 text-right p-3 bg-[#2a2621] rounded border border-[#514b3c]">
    <span class="text-lg font-semibold text-[#e5b769]">Цена:</span>
    <div class="flex items-center space-x-2 justify-end mt-1">
        <?php if($recipe->price_gold > 0): ?>
            <div class="flex items-center">
                <img src="<?php echo e(asset('assets/goldIcon.png')); ?>" alt="Золото" class="w-5 h-5 mr-1">
                <span class="text-md font-medium text-[#e5b769]"><?php echo e($recipe->price_gold); ?></span>
            </div>
        <?php endif; ?>

        <?php if($recipe->price_silver > 0): ?>
            <div class="flex items-center">
                <img src="<?php echo e(asset('assets/silverIcon.png')); ?>" alt="Серебро" class="w-5 h-5 mr-1">
                <span class="text-md font-medium text-[#c0c0c0]"><?php echo e($recipe->price_silver); ?></span>
            </div>
        <?php endif; ?>

        <?php if($recipe->price_bronze > 0): ?>
            <div class="flex items-center">
                <img src="<?php echo e(asset('assets/bronzeIcon.png')); ?>" alt="Бронза" class="w-5 h-5 mr-1">
                <span class="text-md font-medium text-[#cd7f32]"><?php echo e($recipe->price_bronze); ?></span>
            </div>
        <?php endif; ?>
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/shop/partials/recipe_details.blade.php ENDPATH**/ ?>