<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?> 

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>Магазин - Рецепты | <?php echo e(Auth::check() ? Auth::user()->name : ($user->name ?? 'Гость')); ?></title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>



<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    
    <div
        class="flex-grow container max-w-md mx-auto px-1 py-0 bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden">

        
        <div class=""> 
            <div class="flex justify-between items-center text-[#d9d3b8] rounded-md pt-1.5 shadow-inner">
                
                <div class="flex items-center">
                    <div
                        class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] mr-1">
                        
                        <span class="text-[#FF6347] text-xs">❤️</span>
                    </div>
                    <div class="flex flex-col">
                        
                        <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                            <div class="bg-gradient-to-r from-[#8B0000] to-[#FF6347] h-full rounded-full"
                                style="width: calc(<?php echo e($actualResources['current_hp'] ?? 0); ?>/<?php echo e($userProfile->max_hp ?? 1); ?>*100%)">
                                
                            </div>
                        </div>
                        
                        <span
                            class="text-[#e5b769] text-[12px]"><?php echo e($actualResources['current_hp'] ?? 0); ?>/<?php echo e($userProfile->max_hp ?? '?'); ?></span>
                    </div>
                </div>

                
                <div class="flex space-x-1">
                    
                    <a href="/mail" class="relative group">
                        <div
                            class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                            <span class="block text-xs">📩</span>
                        </div>
                        
                        
                    </a>
                    
                    <a href="/events" class="relative group">
                        <div
                            class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                            <span class="block text-xs">🎉</span>
                        </div>
                        
                        
                    </a>
                    
                    <a href="/quests" class="relative group">
                        <div
                            class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                            <span class="block text-xs">📜</span>
                        </div>
                        
                        
                    </a>
                </div>

                
                <div class="flex items-center">
                    <div class="flex flex-col items-end">
                        
                        <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                            <div class="bg-gradient-to-r from-[#00008B] to-[#1E90FF] h-full rounded-full"
                                style="width: calc(<?php echo e($userProfile->mp ?? 0); ?>/<?php echo e($userProfile->max_mp ?? 1); ?>*100%)">
                                
                            </div>
                        </div>
                        
                        <span
                            class="text-[#e5b769] text-[12px]"><?php echo e($userProfile->mp ?? 0); ?>/<?php echo e($userProfile->max_mp ?? '?'); ?></span>
                    </div>
                    <div
                        class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] ml-1">
                        
                        <span class="text-[#1E90FF] text-xs">🔮</span>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="w-full bg-[#6b6658] mt-0.5 h-0.5 rounded-full mb-5"> 
            <?php
                // Используем те же переменные из блока опыта выше
                $experienceProgress = $experienceProgress ?? ['current_experience' => 0, 'next_experience' => 1, 'percentage' => 0];
                $percentage = $experienceProgress['percentage'];
            ?>
            
            <div class="bg-gradient-to-r from-[#a6925e] to-[#e5b769] h-full rounded-full transition-width duration-500"
                style="width: <?php echo e($percentage); ?>%;"></div>

            <div class="flex gap-1 items-center "> 
                
                <div class="flex items-center ">
                    <img src="<?php echo e(asset('assets/goldIcon.png')); ?>" alt="Золото" class="w-4 h-4"> 
                    <span class="text-sm font-medium text-[#e5b769]">
                        
                        <?php echo e(number_format($userProfile->gold ?? 0, 0, ',', ' ')); ?>

                    </span>
                </div>
                
                <div class="flex items-center">
                    <img src="<?php echo e(asset('assets/silverIcon.png')); ?>" alt="Серебро" class="w-4 h-4 mr-0.5"> 
                    <span class="text-sm font-medium text-[#c0c0c0]">
                        
                        <?php echo e(number_format($userProfile->silver ?? 0, 0, ',', ' ')); ?>

                    </span>
                </div>
                
                <div class="flex items-center">
                    <img src="<?php echo e(asset('assets/bronzeIcon.png')); ?>" alt="Бронза" class="w-4 h-4 mr-0.5"> 
                    <span class="text-sm font-medium text-[#cd7f32]">
                        
                        <?php echo e(number_format($userProfile->bronze ?? 0, 0, ',', ' ')); ?>

                    </span>
                </div>
            </div>
        </div>

        
        <div class=""> 
            
            <?php if (isset($component)) { $__componentOriginal360d002b1b676b6f84d43220f22129e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal360d002b1b676b6f84d43220f22129e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumbs','data' => ['breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $attributes = $__attributesOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__attributesOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $component = $__componentOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__componentOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>

            
            <div class="location-name">
                <span>Магазин - Рецепты</span>
            </div>
        </div>

        
        <?php if(session('success')): ?>
            <div class="bg-green-500 text-white p-4 rounded mb-4 text-center mt-4">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="bg-red-500 text-white p-4 rounded mb-4 text-center mt-4">
                <?php echo e(session('error')); ?>

            </div>
        <?php endif; ?>

        
        <div
            class="w-full h-20 bg-[#3b3a33] rounded-lg mt-4 flex items-center justify-center border border-[#a6925e] shadow-lg">
            <img src="<?php echo e(asset('assets/bannerShop.jpg')); ?>" alt="Изображение локации"
                class="w-full h-full object-cover rounded-lg opacity-75">
        </div>

        
        <div class="flex flex-wrap justify-center gap-3 mt-2 mb-6">
            <a href="<?php echo e(route('shop.index')); ?>"
                class="relative overflow-hidden bg-gradient-to-b <?php echo e(request()->routeIs('shop.index') || request()->routeIs('shop.item.details') ? 'from-[#c4a76d] to-[#a6925e]' : 'from-[#514b3c] to-[#38352c]'); ?> py-2 px-4 rounded border <?php echo e(request()->routeIs('shop.index') || request()->routeIs('shop.item.details') ? 'border-[#d4b781]' : 'border-[#514b3c]'); ?> shadow-md transition-all duration-300 group hover:from-[#d4b781] hover:to-[#b7a36f] hover:border-[#d4b781]"
                style="min-width: 100px; text-align: center;">
                <span class="relative z-10 text-[#2f2d2b] font-medium">Вещи</span>
                <span
                    class="absolute inset-0 bg-gradient-to-r from-transparent via-[#f0d89e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
            </a>
            <a href="<?php echo e(route('shop.recipes')); ?>"
                class="relative overflow-hidden bg-gradient-to-b <?php echo e(request()->routeIs('shop.recipes') ? 'from-[#c4a76d] to-[#a6925e]' : 'from-[#514b3c] to-[#38352c]'); ?> py-2 px-4 rounded border <?php echo e(request()->routeIs('shop.recipes') ? 'border-[#d4b781]' : 'border-[#514b3c]'); ?> shadow-md transition-all duration-300 group hover:from-[#d4b781] hover:to-[#b7a36f] hover:border-[#d4b781]"
                style="min-width: 100px; text-align: center;">
                <span class="relative z-10 text-[#2f2d2b] font-medium">Рецепты</span>
                <span
                    class="absolute inset-0 bg-gradient-to-r from-transparent via-[#f0d89e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
            </a>
            
            <div class="relative overflow-hidden bg-gradient-to-b from-[#6b6658] to-[#5a5449] py-2 px-4 rounded border border-[#6b6658] shadow-md cursor-not-allowed opacity-70"
                style="min-width: 100px; text-align: center;">
                <span class="relative z-10 text-[#a8a090] font-medium">Ресурсы</span>
                <span
                    class="absolute -top-1 -right-1 bg-[#a6925e] text-[#2f2d2b] text-xs px-1 py-0.5 rounded-md font-bold animate-pulse z-20">
                    В разработке
                </span>
            </div>
        </div>

        
        <div class="relative mt-4 mb-6">
            
            <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-32 h-1">
                <div class="w-full h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
                </div>
            </div>

            
            <h3 class="text-center text-[#e5b769] font-medium mb-3 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                        clip-rule="evenodd" />
                </svg>
                Фильтр рецептов
            </h3>

            
            <div
                class="relative bg-gradient-to-b from-[#312e25] to-[#1d1b16] rounded-lg p-4 border border-[#a6925e] shadow-lg overflow-hidden">
                
                <div
                    class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>
                <div
                    class="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>
                <div
                    class="absolute inset-y-0 left-0 w-px bg-gradient-to-b from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>
                <div
                    class="absolute inset-y-0 right-0 w-px bg-gradient-to-b from-transparent via-[#e5b769] to-transparent opacity-40">
                </div>

                
                <form id="filterForm" method="GET" action="<?php echo e(route('shop.recipes')); ?>" class="relative z-10">
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2">
                        
                        <div class="group">
                            <label for="recipeType"
                                class="block text-sm font-medium text-[#e5b769] mb-1.5 transition-colors duration-300 group-hover:text-[#f0d89e]">Тип
                                рецепта</label>
                            <div class="relative">
                                <select id="recipeType" name="type"
                                    class="w-full bg-[#252117] text-[#d9d3b8] py-2 px-3 pr-8 rounded border border-[#514b3c] focus:border-[#a6925e] focus:ring focus:ring-[#a6925e] focus:ring-opacity-30 appearance-none transition-all duration-300 text-sm"
                                    disabled>
                                    <option value="">Все</option>
                                    
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a6925e]" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        
                        <div class="group">
                            <label for="level"
                                class="block text-sm font-medium text-[#e5b769] mb-1.5 transition-colors duration-300 group-hover:text-[#f0d89e]">Уровень</label>
                            <div class="relative">
                                <select id="level" name="level"
                                    class="w-full bg-[#252117] text-[#d9d3b8] py-2 px-3 pr-8 rounded border border-[#514b3c] focus:border-[#a6925e] focus:ring focus:ring-[#a6925e] focus:ring-opacity-30 appearance-none transition-all duration-300 text-sm"
                                    disabled>
                                    <option value="">Все уровни</option>
                                    
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a6925e]" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    
                    <div class="flex justify-center space-x-4 mt-5">
                        
                        <button type="submit"
                            class="relative overflow-hidden bg-gradient-to-b from-[#a6925e] to-[#8b7c4b] text-[#2f2d2b] py-2 px-6 rounded font-medium border border-[#c4a76d] hover:from-[#c4a76d] hover:to-[#a6925e] transition-all duration-300 shadow-md group"
                            disabled>
                            <span class="relative z-10 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                                        clip-rule="evenodd" />
                                </svg>
                                <span>Применить</span>
                            </span>
                            <span
                                class="absolute inset-0 bg-gradient-to-r from-transparent via-[#f0d89e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
                        </button>

                        
                        <a href="<?php echo e(route('shop.recipes')); ?>"
                            class="relative overflow-hidden bg-gradient-to-b from-[#38352c] to-[#292722] text-[#d9d3b8] py-2 px-6 rounded font-medium border border-[#514b3c] hover:from-[#45413a] hover:to-[#33312c] hover:text-[#e5b769] transition-all duration-300 shadow-md group">
                            <span class="relative z-10 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                                        clip-rule="evenodd" />
                                </svg>
                                <span>Сбросить</span>
                            </span>
                            <span
                                class="absolute inset-0 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
                        </a>
                    </div>
                </form>
            </div>

            
            <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-32 h-1">
                <div class="w-full h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
                </div>
            </div>
        </div>

        
        <div class="mt-6">
            <h1 class="text-3xl font-bold text-[#e5b769] mb-6 text-center">Список рецептов</h1>

            <?php if($recipes->isEmpty()): ?>
                <div class="bg-[#3b3a33] border border-[#a6925e] rounded-lg p-8 shadow-lg text-center">
                    <p class="text-[#d9d3b8]">Рецепты не найдены. Попробуйте изменить параметры фильтра.</p>
                </div>
            <?php else: ?>
                
                <div class="grid grid-cols-1 gap-6 max-w-md mx-auto">
                    <?php $__currentLoopData = $recipes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recipe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            // Определение цвета качества рецепта // Define recipe quality color
                            $qualityColors = [
                                'Обычное' => 'text-gray-200',
                                'Необычное' => 'text-green-400',
                                'Редкое' => 'text-blue-400',
                                'Эпическое' => 'text-purple-400',
                                'Легендарное' => 'text-orange-400',
                            ];
                            $qualityColor = $qualityColors[$recipe->quality] ?? 'text-gray-200'; // Устанавливаем цвет // Set color
                            // Расчет времени варки с учетом уровня рецепта (целое число) // Calculate brewing time based on recipe level (integer)
                            $levelInt = intval($recipe->level); // Преобразуем уровень в число // Convert level to integer
                            $brewingTimeReduction = max(30, 120 - ($levelInt - 1) * 15); // Уменьшение времени варки (мин), минимум 30 // Brewing time reduction (min), minimum 30
                            // Форматирование времени варки // Format brewing time
                            $baseBrewingTimeSeconds = $recipe->brewing_time ?? 0; // Базовое время в секундах // Base time in seconds
                            $modifier = $recipe->brewing_time_modifier ?? 1; // Модификатор времени // Time modifier
                            $actualBrewingTimeSeconds = ceil($baseBrewingTimeSeconds * $modifier); // Реальное время с модификатором // Actual time with modifier
                            // Применяем уменьшение времени варки в зависимости от уровня // Apply brewing time reduction based on level
                            $finalBrewingTimeSeconds = min($actualBrewingTimeSeconds, $brewingTimeReduction * 60); // Окончательное время в секундах, конвертируем минуты уменьшения в секунды // Final time in seconds, convert reduction minutes to seconds
                            // Используем хелпер-функцию для форматирования времени варки
                            $formattedBrewingTime = formatBrewingTime($finalBrewingTimeSeconds); // Получаем форматированное время // Get formatted time
                            // Получение описания эффекта из рецепта // Get effect description from recipe
                            $effectDescription = $recipe->effect ?? 'Нет описания эффекта'; // Default text if no description
                        ?>
                        
                        
                        <div
                            class="relative rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-[1.02]">
                            
                            
                            <div class="absolute inset-0 bg-gradient-to-br from-[#2a2621] to-[#1a1814] opacity-95"></div>

                            
                            
                            <div class="absolute inset-0 rounded-lg border-2 border-[#a6925e] pointer-events-none"></div>
                            
                            
                            <div
                                class="absolute inset-x-0 top-0 h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
                            </div>
                            
                            
                            <div
                                class="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-60">
                            </div>

                            
                            
                            <div class="relative p-4">
                                
                                
                                <div class="flex items-start space-x-3 mb-3">
                                    
                                    
                                    <div class="relative">
                                        
                                        
                                        <div
                                            class="absolute inset-0 rounded-md bg-gradient-to-br from-[#3b3a33] to-[#252117] blur-[1px]">
                                        </div>
                                        
                                        
                                        <div
                                            class="relative w-16 h-16 bg-[#252117] rounded-md border-2 border-[#a6925e] overflow-hidden flex items-center justify-center shadow-md">

                                            
                                            <img src="<?php echo e($recipe->icon_path); ?>" alt="<?php echo e($recipe->name); ?>"
                                                class="w-14 h-14 object-contain" style="image-rendering: crisp-edges;">
                                        </div>
                                    </div>

                                    
                                    
                                    <div class="flex-1">
                                        
                                        
                                        <h3 class="text-base font-bold <?php echo e($qualityColor); ?> leading-tight mb-1"
                                            style="text-shadow: 0px 1px 3px rgba(0,0,0,0.8);"><?php echo e($recipe->name); ?></h3>

                                        
                                        
                                        <div class="flex items-center text-xs mb-1.5 text-[#d9d3b8]">
                                            <span
                                                class="px-1.5 py-0.5 bg-[#38352c] rounded border border-[#514b3c] mr-1.5">Зелье</span>
                                            
                                            <span class="px-1.5 py-0.5 bg-[#38352c] rounded border border-[#514b3c] mr-1.5">Ур:
                                                <?php echo e($levelInt); ?></span> 
                                            
                                            
                                            <?php if($recipe->effect): ?>
                                                <span
                                                    class="px-1.5 py-0.5 bg-[#4a3a3a] text-[#ffb3b3] rounded border border-[#a65e5e]"><?php echo e($recipe->effect); ?></span>
                                                
                                            <?php endif; ?>
                                        </div>

                                        
                                        
                                        <div class="flex flex-col"> 
                                            
                                            <div class="text-xs text-[#e5b769] mt-0.5">
                                                
                                                
                                                <i class="fas fa-flask text-[10px] mr-0.5"></i>
                                                Алхимия: <span class="font-medium"><?php echo e($recipe->min_alchemy_level); ?></span> 
                                            </div>
                                            <div class="text-xs text-[#e5b769] mt-0.5">
                                                
                                                
                                                <i class="fas fa-clock text-[10px] mr-0.5"></i>
                                                Время: <span class="font-medium"><?php echo e($formattedBrewingTime); ?></span> 
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                
                                <div class="flex items-center justify-between mb-3">
                                    <div
                                        class="flex items-center space-x-2 px-2 py-1 bg-[#2a2621] rounded border border-[#514b3c]">
                                        <span class="text-xs text-[#9a9483]">Цена:</span>
                                        <div class="flex items-center space-x-1">
                                            <?php if($recipe->price_gold > 0): ?>
                                                <div class="flex items-center">
                                                    <img src="<?php echo e(asset('assets/goldIcon.png')); ?>" alt="Золото"
                                                        class="w-4 h-4 mr-0.5">
                                                    <span
                                                        class="text-xs font-medium text-[#e5b769]"><?php echo e($recipe->price_gold); ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <?php if($recipe->price_silver > 0): ?>
                                                <div class="flex items-center">
                                                    <img src="<?php echo e(asset('assets/silverIcon.png')); ?>" alt="Серебро"
                                                        class="w-4 h-4 mr-0.5">
                                                    <span
                                                        class="text-xs font-medium text-[#c0c0c0]"><?php echo e($recipe->price_silver); ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <?php if($recipe->price_bronze > 0): ?>
                                                <div class="flex items-center">
                                                    <img src="<?php echo e(asset('assets/bronzeIcon.png')); ?>" alt="Бронза"
                                                        class="w-4 h-4 mr-0.5">
                                                    <span
                                                        class="text-xs font-medium text-[#cd7f32]"><?php echo e($recipe->price_bronze); ?></span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 rounded-full mr-1 <?php echo e(str_replace('text', 'bg', $qualityColor)); ?>">
                                        </div>
                                        <span class="text-[11px] <?php echo e($qualityColor); ?>"><?php echo e($recipe->quality); ?></span>
                                    </div>
                                </div>

                                
                                
                                <div class="grid grid-cols-2 gap-2">
                                    
                                    
                                    <form action="<?php echo e(route('shop.buy.recipe', $recipe)); ?>" method="POST">
                                        <?php echo csrf_field(); ?> 
                                        <button type="submit"
                                            class="w-full bg-gradient-to-b from-[#c4a76d] to-[#a6925e] text-[#2f2d2b] py-2 px-3 text-sm font-medium rounded shadow-md hover:from-[#d4b781] hover:to-[#b7a36f] transition-all duration-300 border border-[#d4b781] flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                                fill="currentColor"> 
                                                <path
                                                    d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3z">
                                                </path>
                                            </svg>
                                            Купить 
                                        </button>
                                    </form>

                                    
                                    
                                    <a href="javascript:void(0);" onclick="showRecipeDetails(<?php echo e($recipe->id); ?>)"
                                        class="w-full bg-gradient-to-b from-[#514b3c] to-[#38352c] text-[#d9d3b8] py-2 px-3 text-sm font-medium rounded shadow-md hover:from-[#5d5745] hover:to-[#433f35] transition-all duration-300 border border-[#514b3c] flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20"
                                            fill="currentColor"> 
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                            <path fill-rule="evenodd"
                                                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        Подробнее 
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php endif; ?>
        </div>

        
        <?php if($recipes->hasPages()): ?>
            <div class="mt-6 mb-4 flex justify-center items-center">
                <div class="relative">
                    <div
                        class="relative bg-gradient-to-b from-[#312e25] to-[#1a1814] rounded-lg px-5 py-3 border border-[#a6925e] shadow-md transform-gpu before:absolute before:inset-0 before:bg-gradient-to-t before:from-transparent before:to-[rgba(229,183,105,0.10)] before:rounded-lg before:pointer-events-none before:opacity-80 after:absolute after:inset-0 after:rounded-lg after:shadow-[inset_0_1px_2px_rgba(255,255,255,0.15),0_3px_8px_rgba(0,0,0,0.4)] after:pointer-events-none">
                        <div
                            class="absolute top-[6px] left-3 right-3 h-[1px] bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60">
                        </div>
                        <div
                            class="absolute bottom-[6px] left-3 right-3 h-[1px] bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-60">
                        </div>

                        <div class="flex items-center justify-center">
                            <?php if($recipes->onFirstPage()): ?>
                                <button disabled
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70 mr-2">
                                    <span class="text-lg font-medium">←</span>
                                </button>
                            <?php else: ?>
                                <a href="<?php echo e($recipes->previousPageUrl()); ?>"
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#514b3c] bg-[#252117] hover:bg-[#2d2820] hover:border-[#a6925e] transition-all duration-300 text-[#a6925e] mr-2 hover:text-[#e5b769]">
                                    <span class="text-lg font-medium">←</span>
                                </a>
                            <?php endif; ?>

                            <div class="flex items-center justify-center">
                                <?php $__currentLoopData = $recipes->getUrlRange(1, $recipes->lastPage()); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="<?php echo e($url); ?>"
                                        class="relative w-10 h-10 flex items-center justify-center mx-1 rounded <?php echo e($page == $recipes->currentPage() ? 'border border-[#e5b769] bg-[#2d2820] text-[#e5b769] font-medium shadow-[inset_0_1px_3px_rgba(0,0,0,0.3)]' : 'border border-[#514b3c] bg-[#252117] text-[#a6925e] hover:bg-[#2d2820] hover:border-[#a6925e] hover:text-[#e5b769]'); ?> transition-colors duration-300">
                                        <?php if($page == $recipes->currentPage()): ?>
                                            <div
                                                class="absolute top-0 left-0 w-[5px] h-[5px] border-t border-l border-[#e5b769] opacity-80">
                                            </div>
                                            <div
                                                class="absolute top-0 right-0 w-[5px] h-[5px] border-t border-r border-[#e5b769] opacity-80">
                                            </div>
                                            <div
                                                class="absolute bottom-0 left-0 w-[5px] h-[5px] border-b border-l border-[#e5b769] opacity-80">
                                            </div>
                                            <div
                                                class="absolute bottom-0 right-0 w-[5px] h-[5px] border-b border-r border-[#e5b769] opacity-80">
                                            </div>
                                            <span class="text-lg"
                                                style="text-shadow: 0 0 4px rgba(229, 183, 105, 0.5);"><?php echo e($page); ?></span>
                                        <?php else: ?>
                                            <span class="text-lg"><?php echo e($page); ?></span>
                                        <?php endif; ?>
                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            <?php if($recipes->hasMorePages()): ?>
                                <a href="<?php echo e($recipes->nextPageUrl()); ?>"
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#514b3c] bg-[#252117] hover:bg-[#2d2820] hover:border-[#a6925e] transition-all duration-300 text-[#a6925e] ml-2 hover:text-[#e5b769]">
                                    <span class="text-lg font-medium">→</span>
                                </a>
                            <?php else: ?>
                                <button disabled
                                    class="w-10 h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70 ml-2">
                                    <span class="text-lg font-medium">→</span>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mb-4">
                <div class="inline-block px-3 py-1 bg-[#1a1814] rounded border-t border-b border-[#514b3c]">
                    <span class="text-[#9a9483] text-xs">
                        Страница <span class="text-[#e5b769]"><?php echo e($recipes->currentPage()); ?></span> из <span
                            class="text-[#e5b769]"><?php echo e($recipes->lastPage()); ?></span>
                        <span class="inline-block mx-1 text-[#514b3c]">•</span>
                        Всего: <span class="text-[#e5b769]"><?php echo e($recipes->total()); ?></span>
                    </span>
                </div>
            </div>
        <?php endif; ?>
    </div> 

    
    <div id="recipeDetailsModal"
        class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div
            class="bg-[#3b3a33] border-2 border-[#a6925e] rounded-lg p-6 max-w-xs w-full max-h-[90vh] overflow-y-auto mx-4">
            <div id="modalContent" class="text-[#d9d3b8]">
                
            </div>
            <div class="mt-6 text-center">
                <button id="closeModal"
                    class="bg-[#6b6658] text-[#f5f5f5] py-2 px-4 rounded hover:bg-[#807c70] transition duration-300">
                    Закрыть
                </button>
            </div>
        </div>
    </div>

    
    <script>
        // Функция для отображения модального окна с деталями рецепта
        function showRecipeDetails(recipeId) {
            // Получаем детали рецепта через AJAX
            fetch(`/shop/recipe-details/${recipeId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.html) {
                        document.getElementById('modalContent').innerHTML = data.html;
                        document.getElementById('recipeDetailsModal').classList.remove('hidden');
                    } else {
                        document.getElementById('modalContent').innerHTML = '<p class="text-red-400">Не удалось загрузить детали рецепта.</p>';
                        document.getElementById('recipeDetailsModal').classList.remove('hidden');
                        console.error('Ошибка: Отсутствует HTML в ответе от /shop/recipe-details/', data);
                    }
                })
                .catch(error => {
                    console.error('Ошибка при получении деталей рецепта:', error);
                    document.getElementById('modalContent').innerHTML = `<p class="text-red-400">Ошибка загрузки: ${error.message}</p>`;
                    document.getElementById('recipeDetailsModal').classList.remove('hidden');
                });
        }

        document.getElementById('closeModal').addEventListener('click', function () {
            document.getElementById('recipeDetailsModal').classList.add('hidden');
        });

        document.getElementById('recipeDetailsModal').addEventListener('click', function (e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });

        // Добавляем JavaScript для подтверждения покупки всем формам покупки рецепта
        document.addEventListener('DOMContentLoaded', function () {
            // Находим все формы покупки рецептов
            const purchaseForms = document.querySelectorAll('form[action*="shop/buy-recipe"]');

            purchaseForms.forEach(form => {
                form.addEventListener('submit', function (e) {
                    e.preventDefault(); // Предотвращаем стандартную отправку формы

                    // Создаем модальное окно подтверждения в стиле проекта
                    const modal = document.createElement('div');
                    modal.className = 'fixed inset-0 flex items-center justify-center z-50';
                    modal.innerHTML = `
                        <div class="fixed inset-0 bg-black bg-opacity-70" onclick="this.parentNode.remove()"></div>
                        <div class="bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-xl p-6 max-w-sm mx-auto relative z-10 transform transition-all">
                            <div class="text-center mb-4">
                                <h3 class="text-xl font-bold text-[#e5b769]">Подтверждение покупки</h3>
                                <p class="text-[#d9d3b8] mt-2">Вы уверены, что хотите приобрести этот рецепт?</p>
                            </div>
                            <div class="flex justify-center space-x-3 mt-4">
                                <button type="button" class="px-4 py-2 bg-gradient-to-b from-[#514b3c] to-[#38352c] text-[#d9d3b8] rounded border border-[#514b3c] hover:from-[#5d5745] hover:to-[#433f35] transition-all" onclick="this.closest('.fixed').remove()">Отмена</button>
                                <button type="button" id="confirmPurchase" class="px-4 py-2 bg-gradient-to-b from-[#c4a76d] to-[#a6925e] text-[#2f2d2b] rounded border border-[#d4b781] hover:from-[#d4b781] hover:to-[#b7a36f] transition-all">Подтвердить</button>
                            </div>
                        </div>
                    `;

                    document.body.appendChild(modal);

                    // Обработчик для кнопки подтверждения
                    document.getElementById('confirmPurchase').addEventListener('click', function () {
                        modal.remove(); // Удаляем модальное окно
                        form.submit(); // Отправляем форму
                    });
                });
            });
        });
    </script>

    
    <div class="container mx-auto text-center px-2 py-2 flex justify-center space-x-2">
        
        <a href="<?php echo e(route('inventory.index')); ?>"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">Рюкзак</a>
        
        <a href="<?php echo e(route('user.profile')); ?>"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">Персонаж</a>
        
        <a href="#"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">Гильдия</a>
    </div>

    
    <?php if(Auth::check()): ?> 
        
        <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
    <?php endif; ?>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/shop/recipes.blade.php ENDPATH**/ ?>