<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Jobs\MineAutoAttackJob;
use App\Services\MineDetectionService;
use App\Services\MineTargetDistributionService;
use App\Services\BattleLogService;
use App\Services\PlayerHealthService;
use App\Services\CombatFormulaService;
use App\Services\LogFormattingService;

class TestMineAutoAttackStun extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:mine-auto-attack-stun {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Тестирует MineAutoAttackJob со скиллом стана';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');

        // Получаем пользователя
        $user = $userId ? User::find($userId) : User::first();
        if (!$user) {
            $this->error("Пользователь не найден!");
            return 1;
        }

        $this->info("🧪 Тестируем MineAutoAttackJob со скиллом стана для: {$user->name}");

        // Находим локацию рудника
        $mineLocation = MineLocation::first();
        if (!$mineLocation) {
            $this->error("Локация рудника не найдена!");
            return 1;
        }

        $this->info("✅ Найдена локация рудника: {$mineLocation->name}");

        // Создаем или обновляем метку обнаружения для игрока
        $this->info("📍 Создаем метку обнаружения...");

        $mark = MineMark::updateOrCreate(
            [
                'player_id' => $user->id,
                'mine_location_id' => $mineLocation->id,
            ],
            [
                'location_id' => $mineLocation->location_id,
                'location_name' => $mineLocation->name,
                'is_active' => true,
                'expires_at' => now()->addMinutes(30),
                'last_attack_at' => now()->subMinutes(5), // Позволяем атаку
                'attack_count' => 0
            ]
        );

        $this->info("✅ Метка обнаружения создана/обновлена");

        // Обновляем статистику игрока, чтобы он находился в нужной локации
        if ($user->statistics) {
            $user->statistics->update(['current_location' => $mineLocation->name]);
            $this->info("✅ Локация игрока обновлена");
        }

        // Убеждаемся, что у игрока есть HP
        if ($user->profile->hp <= 0) {
            $user->profile->update(['hp' => $user->profile->max_health]);
            $this->info("✅ HP игрока восстановлено");
        }

        // Получаем логи до атаки
        $battleLogService = app(BattleLogService::class);
        $battleLogKey = "battle_logs:mines:{$user->id}";
        $logsBefore = $battleLogService->getLogs($battleLogKey);
        $logsCountBefore = count($logsBefore);

        $this->info("📋 Логов в журнале до атаки: {$logsCountBefore}");

        // Запускаем MineAutoAttackJob
        $this->info("\n⚔️ Запускаем MineAutoAttackJob...");

        $job = new MineAutoAttackJob();
        $job->handle(
            app(MineDetectionService::class),
            app(MineTargetDistributionService::class),
            $battleLogService,
            app(PlayerHealthService::class),
            app(CombatFormulaService::class),
            app(LogFormattingService::class)
        );

        $this->info("✅ MineAutoAttackJob выполнен");

        // Получаем логи после атаки
        $logsAfter = $battleLogService->getLogs($battleLogKey);
        $logsCountAfter = count($logsAfter);

        $this->info("📋 Логов в журнале после атаки: {$logsCountAfter}");

        if ($logsCountAfter > $logsCountBefore) {
            $this->info("🎉 Новые логи добавлены!");

            // Показываем новые логи
            $newLogsCount = $logsCountAfter - $logsCountBefore;
            $this->info("📝 Новых логов: {$newLogsCount}");

            for ($i = 0; $i < $newLogsCount && $i < count($logsAfter); $i++) {
                $log = $logsAfter[$i];
                $logNumber = $i + 1;
                $this->info("   Лог {$logNumber}:");
                $this->info("   Тип: " . ($log['type'] ?? 'unknown'));
                $this->info("   Время: " . ($log['timestamp'] ?? 'unknown'));
                $this->line("   Сообщение: " . strip_tags($log['message'] ?? ''));
                $this->line("   HTML: " . ($log['message'] ?? ''));

                // Проверяем, содержит ли лог информацию о стане
                if (strpos($log['message'] ?? '', 'оглушает') !== false) {
                    $this->info("   🎯 НАЙДЕН ЛОГ СО СТАНОМ!");

                    // Проверяем, что урон НЕ отображается
                    if (!preg_match('/\d+.*Вам/', strip_tags($log['message'] ?? ''))) {
                        $this->info("   ✅ Урон НЕ отображается - правильно!");
                    } else {
                        $this->warn("   ⚠️ Урон все еще отображается - нужно исправить");
                    }
                }

                $this->info("");
            }
        } else {
            $this->warn("⚠️ Новые логи не добавлены");
            $this->info("💡 Возможные причины:");
            $this->info("   - Моб не атаковал (случайность)");
            $this->info("   - Скилл стана не активировался (шанс)");
            $this->info("   - Игрок не находится в нужной локации");
        }

        // Показываем информацию о метке
        $mark->refresh();
        $this->info("\n📊 Информация о метке после атаки:");
        $this->info("   Активна: " . ($mark->is_active ? 'да' : 'нет'));
        $this->info("   Последняя атака: " . ($mark->last_attack_at ?? 'никогда'));
        $this->info("   Количество атак: " . ($mark->attack_count ?? 0));

        return 0;
    }
}
