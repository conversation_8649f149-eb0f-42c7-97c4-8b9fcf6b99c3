<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MobSkillTemplate;
use App\Models\MobSkill;
use App\Models\MineLocation;

class ForceTestMineStun extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:force-mine-stun {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Принудительно создает моба со 100% шансом стана для тестирования';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');

        // Получаем пользователя
        $user = $userId ? User::find($userId) : User::first();
        if (!$user) {
            $this->error("Пользователь не найден!");
            return 1;
        }

        $this->info("🧪 Создаем тестового моба со 100% шансом стана для: {$user->name}");

        // Находим локацию рудника
        $mineLocation = MineLocation::first();
        if (!$mineLocation) {
            $this->error("Локация рудника не найдена!");
            return 1;
        }

        // Находим или создаем шаблон скилла стана
        $stunTemplate = MobSkillTemplate::where('name', 'Тяжелый удар')
            ->where('effect_type', 'stun')
            ->first();

        if (!$stunTemplate) {
            $this->error("Шаблон скилла 'Тяжелый удар' не найден!");
            return 1;
        }

        $this->info("✅ Найден шаблон скилла: {$stunTemplate->name}");

        // Создаем или находим тестового моба
        $testMob = Mob::updateOrCreate(
            ['name' => 'Тестовый Станящий Огр'],
            [
                'slug' => 'test-stunning-ogre',
                'location_id' => $mineLocation->location_id,
                'location' => $mineLocation->name,
                'mob_type' => 'mine',
                'race' => 'ogre',
                'class' => 'warrior',
                'level' => 10,
                'hp' => 100,
                'max_health' => 100,
                'strength' => 15,
                'armor' => 5,
                'experience_reward' => 50,
                'gold_reward' => 25,
                'respawn_time' => 300,
                'death_time' => null,
                'is_boss' => false
            ]
        );

        $this->info("✅ Создан/обновлен тестовый моб: {$testMob->name}");

        // Привязываем скилл стана к мобу со 100% шансом
        $mobSkill = MobSkill::updateOrCreate(
            [
                'mob_id' => $testMob->id,
                'skill_template_id' => $stunTemplate->id
            ],
            [
                'chance' => 100, // 100% шанс активации
                'cooldown' => 0,  // Без кулдауна для тестирования
                'duration' => null // Используем из шаблона
            ]
        );

        $this->info("✅ Скилл стана привязан к мобу со 100% шансом");

        // Тестируем активацию скилла
        $this->info("\n⚔️ Тестируем активацию скилла...");

        $mobSkillService = app(\App\Services\Mine\MobSkillIntegrationService::class);
        $activatedSkills = $mobSkillService->processMobAttackSkills($testMob, $user, [
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'damage_dealt' => 25,
            'attack_type' => 'test_force_stun'
        ]);

        $this->info("🎯 Активированные скиллы: " . count($activatedSkills));

        $hasStunSkill = false;
        foreach ($activatedSkills as $skill) {
            $this->info("   - Тип: " . ($skill['skill_type'] ?? 'unknown'));
            $this->info("   - Сообщение: " . ($skill['message'] ?? 'нет сообщения'));
            $this->info("   - Успех: " . ($skill['success'] ? 'да' : 'нет'));

            if (isset($skill['skill_type']) && $skill['skill_type'] === 'stun') {
                $hasStunSkill = true;
            }
        }

        if ($hasStunSkill) {
            $this->info("\n🎉 УСПЕХ: Скилл стана активировался!");

            // Тестируем форматирование
            $logFormatter = app(\App\Services\LogFormattingService::class);

            $mineAttackMessage = $logFormatter->formatMineDetectionAttack(
                $testMob->name,
                $user->name,
                25,
                $mineLocation->name,
                $activatedSkills
            );

            $this->info("\n📝 Результат formatMineDetectionAttack:");
            $this->line("Текст: " . strip_tags($mineAttackMessage));
            $this->line("HTML: " . $mineAttackMessage);

            // Добавляем в журнал боя
            $battleLogService = app(\App\Services\BattleLogService::class);
            $battleLogKey = "battle_logs:mines:{$user->id}";
            $battleLogService->addLog($battleLogKey, $mineAttackMessage, 'warning');

            $this->info("\n✅ Лог добавлен в журнал боя");
            $this->info("💡 Ключ журнала: {$battleLogKey}");

        } else {
            $this->error("\n❌ ОШИБКА: Скилл стана НЕ активировался даже со 100% шансом!");
            $this->info("🔍 Проверьте настройки скилла и моба");
        }

        return 0;
    }
}
