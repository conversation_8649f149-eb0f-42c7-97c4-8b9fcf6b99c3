<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['events' => []]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['events' => []]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>




<div class="events-section bg-gradient-to-r from-[#2a2621] via-[#312e27] to-[#2a2621] border-2 border-[#a6925e] mt-4 rounded-lg p-4 relative overflow-hidden">
    
    
    <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-[#a6925e]/50 to-transparent"></div>
    <div class="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-[#a6925e]/50 to-transparent"></div>

    <div class="relative z-10">
        
        <div class="flex items-center mb-3">
            <div class="text-xl">📰</div>
            <h3 class="text-[#e5b769] font-semibold text-lg ml-2">Текущие события</h3>
            <div class="flex-1 h-px bg-gradient-to-r from-[#a6925e] to-transparent ml-3"></div>
        </div>

        
        <div class="text-[#d9d3b8] text-sm space-y-3">
            <?php if(count($events) > 0): ?>
                
                <?php $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-start space-x-3">
                        <div class="event-icon text-lg flex-shrink-0 mt-0.5"><?php echo e($event['icon'] ?? '📅'); ?></div>
                        <p class="leading-relaxed"><?php echo $event['text'] ?? ''; ?></p>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                
                <div class="flex items-start space-x-3">
                    <div class="event-icon text-lg flex-shrink-0 mt-0.5">🌅</div>
                    <p class="leading-relaxed">
                        На площади сегодня спокойно. Торговцы предлагают свои товары, а горожане обмениваются последними
                        новостями. Легкий ветерок приносит ароматы из пекарни и звуки молотков из кузницы.
                    </p>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="event-icon text-lg flex-shrink-0 mt-0.5">💰</div>
                    <p class="leading-relaxed">
                        Посетите <a href="<?php echo e(route('market.index')); ?>"
                            class="event-link text-[#e5b769] hover:text-[#f7d08a] transition-colors duration-300 underline decoration-dotted">рынок</a>,
                        чтобы приобрести необходимые товары или продать ненужные вещи.
                    </p>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="event-icon text-lg flex-shrink-0 mt-0.5">🏦</div>
                    <p class="leading-relaxed">
                        Загляните в <a href="<?php echo e(route('bank.index')); ?>"
                            class="event-link text-[#e5b769] hover:text-[#f7d08a] transition-colors duration-300 underline decoration-dotted">банк</a>,
                        чтобы хранить ценные предметы или отправить валюту другим игрокам.
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/events-section.blade.php ENDPATH**/ ?>