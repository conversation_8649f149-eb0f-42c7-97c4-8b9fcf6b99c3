<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Модель для рецептов в магазине
 * Model for recipes in the shop
 */
class ShopRecipe extends Model
{
    use HasFactory;

    /**
     * Атрибуты, которые можно массово назначать
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'potion_recipe_id',
        'price_gold',
        'price_silver',
        'price_bronze',
        'is_available',
        'shop_category',
    ];

    /**
     * Преобразование атрибутов
     * The attributes that should be cast.
     */
    protected $casts = [
        'price_gold' => 'integer',
        'price_silver' => 'integer',
        'price_bronze' => 'integer',
        'is_available' => 'boolean',
    ];

    /**
     * Связь с рецептом зелья
     * Relationship with potion recipe
     */
    public function potionRecipe(): BelongsTo
    {
        return $this->belongsTo(PotionRecipe::class, 'potion_recipe_id');
    }

    /**
     * Получить общую цену в бронзе
     * Get total price in bronze
     */
    public function getTotalPriceInBronze(): int
    {
        return ($this->price_gold * 10000) + ($this->price_silver * 100) + $this->price_bronze;
    }

    /**
     * Проверить, доступен ли рецепт для покупки
     * Check if recipe is available for purchase
     */
    public function isAvailableForPurchase(): bool
    {
        return $this->is_available && $this->potionRecipe && $this->potionRecipe->is_active;
    }

    /**
     * Скоп для получения только доступных рецептов
     * Scope to get only available recipes
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true)
                    ->whereHas('potionRecipe', function ($q) {
                        $q->where('is_active', true);
                    });
    }

    /**
     * Скоп для фильтрации по категории
     * Scope to filter by category
     */
    public function scopeByCategory($query, $category)
    {
        if ($category) {
            return $query->where('shop_category', $category);
        }
        return $query;
    }
}
