<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'partyMembers' => collect(),
    'currentUserId' => null,
    'clickable' => false,
    'routePrefix' => null,
    'routeParams' => []
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'partyMembers' => collect(),
    'currentUserId' => null,
    'clickable' => false,
    'routePrefix' => null,
    'routeParams' => []
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<?php
    // Фильтруем участников - исключаем текущего пользователя и убираем дубликаты
    $otherMembers = $partyMembers->filter(function ($member) use ($currentUserId) {
        return $member->id !== $currentUserId;
    })->unique('id'); // Убираем дубликаты по ID
?>

<?php if($otherMembers->count() > 0): ?>
    <div class="mt-1 mb-0">
        <div class="bg-gradient-to-r from-[#3d3a2e] to-[#2a2721] border border-[#6c4539] rounded-md p-1 max-w-md mx-auto">
            
            <div class="flex items-center justify-left space-x-1 text-xs">
                
                

                
                <div class="flex items-center space-x-1 flex-wrap" id="party-members-container">
                    <?php $__currentLoopData = $otherMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            // Получаем актуальные ресурсы участника
                            $memberResources = $member->profile->getActualResources();
                            $currentHp = $memberResources['current_hp'] ?? $member->profile->current_hp ?? 0;
                            $maxHp = $member->profile->max_hp ?? 100;
                        ?>

                        <?php if($clickable && $routePrefix): ?>
                            
                            <?php
                                // Проверяем, выбран ли этот игрок как цель (союзник)
                                $isSelected = Auth::user()->current_target_type === 'ally' && Auth::user()->current_target_id == $member->id;

                                // Формируем маршрут в зависимости от префикса
                                $selectRoute = $routePrefix . '.select-ally';
                                $routeParameters = array_merge($routeParams, [$member->id]);
                            ?>

                            <form action="<?php echo e(route($selectRoute, $routeParameters)); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit"
                                        class="party-member-item text-[#fceac4] hover:text-[#f28b38] transition-colors duration-200
                                               <?php echo e($isSelected ? 'text-[#f28b38] font-bold' : ''); ?>"
                                        data-member-id="<?php echo e($member->id); ?>">
                                    
                                    <?php if($member->isOnline()): ?>
                                        <span class="inline-block w-1.5 h-1.5 bg-[#3e5c48] rounded-full ml-1"></span>
                                    <?php else: ?>
                                        <span class="inline-block w-1.5 h-1.5 bg-[#6c4539] rounded-full ml-1"></span>
                                    <?php endif; ?>

                                    <span class="font-medium"><?php echo e($member->name); ?></span>

                                    
                                    <span class="text-[#a6925e]">
                                        <span class="member-hp-text" data-current-hp="<?php echo e($currentHp); ?>" data-max-hp="<?php echo e($maxHp); ?>">
                                            <?php echo e($currentHp); ?>/<?php echo e($maxHp); ?>

                                        </span>
                                    </span>

                                    
                                    <img src="<?php echo e(asset('assets/user/hpIcon.png')); ?>" alt="HP" class="w-3 h-3 inline">

                                    
                                    <?php if($isSelected): ?>
                                        <span class="text-xs">🎯</span>
                                    <?php endif; ?>
                                </button>
                            </form>
                        <?php else: ?>
                            
                            <span class="party-member-item text-[#fceac4]" data-member-id="<?php echo e($member->id); ?>">
                                
                                <?php if($member->isOnline()): ?>
                                    <span class="inline-block w-1.5 h-1.5 bg-[#3e5c48] rounded-full ml-1"></span>
                                <?php else: ?>
                                    <span class="inline-block w-1.5 h-1.5 bg-[#6c4539] rounded-full ml-1"></span>
                                <?php endif; ?>

                                <span class="font-medium"><?php echo e($member->name); ?></span>

                                
                                <span class="text-[#a6925e]">
                                    <span class="member-hp-text" data-current-hp="<?php echo e($currentHp); ?>" data-max-hp="<?php echo e($maxHp); ?>">
                                        <?php echo e($currentHp); ?>/<?php echo e($maxHp); ?>

                                    </span>
                                </span>

                                
                                <img src="<?php echo e(asset('assets/user/hpIcon.png')); ?>" alt="HP" class="w-3 h-3 inline">
                            </span>
                        <?php endif; ?>

                        
                        <?php if($index < $otherMembers->count() - 1): ?>
                            <span class="text-[#a6925e]">•</span>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/party/members-bar.blade.php ENDPATH**/ ?>