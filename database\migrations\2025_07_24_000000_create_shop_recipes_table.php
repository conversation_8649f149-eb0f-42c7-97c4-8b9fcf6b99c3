<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Создает таблицу shop_recipes для управления рецептами в магазине
     */
    public function up(): void
    {
        Schema::create('shop_recipes', function (Blueprint $table) {
            $table->id();
            
            // Связь с рецептом зелья
            $table->foreignId('potion_recipe_id')
                ->constrained('potion_recipes')
                ->onDelete('cascade')
                ->comment('ID рецепта зелья');
            
            // Цена в магазине (может отличаться от базовой цены рецепта)
            $table->integer('price_gold')->default(0)->comment('Цена в золоте');
            $table->integer('price_silver')->default(0)->comment('Цена в серебре');
            $table->integer('price_bronze')->default(0)->comment('Цена в бронзе');
            
            // Доступность в магазине
            $table->boolean('is_available')->default(true)->comment('Доступен ли для покупки');
            
            // Категория в магазине (для группировки)
            $table->string('shop_category')->nullable()->comment('Категория в магазине');
            
            // Временные метки
            $table->timestamps();
            
            // Индексы для оптимизации
            $table->index(['is_available', 'created_at'], 'shop_recipes_available_created_idx');
            $table->index('shop_category', 'shop_recipes_category_idx');
            
            // Уникальность - один рецепт может быть только один раз в магазине
            $table->unique('potion_recipe_id', 'shop_recipes_recipe_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shop_recipes');
    }
};
