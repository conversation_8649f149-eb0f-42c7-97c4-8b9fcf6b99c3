<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PotionRecipe;

class TestPotionRecipeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Создаем тестовые рецепты для проверки системы магазина
        $recipes = [
            [
                'recipe_id' => 'healing_potion_basic',
                'name' => 'Зелье лечения',
                'description' => 'Восстанавливает здоровье персонажа',
                'quality' => PotionRecipe::QUALITY_COMMON,
                'brewing_time_modifier' => 1.0,
                'icon' => 'assets/potions/healing_potion.png',
                'effect' => 'health',
                'effect_value' => 50,
                'effect_duration' => 0,
                'level' => 'basic',
                'color' => '#ff0000',
                'brewing_time' => 300,
                'min_alchemy_level' => 1,
                'is_active' => true,
                'price_gold' => 0,
                'price_silver' => 5,
                'price_bronze' => 0,
            ],
            [
                'recipe_id' => 'mana_potion_basic',
                'name' => 'Зелье маны',
                'description' => 'Восстанавливает ману персонажа',
                'quality' => PotionRecipe::QUALITY_COMMON,
                'brewing_time_modifier' => 1.0,
                'icon' => 'assets/potions/mana_potion.png',
                'effect' => 'mana',
                'effect_value' => 30,
                'effect_duration' => 0,
                'level' => 'basic',
                'color' => '#0000ff',
                'brewing_time' => 300,
                'min_alchemy_level' => 1,
                'is_active' => true,
                'price_gold' => 0,
                'price_silver' => 4,
                'price_bronze' => 0,
            ],
            [
                'recipe_id' => 'strength_potion_advanced',
                'name' => 'Зелье силы',
                'description' => 'Временно увеличивает силу персонажа',
                'quality' => PotionRecipe::QUALITY_UNCOMMON,
                'brewing_time_modifier' => 1.2,
                'icon' => 'assets/potions/strength_potion.png',
                'effect' => 'strength',
                'effect_value' => 10,
                'effect_duration' => 600,
                'level' => 'advanced',
                'color' => '#ff8800',
                'brewing_time' => 600,
                'min_alchemy_level' => 3,
                'is_active' => true,
                'price_gold' => 0,
                'price_silver' => 15,
                'price_bronze' => 0,
            ],
        ];

        foreach ($recipes as $recipeData) {
            PotionRecipe::create($recipeData);
        }

        $this->command->info('Тестовые рецепты зелий созданы успешно!');
    }
}
