<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Профессии - <?php echo e(Auth::check() ? Auth::user()->name : 'Гость'); ?></title>

    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    
    <div class="container max-w-md mx-auto px-1 py-0 border-2 border-[#a6925e] rounded-lg flex-grow">
        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasUnreadMessages' => $hasUnreadMessages ?? false,'unreadMessagesCount' => $unreadMessagesCount ?? 0,'hasBrokenItems' => $hasBrokenItems ?? false,'brokenItemsCount' => $brokenItemsCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasUnreadMessages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasUnreadMessages ?? false),'unreadMessagesCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unreadMessagesCount ?? 0),'hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBrokenItems ?? false),'brokenItemsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($brokenItemsCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile,'experienceProgress' => $experienceProgress ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile),'experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>

        
        <div class="text-center flex justify-center space-x-1 max-w-[300px] mx-auto">
            <?php if(session('welcome_message')): ?>
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    <?php echo e(session('welcome_message')); ?>

                </div>
            <?php endif; ?>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal069f4536f90ea76449b638703689fbb5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal069f4536f90ea76449b638703689fbb5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.location-image','data' => ['breadcrumbs' => $breadcrumbs,'title' => 'Профессии','imagePath' => 'assets/bgProfessions.png']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.location-image'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs),'title' => 'Профессии','imagePath' => 'assets/bgProfessions.png']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal069f4536f90ea76449b638703689fbb5)): ?>
<?php $attributes = $__attributesOriginal069f4536f90ea76449b638703689fbb5; ?>
<?php unset($__attributesOriginal069f4536f90ea76449b638703689fbb5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal069f4536f90ea76449b638703689fbb5)): ?>
<?php $component = $__componentOriginal069f4536f90ea76449b638703689fbb5; ?>
<?php unset($__componentOriginal069f4536f90ea76449b638703689fbb5); ?>
<?php endif; ?>

        
        <div class="px-4">
            <h2 class="text-center text-xl font-semibold text-[#e5b769] mb-4 relative">
                <span
                    class="absolute left-0 right-0 top-1/2 h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50"></span>
                <span class="relative bg-[#2f2d2b] px-4">Ремесленные профессии</span>
            </h2>

            <div class="grid grid-cols-1 gap-4">
                <?php $__currentLoopData = $professions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $profession): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($key == 'cooking'): ?>
                        
                        <a href="<?php echo e(route($profession['route'])); ?>" class="group">
                            <div
                                class="relative h-full bg-gradient-to-b from-[#3a3630] to-[#2a2621] rounded-lg overflow-hidden transition-all duration-300 transform hover:scale-[1.02] hover:shadow-[0_0_15px_rgba(196,167,109,0.3)]">
                                
                                <div class="relative h-36 overflow-hidden">
                                    <img src="<?php echo e(asset('assets/cooking.png')); ?>" alt="Кулинария"
                                        class="w-full h-full object-contain transform group-hover:scale-105 transition-all duration-700 ease-in-out">

                                    
                                    <div
                                        class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[#c4a76d] to-transparent opacity-70">
                                    </div>
                                </div>

                                
                                <div
                                    class="px-4 pt-3 pb-2 bg-gradient-to-b from-[#3a3630] to-[#2a2621] border-t border-[#514b3c]">
                                    <h3 class="text-[#e5b769] font-medium text-lg text-center"><?php echo e($profession['name']); ?>

                                    </h3>
                                </div>

                                
                                <div class="p-4 pt-2">
                                    <p class="text-[#d3c6a6] text-sm mb-3"><?php echo e($profession['description']); ?></p>

                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 rounded-full bg-[#514b3c] mr-1.5"></div>
                                            <span class="text-xs text-[#d3c6a6]">Мастерство: <span
                                                    class="text-[#e5b769]">0</span>/100</span>
                                        </div>
                                        <span class="text-[#e5b769] text-xs flex items-center">
                                            Подробнее
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5l7 7-7 7" />
                                            </svg>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    <?php else: ?>
                                
                                <div class="group">
                                    <div
                                        class="relative h-full bg-gradient-to-b from-[#3a3630] to-[#2a2621] rounded-lg overflow-hidden">
                                        
                                        <div class="relative h-36 overflow-hidden filter grayscale">
                                            
                                            <img src="<?php echo e(asset('assets/' . match ($key) {
                            'tailoring' => 'tailor.png',
                            'carpentry' => 'carpenter.png',
                            default => $key . '.png'
                        })); ?>" alt="<?php echo e($profession['name']); ?>" class="w-full h-full object-contain opacity-40">

                                            
                                            <div class="absolute inset-0 bg-[#2a2621] opacity-60"></div>

                                            
                                            <div class="absolute inset-0 flex flex-col items-center justify-center">
                                                <span class="text-[#e5b769] text-2xl font-bold animate-pulse mb-2">Скоро</span>
                                                <div
                                                    class="px-4 py-2 bg-[#2a2621]/80 border border-[#a6925e] rounded-lg transform -rotate-2 hover:rotate-0 transition-transform duration-300">
                                                    <div class="relative">
                                                        <span class="text-[#d3c6a6] text-sm">В разработке</span>
                                                        
                                                        <div
                                                            class="absolute -left-1 -right-1 -top-1 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent">
                                                        </div>
                                                        <div
                                                            class="absolute -left-1 -right-1 -bottom-1 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            
                                            <div
                                                class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[#c4a76d] to-transparent opacity-30">
                                            </div>
                                        </div>

                                        
                                        <div
                                            class="px-4 pt-3 pb-2 bg-gradient-to-b from-[#3a3630] to-[#2a2621] border-t border-[#514b3c]">
                                            <h3 class="text-[#9a9483] font-medium text-lg text-center"><?php echo e($profession['name']); ?>

                                            </h3>
                                        </div>

                                        
                                        <div class="p-4 pt-2 opacity-60">
                                            <p class="text-[#9a9483] text-sm mb-3"><?php echo e($profession['description']); ?></p>

                                            <div class="flex justify-between items-center">
                                                <div class="flex items-center">
                                                    <div class="w-3 h-3 rounded-full bg-[#514b3c] mr-1.5"></div>
                                                    <span class="text-xs text-[#9a9483]">Мастерство: <span
                                                            class="text-[#9a9483]">0</span>/100</span>
                                                </div>
                                                <span class="text-[#9a9483] text-xs flex items-center">
                                                    Недоступно
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                                    </svg>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

    </div>

    
    <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/professions/index.blade.php ENDPATH**/ ?>