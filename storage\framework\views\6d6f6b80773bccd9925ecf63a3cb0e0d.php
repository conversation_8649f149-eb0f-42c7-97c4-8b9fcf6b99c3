<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['tab' => 'item', 'counts' => []]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['tab' => 'item', 'counts' => []]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<div class="mt-0 mb-1">
    
    <div class="flex flex-wrap justify-center items-center gap-1 relative">
        
        <div class="absolute h-[1px] left-1 right-1 top-1/2 transform -translate-y-1/2
            bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-30 z-0"></div>

        
        <?php if(($counts['item'] ?? 0) > 0): ?>
            <a href="<?php echo e(route('inventory.index', ['tab' => 'item'])); ?>"
                class="inventory-tab <?php echo e($tab === 'item' ? 'active' : 'inactive'); ?>">
                <span class="tab-icon">🛡️</span>
                <span class="ml-1 hidden sm:inline">Вещи</span>
                <span class="tab-count <?php echo e($tab === 'item' ? 'active' : 'inactive'); ?>">
                    <?php echo e($counts['item'] ?? 0); ?>

                </span>
            </a>
        <?php endif; ?>

        
        <?php if(($counts['resource'] ?? 0) > 0): ?>
            <a href="<?php echo e(route('inventory.index', ['tab' => 'resource'])); ?>"
                class="inventory-tab <?php echo e($tab === 'resource' ? 'active' : 'inactive'); ?>">
                <span class="tab-icon">💎</span>
                <span class="ml-1 hidden sm:inline">Ресурсы</span>
                <span class="tab-count <?php echo e($tab === 'resource' ? 'active' : 'inactive'); ?>">
                    <?php echo e($counts['resource'] ?? 0); ?>

                </span>
            </a>
        <?php endif; ?>

        
        <?php if(($counts['ingredient'] ?? 0) > 0): ?>
            <a href="<?php echo e(route('inventory.index', ['tab' => 'ingredient'])); ?>"
                class="inventory-tab <?php echo e($tab === 'ingredient' ? 'active' : 'inactive'); ?>">
                <span class="tab-icon">🌿</span>
                <span class="ml-1 hidden sm:inline">Ингр-ты</span>
                <span class="tab-count <?php echo e($tab === 'ingredient' ? 'active' : 'inactive'); ?>">
                    <?php echo e($counts['ingredient'] ?? 0); ?>

                </span>
            </a>
        <?php endif; ?>

        
        <?php if(($counts['recipe'] ?? 0) > 0): ?>
            <a href="<?php echo e(route('inventory.index', ['tab' => 'recipe'])); ?>"
                class="inventory-tab <?php echo e($tab === 'recipe' ? 'active' : 'inactive'); ?>">
                <span class="tab-icon">📜</span>
                <span class="ml-1 hidden sm:inline">Рецепты</span>
                <span class="tab-count <?php echo e($tab === 'recipe' ? 'active' : 'inactive'); ?>">
                    <?php echo e($counts['recipe'] ?? 0); ?>

                </span>
            </a>
        <?php endif; ?>

        
        <?php if(($counts['harvest'] ?? 0) > 0): ?>
            <a href="<?php echo e(route('inventory.index', ['tab' => 'harvest'])); ?>"
                class="inventory-tab <?php echo e($tab === 'harvest' ? 'active' : 'inactive'); ?>">
                <span class="tab-icon">🌾</span>
                <span class="ml-1 hidden sm:inline">Урожай</span>
                <span class="tab-count <?php echo e($tab === 'harvest' ? 'active' : 'inactive'); ?>">
                    <?php echo e($counts['harvest'] ?? 0); ?>

                </span>
            </a>
        <?php endif; ?>

        
        <?php if(($counts['potion'] ?? 0) > 0): ?>
            <a href="<?php echo e(route('inventory.index', ['tab' => 'potion'])); ?>"
                class="inventory-tab <?php echo e($tab === 'potion' ? 'active' : 'inactive'); ?>">
                <span class="tab-icon">🧪</span>
                <span class="ml-1 hidden sm:inline">Зелья</span>
                <span class="tab-count <?php echo e($tab === 'potion' ? 'active' : 'inactive'); ?>">
                    <?php echo e($counts['potion'] ?? 0); ?>

                </span>
            </a>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/inventory/tabs.blade.php ENDPATH**/ ?>