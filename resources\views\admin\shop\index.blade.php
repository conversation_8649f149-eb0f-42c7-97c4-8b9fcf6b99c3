<!DOCTYPE html>
<html lang="ru">
{{-- Указываем язык документа --}}

<head>
    <meta charset="UTF-8">
    {{-- Установка кодировки UTF-8 --}}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- Настройка viewport для адаптивности --}}
    <title>Управление магазином - Админ панель</title>
    {{-- Заголовок страницы --}}
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    {{-- Подключение CSS и JS с помощью Vite --}}
</head>

{{-- Тело документа с фоном и шрифтом из админ-панели --}}

<body class="bg-[#1a1814] text-[#d4cbb0] font-serif">
    {{-- Основной контейнер страницы --}}
    <div class="min-h-screen flex flex-col">
        {{-- Основное содержимое --}}
        <main class="flex-grow container mx-auto px-4 py-6">
            {{-- Верхний блок с информацией --}}
            <div
                class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] border-2 border-[#3b3629] rounded-lg shadow-lg mb-8 p-5">
                {{-- Шапка с заголовком и кнопками навигации --}}
                <div class="flex justify-between items-center mb-5">
                    {{-- Кнопка Назад на Дашборд --}}
                    <a href="{{ route('admin.dashboard') }}" title="Вернуться в панель управления"
                        class="p-2 bg-[#3b3629] hover:bg-[#c1a96e] rounded-lg transition duration-300 border border-[#6e3f35]">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#c1a96e]" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M11 15l-3-3m0 0l3-3m-3 3h8M3 12a9 9 0 1118 0 9 9 0 01-18 0z" />
                        </svg>
                    </a>

                    {{-- Заголовок --}}
                    <h1 class="text-3xl font-bold text-[#e4d7b0]">Управление Магазином</h1>

                    {{-- Кнопки добавления --}}
                    <div class="flex space-x-2">
                        {{-- Кнопка Добавить новый предмет --}}
                        <a href="{{ route('admin.shop.create', ['type' => 'item']) }}"
                            title="Добавить новый предмет в магазин"
                            class="p-2 bg-[#3b3629] hover:bg-[#c1a96e] rounded-lg transition duration-300 border border-[#6e3f35]">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#c1a96e]" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </a>
                        {{-- Кнопка Добавить новый рецепт --}}
                        <a href="{{ route('admin.shop.create', ['type' => 'recipe']) }}"
                            title="Добавить новый рецепт в магазин"
                            class="p-2 bg-[#2f473c] hover:bg-[#c1a96e] rounded-lg transition duration-300 border border-[#2f473c]">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#c1a96e]" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                        </a>
                    </div>
                </div>

                {{-- Отображение сообщений об успехе или ошибках --}}
                @if (session('success'))
                    <div class="bg-green-800 border border-green-600 text-green-100 px-4 py-3 rounded relative mb-4"
                        role="alert">
                        <span class="block sm:inline">{{ session('success') }}</span>
                    </div>
                @endif
                @if (session('error'))
                    <div class="bg-red-800 border border-red-600 text-red-100 px-4 py-3 rounded relative mb-4" role="alert">
                        <span class="block sm:inline">{{ session('error') }}</span>
                    </div>
                @endif
            </div>

            {{-- Вкладки для переключения между предметами и рецептами --}}
            <div class="mb-6">
                <div class="border-b border-[#3b3629]">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        {{-- Вкладка Предметы --}}
                        <a href="{{ route('admin.shop.index', ['tab' => 'items']) }}"
                            class="@if($activeTab === 'items') border-[#c1a96e] text-[#e4d7b0] @else border-transparent text-[#998d66] hover:text-[#d4cbb0] hover:border-[#998d66] @endif whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition duration-300">
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            </svg>
                            Предметы ({{ $shopItems->total() }})
                        </a>
                        {{-- Вкладка Рецепты --}}
                        <a href="{{ route('admin.shop.index', ['tab' => 'recipes']) }}"
                            class="@if($activeTab === 'recipes') border-[#c1a96e] text-[#e4d7b0] @else border-transparent text-[#998d66] hover:text-[#d4cbb0] hover:border-[#998d66] @endif whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition duration-300">
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                            Рецепты ({{ $shopRecipes->total() }})
                        </a>
                    </nav>
                </div>
            </div>

            {{-- Содержимое вкладок --}}
            @if($activeTab === 'items')
                {{-- Таблица с предметами магазина --}}
                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg shadow-md overflow-hidden">
                    <table class="min-w-full divide-y divide-[#514b3c]">
                        {{-- Заголовок таблицы --}}
                        <thead class="bg-[#3d3a2e]">
                            <tr>
                                {{-- ID Предмета --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    ID</th>
                                {{-- Иконка Предмета --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Иконка</th>
                                {{-- Название Предмета --}}
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Название</th>
                                {{-- Тип Предмета --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Тип</th>
                                {{-- Качество Предмета --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Качество</th>
                                {{-- Слот экипировки --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Слот</th>
                                {{-- Цена (Золото) --}}
                                <th scope="col"
                                    class="px-2 py-3 text-center text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Золото</th>
                                {{-- Цена (Серебро) --}}
                                <th scope="col"
                                    class="px-2 py-3 text-center text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Серебро</th>
                                {{-- Цена (Бронза) --}}
                                <th scope="col"
                                    class="px-2 py-3 text-center text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Бронза</th>
                                {{-- Доступность --}}
                                <th scope="col"
                                    class="px-4 py-3 text-center text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Доступен</th>
                                {{-- Действия (Редактировать/Удалить) --}}
                                <th scope="col"
                                    class="px-6 py-3 text-center text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Действия</th>
                            </tr>
                        </thead>
                        {{-- Тело таблицы --}}
                        <tbody class="bg-[#211f1a] divide-y divide-[#4a452c]">
                            {{-- Проверка, есть ли предметы в магазине --}}
                            @forelse ($shopItems as $shopItem)
                                {{-- Итерация по каждому предмету магазина --}}
                                <tr>
                                    {{-- ID --}}
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#9a9483]">{{ $shopItem->id }}</td>
                                    {{-- Иконка --}}
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        @if ($shopItem->item && $shopItem->item->icon)
                                            <img src="{{ asset($shopItem->item->icon) }}"
                                                alt="{{ $shopItem->item->name ?? 'Иконка' }}"
                                                class="w-8 h-8 rounded border border-[#a6925e]">
                                        @else
                                            {{-- Заглушка, если иконки нет --}}
                                            <div
                                                class="w-8 h-8 rounded border border-[#514b3c] bg-[#3d3a2e] flex items-center justify-center text-[#9a9483]">
                                                ?</div>
                                        @endif
                                    </td>
                                    {{-- Название --}}
                                    <td class="px-6 py-3 whitespace-nowrap text-sm font-medium text-[#d9d3b8]">
                                        {{-- Отображаем имя из связанной модели Item, если она есть --}}
                                        {{ $shopItem->item->name ?? 'Предмет не найден' }}
                                    </td>
                                    {{-- Тип --}}
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#b0a890]">
                                        {{ $shopItem->item->type ?? '-' }}
                                    </td>
                                    {{-- Качество --}}
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#b0a890]">
                                        {{ $shopItem->item->quality ?? '-' }}
                                    </td>
                                    {{-- Слот экипировки --}}
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#e5b769]">
                                        @if($shopItem->item && $shopItem->item->type)
                                            {{ \App\Models\GameItem::getSlotForItemType($shopItem->item->type) ?? 'Не определен' }}
                                        @else
                                            -
                                        @endif
                                    </td>
                                    {{-- Цена (Золото) --}}
                                    <td class="px-2 py-3 whitespace-nowrap text-sm text-center text-[#ffd700]">
                                        {{ $shopItem->price_gold ?? 0 }}
                                        {{-- Форматирование цены с иконками, если нужно:
                                        @inject('currencyService', 'App\Services\CurrencyService')
                                        {!! $currencyService->formatPriceForDisplay($shopItem->price_gold,
                                        $shopItem->price_silver, $shopItem->price_bronze) !!} --}}
                                    </td>
                                    {{-- Цена (Серебро) --}}
                                    <td class="px-2 py-3 whitespace-nowrap text-sm text-center text-[#c0c0c0]">
                                        {{ $shopItem->price_silver ?? 0 }}
                                    </td>
                                    {{-- Цена (Бронза) --}}
                                    <td class="px-2 py-3 whitespace-nowrap text-sm text-center text-[#cd7f32]">
                                        {{ $shopItem->price_bronze ?? 0 }}
                                    </td>
                                    {{-- Доступность --}}
                                    <td class="px-4 py-3 whitespace-nowrap text-center text-sm">
                                        @if($shopItem->is_available)
                                            <span class="text-green-400">✔ Да</span>
                                        @else
                                            <span class="text-red-400">✘ Нет</span>
                                        @endif
                                    </td>
                                    {{-- Действия --}}
                                    <td class="px-6 py-3 whitespace-nowrap text-center text-sm font-medium">
                                        {{-- Ссылка на редактирование --}}
                                        <a href="{{ route('admin.shop.edit', $shopItem->id) }}"
                                            class="text-[#a6925e] hover:text-[#e5b769] mr-3 transition duration-300"
                                            title="Редактировать цену">✏️</a>
                                        {{-- Форма для удаления --}}
                                        <form action="{{ route('admin.shop.destroy', $shopItem->id) }}" method="POST"
                                            class="inline-block"
                                            onsubmit="return confirm('Вы уверены, что хотите удалить этот предмет из магазина?');">
                                            @csrf
                                            {{-- Метод DELETE для удаления --}}
                                            @method('DELETE')
                                            {{-- Кнопка удаления --}}
                                            <button type="submit"
                                                class="text-red-500 hover:text-red-700 transition duration-300"
                                                title="Удалить из магазина">🗑️</button>
                                        </form>
                                    </td>
                                </tr>
                                {{-- Если предметов нет --}}
                            @empty
                                <tr>
                                    {{-- Сообщение о том, что предметы не найдены --}}
                                    <td colspan="11" class="px-6 py-4 whitespace-nowrap text-center text-sm text-[#9a9483]">
                                        Предметы в магазине пока не добавлены.
                                    </td>
                                </tr>
                            @endforelse
                            {{-- Конец цикла foreach --}}
                        </tbody>
                    </table>
                </div>

                {{-- Пагинация для предметов --}}
                <div class="mt-6">
                    {{-- Отображение ссылок пагинации с сохранением параметров запроса --}}
                    {{ $shopItems->appends(['tab' => 'items'])->links() }}
                </div>

            @else
                {{-- Таблица с рецептами магазина --}}
                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg shadow-md overflow-hidden">
                    <table class="min-w-full divide-y divide-[#3b3629]">
                        {{-- Заголовок таблицы --}}
                        <thead class="bg-[#2a2722]">
                            <tr>
                                {{-- ID Рецепта --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    ID</th>
                                {{-- Иконка Рецепта --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Иконка</th>
                                {{-- Название Рецепта --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Название</th>
                                {{-- Качество --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Качество</th>
                                {{-- Уровень --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Уровень</th>
                                {{-- Цена в золоте --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Золото</th>
                                {{-- Цена в серебре --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Серебро</th>
                                {{-- Цена в бронзе --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Бронза</th>
                                {{-- Категория --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Категория</th>
                                {{-- Доступность --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Доступен</th>
                                {{-- Действия --}}
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Действия</th>
                            </tr>
                        </thead>
                        <tbody class="bg-[#1a1814] divide-y divide-[#3b3629]">
                            {{-- Итерация по рецептам магазина --}}
                            @forelse ($shopRecipes as $shopRecipe)
                                <tr class="hover:bg-[#2a2722] transition duration-300">
                                    {{-- ID рецепта --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                        {{ $shopRecipe->potionRecipe->id ?? 'N/A' }}
                                    </td>
                                    {{-- Иконка рецепта --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                        @if($shopRecipe->potionRecipe && $shopRecipe->potionRecipe->icon)
                                            <img src="{{ asset($shopRecipe->potionRecipe->icon) }}" alt="Иконка рецепта"
                                                class="w-8 h-8 rounded">
                                        @else
                                            <span class="text-[#998d66]">Нет иконки</span>
                                        @endif
                                    </td>
                                    {{-- Название рецепта --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-[#e4d7b0]">
                                        {{ $shopRecipe->potionRecipe->name ?? 'Неизвестный рецепт' }}
                                    </td>
                                    {{-- Качество --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#c1a96e]">
                                        {{ $shopRecipe->potionRecipe->quality ?? 'N/A' }}
                                    </td>
                                    {{-- Уровень --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                        {{ $shopRecipe->potionRecipe->level ?? 'N/A' }}
                                    </td>
                                    {{-- Цена в золоте --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#c1a96e]">
                                        {{ $shopRecipe->price_gold }}
                                    </td>
                                    {{-- Цена в серебре --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#998d66]">
                                        {{ $shopRecipe->price_silver }}
                                    </td>
                                    {{-- Цена в бронзе --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#6e3f35]">
                                        {{ $shopRecipe->price_bronze }}
                                    </td>
                                    {{-- Категория --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                        {{ $shopRecipe->shop_category ?? 'Без категории' }}
                                    </td>
                                    {{-- Доступность --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm">
                                        @if($shopRecipe->is_available)
                                            <span class="text-[#2f473c] bg-[#1e2e27] px-2 py-1 rounded text-xs">Да</span>
                                        @else
                                            <span class="text-[#59372d] bg-[#3c221b] px-2 py-1 rounded text-xs">Нет</span>
                                        @endif
                                    </td>
                                    {{-- Действия --}}
                                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                        {{-- Форма для удаления рецепта из магазина --}}
                                        <form action="{{ route('admin.shop.destroy-recipe', $shopRecipe) }}" method="POST"
                                            style="display: inline;"
                                            onsubmit="return confirm('Вы уверены, что хотите удалить этот рецепт из магазина?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                class="text-[#59372d] hover:text-[#6e3f35] transition duration-300"
                                                title="Удалить из магазина">🗑️</button>
                                        </form>
                                    </td>
                                </tr>
                                {{-- Если рецептов нет --}}
                            @empty
                                <tr>
                                    {{-- Сообщение о том, что рецепты не найдены --}}
                                    <td colspan="11" class="px-6 py-4 whitespace-nowrap text-center text-sm text-[#998d66]">
                                        Рецепты в магазине пока не добавлены.
                                    </td>
                                </tr>
                            @endforelse
                            {{-- Конец цикла foreach --}}
                        </tbody>
                    </table>
                </div>

                {{-- Пагинация для рецептов --}}
                <div class="mt-6">
                    {{-- Отображение ссылок пагинации с сохранением параметров запроса --}}
                    {{ $shopRecipes->appends(['tab' => 'recipes'])->links() }}
                </div>
            @endif

        </main>

        {{-- Футер админ-панели --}}
        <footer class="bg-[#1a1814] text-[#998d66] py-4 mt-auto border-t border-[#3b3629]">
            <div class="container max-w-7xl mx-auto px-6 text-center text-sm">
                © {{ date('Y') }} Echoes of Eternity | Административная Панель
            </div>
        </footer>
    </div>

</body>

</html>