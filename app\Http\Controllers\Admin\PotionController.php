<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Potion;
use App\Models\PotionRecipe;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

/**
 * Контроллер для управления зельями в админ-панели
 */
class PotionController extends Controller
{
    /**
     * Отображает список всех зелий
     *
     * @param Request $request Запрос с параметрами фильтрации
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Создаем запрос для получения зелий с возможностью фильтрации
        $query = Potion::query();

        // Фильтрация по названию
        if ($request->has('name') && !empty($request->name)) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }

        // Фильтрация по эффекту
        if ($request->has('effect') && !empty($request->effect)) {
            $query->where('effect', $request->effect);
        }

        // Фильтрация по качеству
        if ($request->has('quality') && !empty($request->quality)) {
            $query->where('quality', $request->quality);
        }

        // Фильтрация по уровню
        if ($request->has('level') && !empty($request->level)) {
            $query->where('level', $request->level);
        }

        // Сортировка и пагинация
        $potions = $query->orderBy('id', 'desc')->paginate(15);

        // Получаем список возможных эффектов и качеств для фильтров
        $potionEffects = Potion::POTION_TYPES;
        $potionQualities = array_keys(Potion::QUALITY_LEVELS);
        $potionLevels = Potion::POTION_LEVELS;

        // Для автономной страницы
        if ($request->has('standalone')) {
            return view('admin.potions.standalone_index', compact(
                'potions',
                'potionEffects',
                'potionQualities',
                'potionLevels'
            ));
        }

        // Стандартный вид с лейаутом
        return view('admin.potions.index', compact(
            'potions',
            'potionEffects',
            'potionQualities',
            'potionLevels'
        ));
    }

    /**
     * Отображает форму для создания нового зелья
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Получаем список рецептов для выбора
        $recipes = PotionRecipe::where('is_active', true)->pluck('name', 'id');

        // Получаем списки для выпадающих меню
        $potionEffects = Potion::POTION_TYPES;
        $potionQualities = array_keys(Potion::QUALITY_LEVELS);
        $potionLevels = Potion::POTION_LEVELS;

        return view('admin.potions.create', compact(
            'recipes',
            'potionEffects',
            'potionQualities',
            'potionLevels'
        ));
    }

    /**
     * Сохраняет новое зелье в базе данных
     *
     * @param Request $request Данные формы
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Валидация входных данных
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'effect' => 'required|string|in:' . implode(',', array_keys(Potion::POTION_TYPES)),
            'effect_value' => 'required|numeric|min:1',
            'effect_duration' => 'required|integer|min:0',
            'level' => 'required|string|in:' . implode(',', array_keys(Potion::POTION_LEVELS)),
            'quality' => 'required|string|in:' . implode(',', array_keys(Potion::QUALITY_LEVELS)),
            'uses_left' => 'required|integer|min:1',
            'max_uses' => 'required|integer|min:1',
            'recipe_id' => 'nullable|exists:potion_recipes,id',
            'icon' => 'nullable|string|max:255', // Изменено: теперь принимаем строку с путем к иконке
            'color' => 'nullable|string|max:7',
            'is_template' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->route('admin.potions.create')
                ->withErrors($validator)
                ->withInput();
        }

        // Обработка иконки
        $iconPath = null;
        if ($request->filled('icon')) {
            $iconPath = $request->icon;

            // Убираем префикс assets/ если он есть
            if (strpos($iconPath, 'assets/') === 0) {
                $iconPath = substr($iconPath, 7); // Убираем 'assets/'
            }

            // Если путь не начинается с 'potions/', добавляем его
            if (strpos($iconPath, 'potions/') !== 0) {
                $iconPath = 'potions/' . ltrim($iconPath, '/');
            }

            // Логируем путь к иконке для отладки
            \Log::info('Путь к иконке зелья', [
                'original_icon' => $request->icon,
                'processed_icon_path' => $iconPath,
                'potion_name' => $request->name
            ]);
        }

        // Создаем новое зелье
        $potion = Potion::create([
            'name' => $request->name,
            'description' => $request->description,
            'effect' => $request->effect,
            'effect_value' => $request->effect_value,
            'effect_duration' => $request->effect_duration,
            'level' => $request->level,
            'quality' => $request->quality,
            'icon' => $iconPath,
            'color' => $request->color,
            'uses_left' => $request->uses_left,
            'max_uses' => $request->max_uses,
            'recipe_id' => $request->recipe_id,
            'user_id' => null, // Зелье создается в админке без владельца
            'is_template' => $request->has('is_template') ? true : false, // По умолчанию зелье является шаблоном
        ]);

        return redirect()->route('admin.potions.index')
            ->with('success', 'Зелье успешно создано!');
    }

    /**
     * Отображает детальную информацию о зелье
     *
     * @param int $id Идентификатор зелья
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $potion = Potion::findOrFail($id);
        return view('admin.potions.show', compact('potion'));
    }

    /**
     * Отображает форму для редактирования зелья
     *
     * @param int $id Идентификатор зелья
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $potion = Potion::findOrFail($id);

        // Получаем список рецептов для выбора
        $recipes = PotionRecipe::where('is_active', true)->pluck('name', 'id');

        // Получаем списки для выпадающих меню
        $potionEffects = Potion::POTION_TYPES;
        $potionQualities = array_keys(Potion::QUALITY_LEVELS);
        $potionLevels = Potion::POTION_LEVELS;

        // Получаем список пользователей для возможной привязки
        $users = User::pluck('name', 'id');

        return view('admin.potions.edit', compact(
            'potion',
            'recipes',
            'potionEffects',
            'potionQualities',
            'potionLevels',
            'users'
        ));
    }

    /**
     * Обновляет зелье в базе данных
     *
     * @param Request $request Данные формы
     * @param int $id Идентификатор зелья
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $potion = Potion::findOrFail($id);

        // Валидация входных данных
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'effect' => 'required|string|in:' . implode(',', array_keys(Potion::POTION_TYPES)),
            'effect_value' => 'required|numeric|min:1',
            'effect_duration' => 'required|integer|min:0',
            'level' => 'required|string|in:' . implode(',', array_keys(Potion::POTION_LEVELS)),
            'quality' => 'required|string|in:' . implode(',', array_keys(Potion::QUALITY_LEVELS)),
            'uses_left' => 'required|integer|min:0',
            'max_uses' => 'required|integer|min:1',
            'recipe_id' => 'nullable|exists:potion_recipes,id',
            'user_id' => 'nullable|exists:users,id',
            'icon' => 'nullable|string|max:255', // Изменено: теперь принимаем строку с путем к иконке
            'color' => 'nullable|string|max:7',
            'is_template' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->route('admin.potions.edit', $potion->id)
                ->withErrors($validator)
                ->withInput();
        }

        // Обработка иконки
        $iconPath = $potion->icon;
        if ($request->filled('icon')) {
            $iconPath = $request->icon;

            // Убираем префикс assets/ если он есть
            if (strpos($iconPath, 'assets/') === 0) {
                $iconPath = substr($iconPath, 7); // Убираем 'assets/'
            }

            // Если путь не начинается с 'potions/', добавляем его
            if (strpos($iconPath, 'potions/') !== 0) {
                $iconPath = 'potions/' . ltrim($iconPath, '/');
            }

            // Логируем путь к иконке для отладки
            \Log::info('Обновление пути к иконке зелья', [
                'old_icon_path' => $potion->icon,
                'original_icon' => $request->icon,
                'processed_icon_path' => $iconPath,
                'potion_name' => $request->name,
                'potion_id' => $potion->id
            ]);
        }

        // Обновляем зелье
        $potion->update([
            'name' => $request->name,
            'description' => $request->description,
            'effect' => $request->effect,
            'effect_value' => $request->effect_value,
            'effect_duration' => $request->effect_duration,
            'level' => $request->level,
            'quality' => $request->quality,
            'icon' => $iconPath,
            'color' => $request->color,
            'uses_left' => $request->uses_left,
            'max_uses' => $request->max_uses,
            'recipe_id' => $request->recipe_id,
            'user_id' => $request->user_id, // Оставляем возможность указать владельца
            'is_template' => $request->has('is_template') ? true : false,
        ]);

        return redirect()->route('admin.potions.index')
            ->with('success', 'Зелье успешно обновлено!');
    }

    /**
     * Удаляет зелье из базы данных
     *
     * @param int $id Идентификатор зелья
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $potion = Potion::findOrFail($id);

        try {
            // Проверяем, есть ли связанные записи в таблице potion_instances
            $hasInstances = \DB::table('potion_instances')
                ->where('potion_template_id', $id)
                ->exists();

            if ($hasInstances) {
                // Удаляем связанные записи из таблицы potion_instances
                \DB::table('potion_instances')
                    ->where('potion_template_id', $id)
                    ->delete();
            }

            // Удаляем изображение зелья, если оно есть
            if ($potion->icon) {
                if (strpos($potion->icon, 'assets/') === 0) {
                    // Если иконка в директории assets
                    $iconPath = public_path($potion->icon);
                    if (file_exists($iconPath)) {
                        unlink($iconPath);
                    }
                } elseif (strpos($potion->icon, '/') !== false) {
                    // Если иконка в storage
                    Storage::disk('public')->delete($potion->icon);
                }
                // Если иконка - эмодзи или обычный текст, ничего удалять не нужно
            }

            // Удаляем зелье
            $potion->delete();

            return redirect()->route('admin.potions.index')
                ->with('success', 'Зелье успешно удалено!');
        } catch (\Exception $e) {
            return redirect()->route('admin.potions.index')
                ->with('error', 'Не удалось удалить зелье: ' . $e->getMessage());
        }
    }

    /**
     * Создает тестовое зелье на основе выбранного рецепта
     *
     * @param Request $request Данные с ID рецепта
     * @return \Illuminate\Http\RedirectResponse
     */
    public function createTestPotion(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'recipe_id' => 'required|exists:potion_recipes,id',
            'create_game_item' => 'boolean',
            'user_id' => 'nullable|exists:users,id',
        ]);

        if ($validator->fails()) {
            return redirect()->route('admin.potions.index')
                ->withErrors($validator)
                ->withInput();
        }

        $recipe = PotionRecipe::findOrFail($request->recipe_id);

        // Создаем зелье на основе рецепта
        $potion = Potion::create([
            'name' => $recipe->name,
            'description' => $recipe->description,
            'effect' => $recipe->effect,
            'effect_value' => $recipe->effect_value,
            'effect_duration' => $recipe->effect_duration,
            'level' => $recipe->level,
            'quality' => 'Обычное', // Базовое качество
            'color' => $recipe->color,
            'icon' => $recipe->icon,
            'uses_left' => 1,
            'max_uses' => 1, // По умолчанию одно использование
            'recipe_id' => $recipe->id,
            'user_id' => $request->user_id, // Может быть указан владелец
            'is_template' => !$request->has('create_game_item'), // Если создаем игровой предмет, то это не шаблон
        ]);

        // Если нужно создать игровой предмет
        if ($request->has('create_game_item') && $request->create_game_item) {
            try {
                // Создаем игровой предмет на основе зелья
                $gameItem = $potion->createGameItem($request->user_id);

                // Обновляем зелье, чтобы связать его с игровым предметом
                $potion->update([
                    'game_item_id' => $gameItem->id
                ]);

                return redirect()->route('admin.potions.show', $potion->id)
                    ->with('success', 'Тестовое зелье успешно создано и добавлено в игровой инвентарь!');
            } catch (\Exception $e) {
                // Если произошла ошибка при создании игрового предмета
                return redirect()->route('admin.potions.show', $potion->id)
                    ->with('error', 'Зелье создано, но не удалось создать игровой предмет: ' . $e->getMessage());
            }
        }

        return redirect()->route('admin.potions.show', $potion->id)
            ->with('success', 'Тестовое зелье успешно создано!');
    }

    /**
     * Создает игровой предмет на основе шаблона зелья
     *
     * @param int $id ID зелья
     * @param Request $request Данные запроса
     * @return \Illuminate\Http\RedirectResponse
     */
    public function createGameItemFromPotion($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'location' => 'required|in:inventory,bank',
        ]);

        if ($validator->fails()) {
            return redirect()->route('admin.potions.show', $id)
                ->withErrors($validator)
                ->withInput();
        }

        $potion = Potion::findOrFail($id);

        // Проверяем, что зелье является шаблоном
        if (!$potion->isTemplate()) {
            return redirect()->route('admin.potions.show', $id)
                ->with('error', 'Нельзя создать игровой предмет из не-шаблонного зелья');
        }

        try {
            // Создаем игровой предмет на основе зелья
            $gameItem = $potion->createGameItem($request->user_id, $request->location);

            return redirect()->route('admin.potions.index')
                ->with('success', "Зелье \"{$potion->name}\" успешно добавлено игроку!");
        } catch (\Exception $e) {
            return redirect()->route('admin.potions.show', $id)
                ->with('error', 'Не удалось создать игровой предмет: ' . $e->getMessage());
        }
    }
}