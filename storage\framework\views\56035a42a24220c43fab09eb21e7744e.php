<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Управление предметами - Админ панель</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js', 'resources/js/admin/item-management.js']); ?>
</head>

<body class="bg-[#211f1a] text-[#d9d3b8] font-serif">
    <div class="min-h-screen flex flex-col">
        <main class="flex-grow container mx-auto px-4 py-6">
            <div
                class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg mb-8 p-5">
                <div class="flex justify-between items-center mb-5">
                    <a href="<?php echo e(route('admin.dashboard')); ?>" title="Вернуться в панель управления"
                        class="p-2 bg-[#514b3c] hover:bg-[#a6925e] rounded-lg transition duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#e5b769]" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                    </a>
                    <h1 class="text-2xl font-bold text-center text-[#e5b769]">Управление предметами</h1>
                    <div class="w-10"></div>
                </div>

                <?php if(session('success')): ?>
                    <div class="bg-green-800 border border-green-600 text-green-100 px-4 py-3 rounded relative mb-4">
                        <span class="block sm:inline"><?php echo e(session('success')); ?></span>
                    </div>
                <?php endif; ?>

                <?php if($errors->any()): ?>
                    <div class="bg-red-800 border border-red-600 text-red-100 px-4 py-3 rounded relative mb-4">
                        <ul class="list-disc list-inside">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="flex justify-between items-center mb-6">
                    <div>
                        <a href="<?php echo e(route('admin.items.create')); ?>"
                            class="px-4 py-2 bg-[#a6925e] text-[#2f2d2b] rounded hover:bg-[#c4a76d] transition duration-300 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4v16m8-8H4" />
                            </svg>
                            Создать
                        </a>
                        <a href="<?php echo e(route('admin.items.stats-calculator')); ?>"
                            class="px-4 py-2 bg-[#514b3c] text-[#d9d3b8] rounded hover:bg-[#625b4a] transition duration-300 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                            Калькулятор статов
                        </a>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full divide-y divide-[#514b3c]">
                        <thead class="bg-[#2a2721]">
                            <tr>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                    ID</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                    Иконка</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                    Название</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                    Тип</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                    Качество</th>
                                <th
                                    class="px-4 py-3 text-center text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                    Золото</th>
                                <th
                                    class="px-4 py-3 text-center text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                    Серебро</th>
                                <th
                                    class="px-4 py-3 text-center text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                    Бронза</th>
                                <th
                                    class="px-4 py-3 text-center text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                    Стартовый</th>
                                <th
                                    class="px-4 py-3 text-center text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                    Действия</th>
                            </tr>
                        </thead>
                        <tbody class="bg-[#252117] divide-y divide-[#514b3c]">
                            <?php $__empty_1 = true; $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#9a9483]"><?php echo e($item->id); ?></td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?php if($item->icon): ?>
                                            <img src="<?php echo e(asset($item->icon)); ?>" alt="<?php echo e($item->name); ?>"
                                                class="w-8 h-8 rounded border border-[#a6925e]">
                                        <?php else: ?>
                                            <div
                                                class="w-8 h-8 rounded border border-[#514b3c] bg-[#3d3a2e] flex items-center justify-center text-[#9a9483]">
                                                ?</div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-[#d9d3b8]">
                                        <?php echo e($item->name); ?>

                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#b0a890]"><?php echo e($item->type); ?></td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#b0a890]"><?php echo e($item->quality); ?></td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-center text-[#ffd700]">
                                        <?php echo e($item->gold); ?>

                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-center text-[#c0c0c0]">
                                        <?php echo e($item->silver); ?>

                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-center text-[#cd7f32]">
                                        <?php echo e($item->bronze); ?>

                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-center text-sm">
                                        <?php if($item->is_starter_item): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-800 text-green-100">
                                                ✓ Да
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-800 text-gray-300">
                                                ✗ Нет
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                        <div class="flex items-center justify-center space-x-2">
                                            <a href="<?php echo e(route('admin.items.edit', $item->id)); ?>"
                                                class="text-[#a6925e] hover:text-[#e5b769] transition duration-300"
                                                title="Редактировать">✏️</a>

                                            <form action="<?php echo e(route('admin.items.destroy', $item->id)); ?>" method="POST"
                                                class="inline-block"
                                                onsubmit="return confirm('Вы уверены, что хотите удалить этот предмет?');">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit"
                                                    class="text-red-500 hover:text-red-700 transition duration-300"
                                                    title="Удалить эталон">🗑️</button>
                                            </form>

                                            <button onclick="deleteAllInstances(<?php echo e($item->id); ?>, '<?php echo e($item->name); ?>')"
                                                class="text-orange-500 hover:text-orange-700 transition duration-300 text-lg"
                                                title="Удалить ВСЕ экземпляры предмета у игроков">💥</button>

                                            <button onclick="deleteAllInstancesFromAllPlayers(<?php echo e($item->id); ?>, '<?php echo e($item->name); ?>')"
                                                class="text-red-600 hover:text-red-800 transition duration-300 text-lg"
                                                title="Удалить ВСЕ экземпляры предмета у ВСЕХ игроков">🚨</button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="px-6 py-4 whitespace-nowrap text-center text-sm text-[#9a9483]">
                                        Предметы пока не добавлены.
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-6">
                    <?php echo e($items->links()); ?>

                </div>
            </div>
        </main>

        <footer class="bg-[#2a2721] text-[#9a9483] py-4 mt-auto border-t border-[#514b3c]">
            <div class="container max-w-7xl mx-auto px-6 text-center text-sm">
                © <?php echo e(date('Y')); ?> Echoes of Eternity | Административная Панель
            </div>
        </footer>
    </div>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/items/index.blade.php ENDPATH**/ ?>