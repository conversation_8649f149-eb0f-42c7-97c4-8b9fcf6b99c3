<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Создание зелья - Админ панель</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    
    <div class="min-h-screen flex flex-col">
        
        <main class="flex-grow container mx-auto px-4 py-6">
            
            <div
                class="container max-w-6xl mx-auto px-4 py-4 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg shadow-lg mb-6">
                
                <div class="flex justify-between items-center mb-4">
                    
                    <a href="<?php echo e(route('admin.potions.index')); ?>"
                        class="w-10 h-10 bg-[#a6925e] flex items-center justify-center rounded-lg hover:bg-[#e5b769] transition duration-300">
                        <span class="text-xl">←</span>
                    </a>

                    
                    <h1 class="text-2xl font-bold text-[#e5b769] text-center">Создание нового зелья</h1>

                    
                    <div class="w-10 h-10"></div>
                </div>

                
                <?php if($errors->any()): ?>
                    <div class="bg-[#38352c] text-[#ff6b6b] p-4 rounded-lg border border-[#a6925e] mb-4">
                        <ul class="list-disc pl-5">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>

            
            <div class="container max-w-6xl mx-auto mb-6">
                <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] rounded-lg p-6 border-2 border-[#a6925e]">
                    <h2 class="text-xl font-bold text-[#e5b769] mb-6 text-center">Информация о зелье</h2>

                    <form action="<?php echo e(route('admin.potions.store')); ?>" method="POST" enctype="multipart/form-data"
                        class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <?php echo csrf_field(); ?>

                        
                        <div class="space-y-4">
                            
                            <div>
                                <label for="name" class="block text-sm text-[#d9d3b8] mb-2">Название зелья <span
                                        class="text-[#ff6b6b]">*</span></label>
                                <input type="text" name="name" id="name" value="<?php echo e(old('name')); ?>" required
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                            </div>

                            
                            <div>
                                <label for="description" class="block text-sm text-[#d9d3b8] mb-2">Описание <span
                                        class="text-[#ff6b6b]">*</span></label>
                                <textarea name="description" id="description" rows="5" required
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]"><?php echo e(old('description')); ?></textarea>
                            </div>

                            
                            <div>
                                <label for="effect" class="block text-sm text-[#d9d3b8] mb-2">Тип эффекта <span
                                        class="text-[#ff6b6b]">*</span></label>
                                <select name="effect" id="effect" required
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                    <option value="">-- Выберите эффект --</option>
                                    <?php $__currentLoopData = $potionEffects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>" <?php echo e(old('effect') == $key ? 'selected' : ''); ?>>
                                            <?php echo e($value); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            
                            <div>
                                <label for="effect_value" class="block text-sm text-[#d9d3b8] mb-2">Значение эффекта
                                    <span class="text-[#ff6b6b]">*</span></label>
                                <input type="number" name="effect_value" id="effect_value"
                                    value="<?php echo e(old('effect_value', 10)); ?>" min="1" required
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <p class="text-xs text-[#d9d3b8] mt-1">Базовое значение эффекта зелья (например,
                                    количество восстанавливаемого здоровья)</p>
                            </div>

                            
                            <div>
                                <label for="effect_duration" class="block text-sm text-[#d9d3b8] mb-2">Длительность
                                    эффекта (сек) <span class="text-[#ff6b6b]">*</span></label>
                                <input type="number" name="effect_duration" id="effect_duration"
                                    value="<?php echo e(old('effect_duration', 60)); ?>" min="0" required
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <p class="text-xs text-[#d9d3b8] mt-1">Длительность эффекта в секундах (0 для мгновенных
                                    эффектов)</p>
                            </div>
                        </div>

                        
                        <div class="space-y-4">
                            
                            <div>
                                <label for="level" class="block text-sm text-[#d9d3b8] mb-2">Уровень зелья <span
                                        class="text-[#ff6b6b]">*</span></label>
                                <select name="level" id="level" required
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                    <option value="">-- Выберите уровень --</option>
                                    <?php $__currentLoopData = $potionLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $levelKey => $levelName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($levelKey); ?>" <?php echo e(old('level') == $levelKey ? 'selected' : ''); ?>>
                                            <?php echo e($levelName); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <p class="text-xs text-[#d9d3b8] mt-1">Выберите уровень зелья из доступных вариантов</p>
                            </div>

                            
                            <div>
                                <label for="quality" class="block text-sm text-[#d9d3b8] mb-2">Качество зелья <span
                                        class="text-[#ff6b6b]">*</span></label>
                                <select name="quality" id="quality" required
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                    <option value="">-- Выберите качество --</option>
                                    <?php $__currentLoopData = $potionQualities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quality): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($quality); ?>" <?php echo e(old('quality') == $quality ? 'selected' : ''); ?>>
                                            <?php echo e($quality); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            
                            <div>
                                <label for="uses_left" class="block text-sm text-[#d9d3b8] mb-2">Количество
                                    использований <span class="text-[#ff6b6b]">*</span></label>
                                <input type="number" name="uses_left" id="uses_left" value="<?php echo e(old('uses_left', 1)); ?>"
                                    min="1" required
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <p class="text-xs text-[#d9d3b8] mt-1">Начальное количество использований зелья</p>
                            </div>

                            
                            <div>
                                <label for="max_uses" class="block text-sm text-[#d9d3b8] mb-2">Максимальное количество
                                    использований <span class="text-[#ff6b6b]">*</span></label>
                                <input type="number" name="max_uses" id="max_uses" value="<?php echo e(old('max_uses', 1)); ?>"
                                    min="1" required
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <p class="text-xs text-[#d9d3b8] mt-1">Максимальное количество использований зелья (для
                                    отображения прогресса)</p>
                            </div>

                            
                            <div>
                                <label for="recipe_id" class="block text-sm text-[#d9d3b8] mb-2">Рецепт (если
                                    есть)</label>
                                <select name="recipe_id" id="recipe_id"
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                    <option value="">-- Без рецепта --</option>
                                    <?php $__currentLoopData = $recipes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($id); ?>" <?php echo e(old('recipe_id') == $id ? 'selected' : ''); ?>>
                                            <?php echo e($name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            
                            <div>
                                <label for="icon" class="block text-sm text-[#d9d3b8] mb-2">Иконка зелья <span
                                        class="text-[#ff6b6b]">*</span></label>
                                <input type="text" name="icon" id="icon"
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]"
                                    placeholder="potions/smallBottleHP.png" value="<?php echo e(old('icon')); ?>">
                                <p class="text-xs text-[#d9d3b8] mt-1">Укажите только имя файла из директории potions,
                                    например: potions/smallBottleHP.png</p>
                                <div class="mt-2">
                                    <p class="text-xs text-[#d9d3b8] mb-1">Доступные иконки зелий:</p>
                                    <div class="grid grid-cols-4 gap-2 mt-1">
                                        <div class="text-center">
                                            <img src="<?php echo e(asset('assets/potions/smallBottleHP.png')); ?>"
                                                alt="Малый флакон здоровья" class="w-8 h-8 mx-auto">
                                            <p class="text-xs mt-1">smallBottleHP.png</p>
                                        </div>
                                        <div class="text-center">
                                            <img src="<?php echo e(asset('assets/potions/mediumBottleHP.png')); ?>"
                                                alt="Средний флакон здоровья" class="w-8 h-8 mx-auto">
                                            <p class="text-xs mt-1">mediumBottleHP.png</p>
                                        </div>
                                        <div class="text-center">
                                            <img src="<?php echo e(asset('assets/potions/largeBottleHP.png')); ?>"
                                                alt="Большой флакон здоровья" class="w-8 h-8 mx-auto">
                                            <p class="text-xs mt-1">largeBottleHP.png</p>
                                        </div>
                                        <div class="text-center">
                                            <img src="<?php echo e(asset('assets/potions/smallBottleMP.png')); ?>"
                                                alt="Малый флакон маны" class="w-8 h-8 mx-auto">
                                            <p class="text-xs mt-1">smallBottleMP.png</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" name="color" id="color" value="#6c757d">

                            
                            <div class="mt-4">
                                <label class="flex items-center">
                                    <input type="checkbox" name="is_template" id="is_template" value="1" <?php echo e(old('is_template', true) ? 'checked' : ''); ?>

                                        class="w-5 h-5 bg-[#2a2721] border border-[#8c784e] rounded text-[#a6925e] focus:outline-none focus:border-[#e5b769]">
                                    <span class="ml-2 text-sm text-[#d9d3b8]">Это шаблон зелья</span>
                                </label>
                                <p class="text-xs text-[#d9d3b8] mt-1">Шаблоны используются для создания экземпляров
                                    зелий для игроков</p>
                            </div>
                        </div>

                        
                        <div class="md:col-span-2 flex justify-between mt-6">
                            <a href="<?php echo e(route('admin.potions.index')); ?>"
                                class="px-4 py-2 bg-[#4a4a3d] text-[#e5b769] rounded font-semibold hover:bg-[#5a5a4d] transition duration-300">
                                Отмена
                            </a>
                            <button type="submit"
                                class="px-6 py-2 bg-[#a6925e] text-[#2f2d2b] rounded-lg font-semibold hover:bg-[#e5b769] transition duration-300">
                                Создать зелье
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            
            <div class="container max-w-6xl mx-auto mb-6">
                <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] rounded-lg p-6 border-2 border-[#a6925e]">
                    <h2 class="text-xl font-bold text-[#e5b769] mb-4 text-center">Быстрое создание зелья из рецепта</h2>

                    <form action="<?php echo e(route('admin.potions.create-test')); ?>" method="POST" class="flex flex-col gap-4">
                        <?php echo csrf_field(); ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="recipe_id_quick" class="block text-sm text-[#d9d3b8] mb-2">Выберите
                                    рецепт</label>
                                <select name="recipe_id" id="recipe_id_quick" required
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                    <option value="">-- Выберите рецепт --</option>
                                    <?php $__currentLoopData = \App\Models\PotionRecipe::where('is_active', true)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recipe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($recipe->id); ?>"><?php echo e($recipe->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <div>
                                <label for="user_id" class="block text-sm text-[#d9d3b8] mb-2">Владелец
                                    (опционально)</label>
                                <select name="user_id" id="user_id"
                                    class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                    <option value="">-- Без владельца --</option>
                                    <?php $__currentLoopData = \App\Models\User::orderBy('name')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <div class="flex items-center mb-2">
                            <input type="checkbox" name="create_game_item" id="create_game_item" value="1" class="mr-2">
                            <label for="create_game_item" class="text-sm text-[#d9d3b8]">
                                Создать игровой предмет (будет добавлен в инвентарь игрока)
                            </label>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit"
                                class="px-4 py-2 bg-[#a6925e] text-[#2f2d2b] rounded font-semibold hover:bg-[#e5b769] transition duration-300">
                                Создать тестовое зелье
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            
            <footer
                class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] text-[#d9d3b8] py-3 mt-6 border-t-2 border-[#a6925e] rounded-lg">
                <div class="container max-w-6xl mx-auto px-6 text-center">
                    <div class="flex justify-between items-center flex-wrap">
                        <div>
                            <p class="text-sm font-semibold">
                                <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-[#e5b769] hover:underline">
                                    Вернуться в админ-панель
                                </a>
                            </p>
                        </div>

                        <div>
                            <p class="text-sm font-semibold">
                                Время: <span class="text-[#e5b769]"><?php echo e(date('H:i')); ?></span>
                            </p>
                        </div>

                        <div>
                            <form action="<?php echo e(route('logout')); ?>" method="POST" class="inline-block">
                                <?php echo csrf_field(); ?>
                                <button type="submit"
                                    class="bg-[#a6925e] text-[#2f2d2b] py-1 px-3 rounded font-semibold hover:bg-[#e5b769] transition duration-300">
                                    Выйти
                                </button>
                            </form>
                        </div>
                    </div>

                    <p class="text-sm font-semibold mt-3">
                        © <?php echo e(date('Y')); ?> Echoes of Eternity. Все права защищены.
                    </p>
                </div>
            </footer>
        </main>
    </div>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/potions/create.blade.php ENDPATH**/ ?>