<!DOCTYPE html>
<html lang="ru">


<head>
    <meta charset="UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>Управление магазином - Админ панель</title>
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    
</head>



<body class="bg-[#1a1814] text-[#d4cbb0] font-serif">
    
    <div class="min-h-screen flex flex-col">
        
        <main class="flex-grow container mx-auto px-4 py-6">
            
            <div
                class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] border-2 border-[#3b3629] rounded-lg shadow-lg mb-8 p-5">
                
                <div class="flex justify-between items-center mb-5">
                    
                    <a href="<?php echo e(route('admin.dashboard')); ?>" title="Вернуться в панель управления"
                        class="p-2 bg-[#3b3629] hover:bg-[#c1a96e] rounded-lg transition duration-300 border border-[#6e3f35]">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#c1a96e]" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M11 15l-3-3m0 0l3-3m-3 3h8M3 12a9 9 0 1118 0 9 9 0 01-18 0z" />
                        </svg>
                    </a>

                    
                    <h1 class="text-3xl font-bold text-[#e4d7b0]">Управление Магазином</h1>

                    
                    <div class="flex space-x-2">
                        
                        <a href="<?php echo e(route('admin.shop.create', ['type' => 'item'])); ?>"
                            title="Добавить новый предмет в магазин"
                            class="p-2 bg-[#3b3629] hover:bg-[#c1a96e] rounded-lg transition duration-300 border border-[#6e3f35]">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#c1a96e]" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </a>
                        
                        <a href="<?php echo e(route('admin.shop.create', ['type' => 'recipe'])); ?>"
                            title="Добавить новый рецепт в магазин"
                            class="p-2 bg-[#2f473c] hover:bg-[#c1a96e] rounded-lg transition duration-300 border border-[#2f473c]">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#c1a96e]" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                        </a>
                    </div>
                </div>

                
                <?php if(session('success')): ?>
                    <div class="bg-green-800 border border-green-600 text-green-100 px-4 py-3 rounded relative mb-4"
                        role="alert">
                        <span class="block sm:inline"><?php echo e(session('success')); ?></span>
                    </div>
                <?php endif; ?>
                <?php if(session('error')): ?>
                    <div class="bg-red-800 border border-red-600 text-red-100 px-4 py-3 rounded relative mb-4" role="alert">
                        <span class="block sm:inline"><?php echo e(session('error')); ?></span>
                    </div>
                <?php endif; ?>
            </div>

            
            <div class="mb-6">
                <div class="border-b border-[#3b3629]">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        
                        <a href="<?php echo e(route('admin.shop.index', ['tab' => 'items'])); ?>"
                            class="<?php if($activeTab === 'items'): ?> border-[#c1a96e] text-[#e4d7b0] <?php else: ?> border-transparent text-[#998d66] hover:text-[#d4cbb0] hover:border-[#998d66] <?php endif; ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition duration-300">
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            </svg>
                            Предметы (<?php echo e($shopItems->total()); ?>)
                        </a>
                        
                        <a href="<?php echo e(route('admin.shop.index', ['tab' => 'recipes'])); ?>"
                            class="<?php if($activeTab === 'recipes'): ?> border-[#c1a96e] text-[#e4d7b0] <?php else: ?> border-transparent text-[#998d66] hover:text-[#d4cbb0] hover:border-[#998d66] <?php endif; ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition duration-300">
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                            Рецепты (<?php echo e($shopRecipes->total()); ?>)
                        </a>
                    </nav>
                </div>
            </div>

            
            <?php if($activeTab === 'items'): ?>
                
                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg shadow-md overflow-hidden">
                    <table class="min-w-full divide-y divide-[#514b3c]">
                        
                        <thead class="bg-[#3d3a2e]">
                            <tr>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    ID</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Иконка</th>
                                
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Название</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Тип</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Качество</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Слот</th>
                                
                                <th scope="col"
                                    class="px-2 py-3 text-center text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Золото</th>
                                
                                <th scope="col"
                                    class="px-2 py-3 text-center text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Серебро</th>
                                
                                <th scope="col"
                                    class="px-2 py-3 text-center text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Бронза</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-center text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Доступен</th>
                                
                                <th scope="col"
                                    class="px-6 py-3 text-center text-xs font-medium text-[#e5b769] uppercase tracking-wider">
                                    Действия</th>
                            </tr>
                        </thead>
                        
                        <tbody class="bg-[#211f1a] divide-y divide-[#4a452c]">
                            
                            <?php $__empty_1 = true; $__currentLoopData = $shopItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shopItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                
                                <tr>
                                    
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#9a9483]"><?php echo e($shopItem->id); ?></td>
                                    
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?php if($shopItem->item && $shopItem->item->icon): ?>
                                            <img src="<?php echo e(asset($shopItem->item->icon)); ?>"
                                                alt="<?php echo e($shopItem->item->name ?? 'Иконка'); ?>"
                                                class="w-8 h-8 rounded border border-[#a6925e]">
                                        <?php else: ?>
                                            
                                            <div
                                                class="w-8 h-8 rounded border border-[#514b3c] bg-[#3d3a2e] flex items-center justify-center text-[#9a9483]">
                                                ?</div>
                                        <?php endif; ?>
                                    </td>
                                    
                                    <td class="px-6 py-3 whitespace-nowrap text-sm font-medium text-[#d9d3b8]">
                                        
                                        <?php echo e($shopItem->item->name ?? 'Предмет не найден'); ?>

                                    </td>
                                    
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#b0a890]">
                                        <?php echo e($shopItem->item->type ?? '-'); ?>

                                    </td>
                                    
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#b0a890]">
                                        <?php echo e($shopItem->item->quality ?? '-'); ?>

                                    </td>
                                    
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-[#e5b769]">
                                        <?php if($shopItem->item && $shopItem->item->type): ?>
                                            <?php echo e(\App\Models\GameItem::getSlotForItemType($shopItem->item->type) ?? 'Не определен'); ?>

                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    
                                    <td class="px-2 py-3 whitespace-nowrap text-sm text-center text-[#ffd700]">
                                        <?php echo e($shopItem->price_gold ?? 0); ?>

                                        
                                    </td>
                                    
                                    <td class="px-2 py-3 whitespace-nowrap text-sm text-center text-[#c0c0c0]">
                                        <?php echo e($shopItem->price_silver ?? 0); ?>

                                    </td>
                                    
                                    <td class="px-2 py-3 whitespace-nowrap text-sm text-center text-[#cd7f32]">
                                        <?php echo e($shopItem->price_bronze ?? 0); ?>

                                    </td>
                                    
                                    <td class="px-4 py-3 whitespace-nowrap text-center text-sm">
                                        <?php if($shopItem->is_available): ?>
                                            <span class="text-green-400">✔ Да</span>
                                        <?php else: ?>
                                            <span class="text-red-400">✘ Нет</span>
                                        <?php endif; ?>
                                    </td>
                                    
                                    <td class="px-6 py-3 whitespace-nowrap text-center text-sm font-medium">
                                        
                                        <a href="<?php echo e(route('admin.shop.edit', $shopItem->id)); ?>"
                                            class="text-[#a6925e] hover:text-[#e5b769] mr-3 transition duration-300"
                                            title="Редактировать цену">✏️</a>
                                        
                                        <form action="<?php echo e(route('admin.shop.destroy', $shopItem->id)); ?>" method="POST"
                                            class="inline-block"
                                            onsubmit="return confirm('Вы уверены, что хотите удалить этот предмет из магазина?');">
                                            <?php echo csrf_field(); ?>
                                            
                                            <?php echo method_field('DELETE'); ?>
                                            
                                            <button type="submit"
                                                class="text-red-500 hover:text-red-700 transition duration-300"
                                                title="Удалить из магазина">🗑️</button>
                                        </form>
                                    </td>
                                </tr>
                                
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    
                                    <td colspan="11" class="px-6 py-4 whitespace-nowrap text-center text-sm text-[#9a9483]">
                                        Предметы в магазине пока не добавлены.
                                    </td>
                                </tr>
                            <?php endif; ?>
                            
                        </tbody>
                    </table>
                </div>

                
                <div class="mt-6">
                    
                    <?php echo e($shopItems->appends(['tab' => 'items'])->links()); ?>

                </div>

            <?php else: ?>
                
                <div class="bg-[#1a1814] border border-[#3b3629] rounded-lg shadow-md overflow-hidden">
                    <table class="min-w-full divide-y divide-[#3b3629]">
                        
                        <thead class="bg-[#2a2722]">
                            <tr>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    ID</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Иконка</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Название</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Качество</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Уровень</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Золото</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Серебро</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Бронза</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Категория</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Доступен</th>
                                
                                <th scope="col"
                                    class="px-4 py-3 text-left text-xs font-medium text-[#e4d7b0] uppercase tracking-wider">
                                    Действия</th>
                            </tr>
                        </thead>
                        <tbody class="bg-[#1a1814] divide-y divide-[#3b3629]">
                            
                            <?php $__empty_1 = true; $__currentLoopData = $shopRecipes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shopRecipe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="hover:bg-[#2a2722] transition duration-300">
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                        <?php echo e($shopRecipe->potionRecipe->id ?? 'N/A'); ?>

                                    </td>
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                        <?php if($shopRecipe->potionRecipe && $shopRecipe->potionRecipe->icon): ?>
                                            <img src="<?php echo e(asset($shopRecipe->potionRecipe->icon)); ?>" alt="Иконка рецепта"
                                                class="w-8 h-8 rounded">
                                        <?php else: ?>
                                            <span class="text-[#998d66]">Нет иконки</span>
                                        <?php endif; ?>
                                    </td>
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-[#e4d7b0]">
                                        <?php echo e($shopRecipe->potionRecipe->name ?? 'Неизвестный рецепт'); ?>

                                    </td>
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#c1a96e]">
                                        <?php echo e($shopRecipe->potionRecipe->quality ?? 'N/A'); ?>

                                    </td>
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                        <?php echo e($shopRecipe->potionRecipe->level ?? 'N/A'); ?>

                                    </td>
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#c1a96e]">
                                        <?php echo e($shopRecipe->price_gold); ?>

                                    </td>
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#998d66]">
                                        <?php echo e($shopRecipe->price_silver); ?>

                                    </td>
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#6e3f35]">
                                        <?php echo e($shopRecipe->price_bronze); ?>

                                    </td>
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-[#d4cbb0]">
                                        <?php echo e($shopRecipe->shop_category ?? 'Без категории'); ?>

                                    </td>
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm">
                                        <?php if($shopRecipe->is_available): ?>
                                            <span class="text-[#2f473c] bg-[#1e2e27] px-2 py-1 rounded text-xs">Да</span>
                                        <?php else: ?>
                                            <span class="text-[#59372d] bg-[#3c221b] px-2 py-1 rounded text-xs">Нет</span>
                                        <?php endif; ?>
                                    </td>
                                    
                                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                        
                                        <form action="<?php echo e(route('admin.shop.destroy-recipe', $shopRecipe)); ?>" method="POST"
                                            style="display: inline;"
                                            onsubmit="return confirm('Вы уверены, что хотите удалить этот рецепт из магазина?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit"
                                                class="text-[#59372d] hover:text-[#6e3f35] transition duration-300"
                                                title="Удалить из магазина">🗑️</button>
                                        </form>
                                    </td>
                                </tr>
                                
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    
                                    <td colspan="11" class="px-6 py-4 whitespace-nowrap text-center text-sm text-[#998d66]">
                                        Рецепты в магазине пока не добавлены.
                                    </td>
                                </tr>
                            <?php endif; ?>
                            
                        </tbody>
                    </table>
                </div>

                
                <div class="mt-6">
                    
                    <?php echo e($shopRecipes->appends(['tab' => 'recipes'])->links()); ?>

                </div>
            <?php endif; ?>

        </main>

        
        <footer class="bg-[#1a1814] text-[#998d66] py-4 mt-auto border-t border-[#3b3629]">
            <div class="container max-w-7xl mx-auto px-6 text-center text-sm">
                © <?php echo e(date('Y')); ?> Echoes of Eternity | Административная Панель
            </div>
        </footer>
    </div>

</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/shop/index.blade.php ENDPATH**/ ?>