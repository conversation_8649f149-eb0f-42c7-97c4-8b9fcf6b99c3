<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'route' => '#',
    'title' => 'Локация',
    'description' => 'Описание локации',
    'icon' => 'assets/default.png',
    'iconEmoji' => '🏠',
    'isActive' => true,
    'statusText' => 'Активно',
    'statusColor' => 'green'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'route' => '#',
    'title' => 'Локация',
    'description' => 'Описание локации',
    'icon' => 'assets/default.png',
    'iconEmoji' => '🏠',
    'isActive' => true,
    'statusText' => 'Активно',
    'statusColor' => 'green'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>



<?php if($isActive): ?>
    
    <a href="<?php echo e(route($route)); ?>"
        class="location-card group relative block p-4 bg-gradient-to-r from-[#2a2621] via-[#312e27] to-[#38352c] border-2 border-[#514b3c] rounded-lg shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 hover:border-[#a6925e] overflow-hidden active:scale-95 touch-manipulation">
        
        
        <div class="absolute inset-0 bg-gradient-to-r from-[#a6925e]/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <div class="flex items-center relative z-10">
            
            <div class="relative mr-4 flex-shrink-0">
                <div class="w-14 h-14 flex items-center justify-center bg-gradient-to-br from-[#252117] to-[#1a1611] rounded-xl border-2 border-[#514b3c] group-hover:border-[#a6925e] transition-all duration-300 group-hover:scale-110">
                    <img src="<?php echo e(asset($icon)); ?>" alt="<?php echo e($title); ?>"
                        class="w-9 h-9 object-contain group-hover:brightness-110 transition-all duration-300"
                        onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <span class="text-2xl group-hover:scale-110 transition-transform duration-300" style="display: none;"><?php echo e($iconEmoji); ?></span>
                </div>
                
                <div class="absolute inset-0 bg-[#a6925e]/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            
            <div class="flex-grow min-w-0">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center">
                        <h3 class="text-[#e5b769] font-semibold text-lg group-hover:text-[#f7d08a] transition-colors duration-300"><?php echo e($title); ?></h3>
                        <div class="ml-3 px-2 py-1 bg-[#2E8B57]/20 border border-[#2E8B57]/40 rounded-full">
                            <span class="text-[#4ade80] text-xs font-medium"><?php echo e($statusText); ?></span>
                        </div>
                    </div>
                    
                    <div class="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-[#252117] to-[#1a1611] rounded-full border-2 border-[#514b3c] group-hover:border-[#a6925e] transition-all duration-300 group-hover:translate-x-1 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#e5b769] group-hover:text-[#f7d08a] transition-colors duration-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </div>
                </div>
                <p class="text-[#a09a8a] text-sm leading-relaxed group-hover:text-[#b5afa0] transition-colors duration-300"><?php echo e($description); ?></p>
            </div>
        </div>
    </a>
<?php else: ?>
    
    <div class="group relative block p-4 bg-gradient-to-r from-[#2a2621] via-[#312e27] to-[#38352c] border-2 border-[#514b3c] rounded-lg shadow-lg opacity-70 cursor-not-allowed overflow-hidden">
        
        
        <div class="absolute inset-0 bg-gradient-to-r from-[#514b3c]/10 via-transparent to-transparent"></div>

        <div class="flex items-center relative z-10">
            
            <div class="relative mr-4 flex-shrink-0">
                <div class="w-14 h-14 flex items-center justify-center bg-gradient-to-br from-[#252117] to-[#1a1611] rounded-xl border-2 border-[#514b3c] opacity-60">
                    <img src="<?php echo e(asset($icon)); ?>" alt="<?php echo e($title); ?>"
                        class="w-9 h-9 object-contain grayscale"
                        onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <span class="text-2xl grayscale" style="display: none;"><?php echo e($iconEmoji); ?></span>
                </div>
            </div>

            
            <div class="flex-grow min-w-0">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center">
                        <h3 class="text-[#a09a8a] font-semibold text-lg"><?php echo e($title); ?></h3>
                        <div class="ml-3 px-2 py-1 bg-[#e74c3c]/20 border border-[#e74c3c]/40 rounded-full">
                            <span class="text-[#f87171] text-xs font-medium"><?php echo e($statusText); ?></span>
                        </div>
                    </div>
                    
                    <div class="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-[#252117] to-[#1a1611] rounded-full border-2 border-[#514b3c] flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#a09a8a]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-[#a09a8a] text-sm leading-relaxed"><?php echo e($description); ?></p>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/location-card.blade.php ENDPATH**/ ?>