# Руководство по тестированию системы зелий

## Исправленные проблемы

1. **Проблема с полем level**: Исправлена валидация - теперь используется выпадающий список с правильными значениями (basic, advanced, expert, master)
2. **Проблема с полем icon**: Исправлена валидация - теперь принимается строка с путем к иконке вместо файла

## Тестирование создания зелья в админке

### Шаг 1: Авторизация в админке
1. Перейдите по адресу: http://127.0.0.1:8000/admin/login
2. Введите данные:
   - Имя: admin
   - Пароль: qwe123

### Шаг 2: Создание зелья
1. Перейдите по адресу: http://127.0.0.1:8000/admin/potions/create
2. Заполните форму:
   - **Название**: Тестовое зелье здоровья
   - **Описание**: Восстанавливает 50 HP
   - **Эффект**: health (Зелье здоровья)
   - **Значение эффекта**: 50
   - **Длительность эффекта**: 0 (мгновенное)
   - **Уровень зелья**: basic (Базовое)
   - **Качество зелья**: Обычное
   - **Количество использований**: 1
   - **Максимальное количество использований**: 1
   - **Рецепт**: Выберите "Малое зелье жизни" (если доступен)
   - **Иконка зелья**: potions/smallBottleHP.png
   - **Цвет**: #ff0000
   - **Является шаблоном**: Отметить галочку

### Шаг 3: Проверка создания
После создания зелья вы должны увидеть сообщение об успешном создании и быть перенаправлены на список зелий.

## Тестирование системы алхимии для игроков

### Шаг 1: Авторизация игрока
1. Перейдите по адресу: http://127.0.0.1:8000/auth/login
2. Авторизуйтесь как обычный игрок

### Шаг 2: Переход к алхимику
1. Перейдите по адресу: http://127.0.0.1:8000/alchemist
2. Проверьте, что страница загружается без ошибок

### Шаг 3: Проверка рецептов
1. На странице алхимика должны отображаться доступные рецепты
2. Проверьте, что рецепт "Малое зелье жизни" отображается корректно

## Команды для тестирования

### Проверка рецептов в базе данных
```bash
php check_recipes.php
```

### Проверка зелий в базе данных
```bash
php artisan tinker
App\Models\Potion::all(['id', 'name', 'level', 'is_template'])
```

### Запуск сервера
```bash
php artisan serve
```

## Ожидаемые результаты

1. Форма создания зелья должна работать без ошибок валидации
2. Поле "Уровень зелья" должно быть выпадающим списком с опциями: Базовое, Продвинутое, Экспертное, Мастерское
3. Поле "Иконка зелья" должно принимать строку с путем к файлу
4. Зелье должно успешно создаваться и сохраняться в базе данных
5. Система алхимии для игроков должна корректно отображать доступные рецепты
