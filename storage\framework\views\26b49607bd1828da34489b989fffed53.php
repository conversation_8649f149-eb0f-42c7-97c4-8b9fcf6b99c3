<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?> 

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Эльфийская Гавань - <?php echo e(Auth::check() ? Auth::user()->name : 'Гость'); ?></title>

    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    
    <div
        class="container max-w-md mx-auto px-1 py-0 bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden">

        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasBrokenItems' => $hasBrokenItems ?? false,'brokenItemsCount' => $brokenItemsCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBrokenItems ?? false),'brokenItemsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($brokenItemsCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile,'experienceProgress' => $experienceProgress ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile),'experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>

        
        <div class="text-center flex justify-center space-x-1">
            <?php if(session('welcome_message')): ?>
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    <?php echo e(session('welcome_message')); ?>

                </div>
            <?php endif; ?>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.game-flash-messages','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('game-flash-messages'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6)): ?>
<?php $attributes = $__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6; ?>
<?php unset($__attributesOriginal0a9eda54510e94b9f8681b0ba238a8a6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6)): ?>
<?php $component = $__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6; ?>
<?php unset($__componentOriginal0a9eda54510e94b9f8681b0ba238a8a6); ?>
<?php endif; ?>

        
        <div class="mb-2">
            
            <?php if (isset($component)) { $__componentOriginal5e903ecf51adf2fc13a9a2695cfb0e86 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5e903ecf51adf2fc13a9a2695cfb0e86 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.active-effects','data' => ['userEffects' => $userEffects]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.active-effects'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userEffects' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userEffects)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5e903ecf51adf2fc13a9a2695cfb0e86)): ?>
<?php $attributes = $__attributesOriginal5e903ecf51adf2fc13a9a2695cfb0e86; ?>
<?php unset($__attributesOriginal5e903ecf51adf2fc13a9a2695cfb0e86); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5e903ecf51adf2fc13a9a2695cfb0e86)): ?>
<?php $component = $__componentOriginal5e903ecf51adf2fc13a9a2695cfb0e86; ?>
<?php unset($__componentOriginal5e903ecf51adf2fc13a9a2695cfb0e86); ?>
<?php endif; ?>

            
            <?php
                $isStunned = $userEffects->contains(function ($effect) {
                    return $effect->skill_id == 14 && $effect->isActive();
                });
            ?>

            
            
            <?php if (isset($component)) { $__componentOriginalb2e10d24a20f6cc9166886822f64e009 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb2e10d24a20f6cc9166886822f64e009 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.outpost-header','data' => ['breadcrumbs' => $breadcrumbs,'title' => $locationName ?? 'Эльфийская Гавань','useCustomLocationName' => false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.outpost-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs),'title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($locationName ?? 'Эльфийская Гавань'),'useCustomLocationName' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb2e10d24a20f6cc9166886822f64e009)): ?>
<?php $attributes = $__attributesOriginalb2e10d24a20f6cc9166886822f64e009; ?>
<?php unset($__attributesOriginalb2e10d24a20f6cc9166886822f64e009); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb2e10d24a20f6cc9166886822f64e009)): ?>
<?php $component = $__componentOriginalb2e10d24a20f6cc9166886822f64e009; ?>
<?php unset($__componentOriginalb2e10d24a20f6cc9166886822f64e009); ?>
<?php endif; ?>
        </div>

        <!-- Блок фракций и деревень -->
        <div class="mt-2">
            <!-- Статус фракций -->
            <?php if (isset($component)) { $__componentOriginalb22308892d0e76a700f735dedd90d9f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb22308892d0e76a700f735dedd90d9f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.faction-status','data' => ['solWarriors' => $solWarriors,'solMages' => $solMages,'solKnights' => $solKnights,'lunWarriors' => $lunWarriors,'lunMages' => $lunMages,'lunKnights' => $lunKnights]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.faction-status'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['solWarriors' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($solWarriors),'solMages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($solMages),'solKnights' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($solKnights),'lunWarriors' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lunWarriors),'lunMages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lunMages),'lunKnights' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lunKnights)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb22308892d0e76a700f735dedd90d9f6)): ?>
<?php $attributes = $__attributesOriginalb22308892d0e76a700f735dedd90d9f6; ?>
<?php unset($__attributesOriginalb22308892d0e76a700f735dedd90d9f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb22308892d0e76a700f735dedd90d9f6)): ?>
<?php $component = $__componentOriginalb22308892d0e76a700f735dedd90d9f6; ?>
<?php unset($__componentOriginalb22308892d0e76a700f735dedd90d9f6); ?>
<?php endif; ?>

            <!-- Деревни и обелиск -->
            <div class="grid grid-cols-3 gap-2 w-full">
                <?php
                    $userRace = auth()->user()->profile->race;
                    // Получаем все деревни для текущей локации
                    $villagesCount = count($villages ?? []);
                    // Определяем, сколько деревень будет до и после обелиска
                    $villagesBefore = floor($villagesCount / 2);
                    $villagesAfter = $villagesCount - $villagesBefore;
                ?>

                <!-- Деревни перед обелиском -->
                <?php $__currentLoopData = $villages ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $village): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($index < $villagesBefore): ?>
                        <?php if (isset($component)) { $__componentOriginal02722e862209f31fcbcb74afaa31a8bc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal02722e862209f31fcbcb74afaa31a8bc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.outposts.village-block','data' => ['village' => $village,'userRace' => $userRace,'villageName' => $village->name,'routePrefix' => 'battle.outposts','locationId' => $outpostLocation->id]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.outposts.village-block'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['village' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($village),'userRace' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userRace),'villageName' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($village->name),'routePrefix' => 'battle.outposts','locationId' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($outpostLocation->id)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal02722e862209f31fcbcb74afaa31a8bc)): ?>
<?php $attributes = $__attributesOriginal02722e862209f31fcbcb74afaa31a8bc; ?>
<?php unset($__attributesOriginal02722e862209f31fcbcb74afaa31a8bc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal02722e862209f31fcbcb74afaa31a8bc)): ?>
<?php $component = $__componentOriginal02722e862209f31fcbcb74afaa31a8bc; ?>
<?php unset($__componentOriginal02722e862209f31fcbcb74afaa31a8bc); ?>
<?php endif; ?>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <!-- Обелиск -->
                <?php if (isset($component)) { $__componentOriginalaab779bdcad3a00325834d172a2439cd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaab779bdcad3a00325834d172a2439cd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.outposts.obelisk-block','data' => ['obelisk' => $obelisk ?? null,'user' => $user ?? null,'routePrefix' => 'battle.outposts']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.outposts.obelisk-block'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['obelisk' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($obelisk ?? null),'user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user ?? null),'routePrefix' => 'battle.outposts']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaab779bdcad3a00325834d172a2439cd)): ?>
<?php $attributes = $__attributesOriginalaab779bdcad3a00325834d172a2439cd; ?>
<?php unset($__attributesOriginalaab779bdcad3a00325834d172a2439cd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaab779bdcad3a00325834d172a2439cd)): ?>
<?php $component = $__componentOriginalaab779bdcad3a00325834d172a2439cd; ?>
<?php unset($__componentOriginalaab779bdcad3a00325834d172a2439cd); ?>
<?php endif; ?>

                <!-- Деревни после обелиска -->
                <?php $__currentLoopData = $villages ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $village): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($index >= $villagesBefore && $index < $villagesBefore + $villagesAfter): ?>
                        <?php if (isset($component)) { $__componentOriginal02722e862209f31fcbcb74afaa31a8bc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal02722e862209f31fcbcb74afaa31a8bc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.outposts.village-block','data' => ['village' => $village,'userRace' => $userRace,'villageName' => $village->name,'routePrefix' => 'battle.outposts','locationId' => $outpostLocation->id]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.outposts.village-block'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['village' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($village),'userRace' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userRace),'villageName' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($village->name),'routePrefix' => 'battle.outposts','locationId' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($outpostLocation->id)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal02722e862209f31fcbcb74afaa31a8bc)): ?>
<?php $attributes = $__attributesOriginal02722e862209f31fcbcb74afaa31a8bc; ?>
<?php unset($__attributesOriginal02722e862209f31fcbcb74afaa31a8bc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal02722e862209f31fcbcb74afaa31a8bc)): ?>
<?php $component = $__componentOriginal02722e862209f31fcbcb74afaa31a8bc; ?>
<?php unset($__componentOriginal02722e862209f31fcbcb74afaa31a8bc); ?>
<?php endif; ?>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Блок действий с целью -->
            <?php if (isset($component)) { $__componentOriginaled0e60e35ab99f82553f64fa5816db40 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaled0e60e35ab99f82553f64fa5816db40 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.target-block','data' => ['target' => $target ?? null,'isStunned' => $isStunned ?? false,'lastAttacker' => $lastAttacker ?? null,'lastAttackerResources' => $lastAttackerResources ?? null,'routePrefix' => 'battle.outposts']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.target-block'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['target' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($target ?? null),'isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned ?? false),'lastAttacker' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lastAttacker ?? null),'lastAttackerResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lastAttackerResources ?? null),'routePrefix' => 'battle.outposts']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaled0e60e35ab99f82553f64fa5816db40)): ?>
<?php $attributes = $__attributesOriginaled0e60e35ab99f82553f64fa5816db40; ?>
<?php unset($__attributesOriginaled0e60e35ab99f82553f64fa5816db40); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaled0e60e35ab99f82553f64fa5816db40)): ?>
<?php $component = $__componentOriginaled0e60e35ab99f82553f64fa5816db40; ?>
<?php unset($__componentOriginaled0e60e35ab99f82553f64fa5816db40); ?>
<?php endif; ?>

            <!-- Панель быстрого использования зелий -->
            <div class="mt-2 mb-2">
                <?php if (isset($component)) { $__componentOriginaleedfaed7b075f91ddad1da97aa706e0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleedfaed7b075f91ddad1da97aa706e0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user.quick-potion-bar','data' => ['class' => 'flex justify-center items-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('user.quick-potion-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'flex justify-center items-center']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleedfaed7b075f91ddad1da97aa706e0d)): ?>
<?php $attributes = $__attributesOriginaleedfaed7b075f91ddad1da97aa706e0d; ?>
<?php unset($__attributesOriginaleedfaed7b075f91ddad1da97aa706e0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleedfaed7b075f91ddad1da97aa706e0d)): ?>
<?php $component = $__componentOriginaleedfaed7b075f91ddad1da97aa706e0d; ?>
<?php unset($__componentOriginaleedfaed7b075f91ddad1da97aa706e0d); ?>
<?php endif; ?>
            </div>

            <!-- Панель умений -->
            <?php if (isset($component)) { $__componentOriginal4923a1cb42f469880881d13f1dd35a84 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4923a1cb42f469880881d13f1dd35a84 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.skills-panel','data' => ['routePrefix' => 'battle.outposts','isStunned' => $isStunned ?? false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.skills-panel'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['routePrefix' => 'battle.outposts','isStunned' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isStunned ?? false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4923a1cb42f469880881d13f1dd35a84)): ?>
<?php $attributes = $__attributesOriginal4923a1cb42f469880881d13f1dd35a84; ?>
<?php unset($__attributesOriginal4923a1cb42f469880881d13f1dd35a84); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4923a1cb42f469880881d13f1dd35a84)): ?>
<?php $component = $__componentOriginal4923a1cb42f469880881d13f1dd35a84; ?>
<?php unset($__componentOriginal4923a1cb42f469880881d13f1dd35a84); ?>
<?php endif; ?>

            
            <?php if (isset($component)) { $__componentOriginalf6e51e9619f8d3a8bf3ed643fe935182 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf6e51e9619f8d3a8bf3ed643fe935182 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.party.members-bar','data' => ['partyMembers' => $partyMembers ?? collect(),'currentUserId' => $user->id ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('party.members-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['partyMembers' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($partyMembers ?? collect()),'currentUserId' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user->id ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf6e51e9619f8d3a8bf3ed643fe935182)): ?>
<?php $attributes = $__attributesOriginalf6e51e9619f8d3a8bf3ed643fe935182; ?>
<?php unset($__attributesOriginalf6e51e9619f8d3a8bf3ed643fe935182); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf6e51e9619f8d3a8bf3ed643fe935182)): ?>
<?php $component = $__componentOriginalf6e51e9619f8d3a8bf3ed643fe935182; ?>
<?php unset($__componentOriginalf6e51e9619f8d3a8bf3ed643fe935182); ?>
<?php endif; ?>

            
            <?php if (isset($component)) { $__componentOriginal3f9d36c673437b9c6830ab5b64ad67c9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3f9d36c673437b9c6830ab5b64ad67c9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.battle-logs','data' => ['battleLogs' => $battleLogs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.battle-logs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['battleLogs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($battleLogs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3f9d36c673437b9c6830ab5b64ad67c9)): ?>
<?php $attributes = $__attributesOriginal3f9d36c673437b9c6830ab5b64ad67c9; ?>
<?php unset($__attributesOriginal3f9d36c673437b9c6830ab5b64ad67c9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3f9d36c673437b9c6830ab5b64ad67c9)): ?>
<?php $component = $__componentOriginal3f9d36c673437b9c6830ab5b64ad67c9; ?>
<?php unset($__componentOriginal3f9d36c673437b9c6830ab5b64ad67c9); ?>
<?php endif; ?>

        </div>

        
        <div class="container mx-auto text-center px-2 py-2 flex justify-center space-x-2">
            
            <a href="<?php echo e(route('inventory.index')); ?>"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Рюкзак
            </a>
            
            <a href="<?php echo e(route('user.profile')); ?>"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Персонаж
            </a>
            
            <a href="<?php echo e(route('party.index')); ?>"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Группа
            </a>
            
            <a href="<?php echo e(route('guilds.index')); ?>"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Гильдия
            </a>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
        
        <style>
            @keyframes messagePulse {
                0% {
                    filter: drop-shadow(0 0 0 rgba(231, 76, 60, 0));
                }

                50% {
                    filter: drop-shadow(0 0 5px rgba(231, 76, 60, 0.7));
                }

                100% {
                    filter: drop-shadow(0 0 0 rgba(231, 76, 60, 0));
                }
            }

            @keyframes fadeUp {
                0% {
                    opacity: 0;
                    transform: translateY(0);
                }

                20% {
                    opacity: 1;
                }

                80% {
                    opacity: 1;
                }

                100% {
                    opacity: 0;
                    transform: translateY(-15px);
                }
            }

            .animate-fade-up {
                animation: fadeUp 2s ease-out forwards;
            }

            #progressBar {
                transition: width 0.1s linear;
            }
        </style>

        
        <?php echo app('Illuminate\Foundation\Vite')(['resources/js/attackLimiter.js']); ?>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/battle/outposts/locations/elve_haven.blade.php ENDPATH**/ ?>