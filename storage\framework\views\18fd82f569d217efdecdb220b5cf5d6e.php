<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Регистрация</title>

    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    
    <div class="container max-w-md mx-auto px-1 py-0 border-2 border-[#a6925e] rounded-lg flex-grow relative">
        
        <div class="flex justify-between items-center text-[#d9d3b8] rounded-md pt-1.5 shadow-inner">
            
            <div class="flex items-center">
                <div
                    class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] mr-1">
                    <span class="text-[#FF6347] text-xs">❤️</span>
                </div>
                <div class="flex flex-col">
                    <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                        <div class="bg-gradient-to-r from-[#8B0000] to-[#FF6347] h-full rounded-full"
                            style="width: calc(<?php echo e($actualResources['current_hp']); ?>/<?php echo e($userProfile->max_hp); ?>*100%)">
                        </div>
                    </div>
                    <span
                        class="text-[#e5b769] text-[12px]"><?php echo e($actualResources['current_hp']); ?>/<?php echo e($userProfile->max_hp); ?></span>
                </div>
            </div>

            
            <div class="flex space-x-1">
                
                <a href="/mail" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">📩</span>
                    </div>
                    <span
                        class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center">
                        3
                    </span>
                </a>

                
                <a href="/events" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">🎉</span>
                    </div>
                    <span
                        class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center">
                        3
                    </span>
                </a>

                
                <a href="/quests" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">📜</span>
                    </div>
                    <span
                        class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center">
                        5
                    </span>
                </a>
            </div>

            
            <div class="flex items-center">
                <div class="flex flex-col items-end">
                    <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                        <div class="bg-gradient-to-r from-[#00008B] to-[#1E90FF] h-full rounded-full"
                            style="width: calc(<?php echo e($userProfile->mp); ?>/<?php echo e($userProfile->max_mp); ?>*100%)"></div>
                    </div>
                    <span class="text-[#e5b769] text-[12px]"><?php echo e($userProfile->mp); ?>/<?php echo e($userProfile->max_mp); ?></span>
                </div>
                <div
                    class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] ml-1">
                    <span class="text-[#1E90FF] text-xs">🔮</span>
                </div>
            </div>
        </div>

        
        <div class="w-full bg-[#6b6658] mt-0.5 h-0.5 rounded-full mb-5">
            <div class="bg-[#e5b769] h-0.5 rounded-full" style="width: 50%;"></div>
            <div class="flex gap-1 items-center">
                
                <div class="flex items-center">
                    <img src="<?php echo e(asset('assets/goldIcon.png')); ?>" alt="Золото" class="w-4 h-4">
                    <span class="text-sm font-medium text-[#e5b769]">
                        <?php echo e(number_format($userProfile->gold, 0, ',', ' ')); ?>

                    </span>
                </div>
                
                <div class="flex items-center">
                    <img src="<?php echo e(asset('assets/silverIcon.png')); ?>" alt="Серебро" class="w-4 h-4">
                    <span class="text-sm font-medium text-[#c0c0c0]">
                        <?php echo e(number_format($userProfile->silver, 0, ',', ' ')); ?>

                    </span>
                </div>
                
                <div class="flex items-center">
                    <img src="<?php echo e(asset('assets/bronzeIcon.png')); ?>" alt="Бронза" class="w-4 h-4">
                    <span class="text-sm font-medium text-[#cd7f32]">
                        <?php echo e(number_format($userProfile->bronze, 0, ',', ' ')); ?>

                    </span>
                </div>
            </div>
        </div>

        <div class="text-center flex justify-center space-x-1 max-w-[300px] mx-auto">
            <?php if(session('welcome_message')): ?>
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    <?php echo e(session('welcome_message')); ?>

                </div>
            <?php endif; ?>
        </div>

        
        <div class="mb-2 w-full mx-auto">
            <?php if (isset($component)) { $__componentOriginal360d002b1b676b6f84d43220f22129e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal360d002b1b676b6f84d43220f22129e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumbs','data' => ['breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $attributes = $__attributesOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__attributesOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $component = $__componentOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__componentOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
            <p
                class="block text-center mx-auto text-white py-1 px-8 rounded-sm shadow-lg bg-gradient-to-b from-[#2E8B57]">
                Главная
            </p>
        </div>
        
        <div class="text-center my-2">
            <h1 class="text-xl font-semibold text-[#e5b769]">Кулинарная мастерская</h1>
            <p class="text-xs text-[#d3c6a6] mt-1">Создавайте кулинарные шедевры из доступных ингредиентов</p>

            
            <div class="flex items-center justify-center mt-2">
                <span class="text-xs text-[#9a9483] mr-2">Уровень навыка:</span>
                <div class="w-24 h-2.5 bg-[#2a261f] rounded-full overflow-hidden">
                    <div class="h-full bg-gradient-to-r from-[#a6925e] to-[#e5b769] rounded-full" style="width: 45%">
                    </div>
                </div>
                <span class="text-xs text-[#e5b769] ml-2">4/10</span>
            </div>
        </div>

        
        <div class="mt-3 relative">
            
            <div
                class="bg-gradient-to-b from-[#3c3628] to-[#2a261f] rounded-lg p-3 border border-[#514b3c] relative overflow-hidden">
                
                <div class="absolute top-0 left-0 w-full h-full opacity-10">
                    <div class="absolute top-2 left-2 w-8 h-8 border-t border-l border-[#e5b769] rounded-tl-lg"></div>
                    <div class="absolute top-2 right-2 w-8 h-8 border-t border-r border-[#e5b769] rounded-tr-lg"></div>
                    <div class="absolute bottom-2 left-2 w-8 h-8 border-b border-l border-[#e5b769] rounded-bl-lg">
                    </div>
                    <div class="absolute bottom-2 right-2 w-8 h-8 border-b border-r border-[#e5b769] rounded-br-lg">
                    </div>
                </div>

                
                <div
                    class="relative mx-auto bg-[#252117] rounded-lg border-2 border-[#514b3c] shadow-lg overflow-hidden w-64 max-w-full">
                    
                    <div
                        class="bg-gradient-to-r from-[#383428] via-[#2d281e] to-[#383428] h-6 flex items-center justify-center border-b border-[#514b3c]">
                        <div
                            class="absolute left-0 right-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-40">
                        </div>
                    </div>

                    
                    <div class="bg-gradient-to-br from-[#302a1e] to-[#1f1c18] p-2.5 shadow-inner relative">
                        
                        <div class="absolute inset-0 opacity-60 flex items-center justify-center overflow-hidden">
                            <img src="<?php echo e(asset('assets/bowler.png')); ?>" alt="Котелок"
                                class="w-[115%] h-[115%] object-cover mix-blend-overlay transform -translate-y-1.5">
                        </div>

                        
                        <div
                            class="absolute inset-0 bg-gradient-to-b from-[#30281c] via-transparent to-[#30281c] opacity-20">
                        </div>

                        
                        <div class="text-center mb-2 relative z-10">
                            <h3 class="text-xs text-[#e5b769] font-medium">Кулинарный Котелок</h3>
                        </div>

                        
                        <div class="grid grid-cols-3 grid-rows-2 gap-2 relative z-10">
                            
                            <div
                                class="aspect-square bg-[#1a1713] bg-opacity-80 backdrop-blur-sm rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors cursor-pointer flex items-center justify-center group relative">
                                <span
                                    class="text-[#9a9483] text-base group-hover:text-[#e5b769] transition-colors">+</span>
                                <div
                                    class="absolute inset-1 bg-[#e5b76910] opacity-0 group-hover:opacity-100 rounded transition-opacity">
                                </div>
                            </div>
                            <div
                                class="aspect-square bg-[#1a1713] bg-opacity-80 backdrop-blur-sm rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors cursor-pointer flex items-center justify-center group relative">
                                <span
                                    class="text-[#9a9483] text-base group-hover:text-[#e5b769] transition-colors">+</span>
                                <div
                                    class="absolute inset-1 bg-[#e5b76910] opacity-0 group-hover:opacity-100 rounded transition-opacity">
                                </div>
                            </div>
                            <div
                                class="aspect-square bg-[#1a1713] bg-opacity-80 backdrop-blur-sm rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors cursor-pointer flex items-center justify-center group relative">
                                <span
                                    class="text-[#9a9483] text-base group-hover:text-[#e5b769] transition-colors">+</span>
                                <div
                                    class="absolute inset-1 bg-[#e5b76910] opacity-0 group-hover:opacity-100 rounded transition-opacity">
                                </div>
                            </div>
                            <div
                                class="aspect-square bg-[#1a1713] bg-opacity-80 backdrop-blur-sm rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors cursor-pointer flex items-center justify-center group relative">
                                <span
                                    class="text-[#9a9483] text-base group-hover:text-[#e5b769] transition-colors">+</span>
                                <div
                                    class="absolute inset-1 bg-[#e5b76910] opacity-0 group-hover:opacity-100 rounded transition-opacity">
                                </div>
                            </div>
                            <div
                                class="aspect-square bg-[#1a1713] bg-opacity-80 backdrop-blur-sm rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors cursor-pointer flex items-center justify-center group relative">
                                <span
                                    class="text-[#9a9483] text-base group-hover:text-[#e5b769] transition-colors">+</span>
                                <div
                                    class="absolute inset-1 bg-[#e5b76910] opacity-0 group-hover:opacity-100 rounded transition-opacity">
                                </div>
                            </div>
                            <div
                                class="aspect-square bg-[#1a1713] bg-opacity-80 backdrop-blur-sm rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors cursor-pointer flex items-center justify-center group relative">
                                <span
                                    class="text-[#9a9483] text-base group-hover:text-[#e5b769] transition-colors">+</span>
                                <div
                                    class="absolute inset-1 bg-[#e5b76910] opacity-0 group-hover:opacity-100 rounded transition-opacity">
                                </div>
                            </div>
                        </div>

                        
                        <div class="absolute bottom-2 left-1/4 right-1/4 flex justify-around">
                            <div class="w-1 h-1 bg-[#e5b769] rounded-full opacity-30 animate-bubble"></div>
                            <div
                                class="w-1.5 h-1.5 bg-[#e5b769] rounded-full opacity-20 animate-bubble animation-delay-300">
                            </div>
                            <div
                                class="w-1 h-1 bg-[#e5b769] rounded-full opacity-30 animate-bubble animation-delay-700">
                            </div>
                        </div>

                        
                        <div
                            class="absolute -bottom-1 -left-1 h-6 w-6 border-b border-l border-[#a6925e20] rounded-bl-lg">
                        </div>
                        <div
                            class="absolute -bottom-1 -right-1 h-6 w-6 border-b border-r border-[#a6925e20] rounded-br-lg">
                        </div>
                    </div>

                    
                    <div
                        class="bg-gradient-to-r from-[#383428] via-[#2d281e] to-[#383428] h-6 flex items-center justify-center border-t border-[#514b3c]">
                        <div class="flex space-x-8">
                            <div class="w-5 h-1 bg-[#a6925e] rounded-full opacity-40"></div>
                            <div class="w-5 h-1 bg-[#a6925e] rounded-full opacity-40"></div>
                        </div>
                        <div
                            class="absolute left-0 right-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-40">
                        </div>
                    </div>
                </div>

                
                <div class="relative h-4 w-20 mx-auto mt-1">
                    <div class="absolute inset-0 flex justify-center space-x-4">
                        <div class="w-2 h-2 rounded-full bg-[#e5b769] opacity-70 animate-pulse"></div>
                        <div class="w-2 h-2 rounded-full bg-[#e5b769] opacity-50 animate-pulse animation-delay-300">
                        </div>
                        <div class="w-2 h-2 rounded-full bg-[#e5b769] opacity-70 animate-pulse animation-delay-500">
                        </div>
                    </div>
                </div>

                
                <div class="mt-2 text-center relative">
                    <button
                        class="bg-gradient-to-r from-[#a6925e] to-[#c4a76d] text-[#2f2d2b] py-2 px-4 rounded-md font-medium text-sm shadow-md hover:from-[#c4a76d] hover:to-[#d4b781] transform hover:scale-105 transition-all duration-200 focus:outline-none relative overflow-hidden group">
                        <span class="relative z-10">Приготовить</span>
                        <span
                            class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-10 transform -translate-x-full group-hover:translate-x-full transition-all duration-700"></span>
                    </button>

                    
                    <div class="hidden mt-2 w-full bg-[#2a261f] rounded-full h-2.5 overflow-hidden">
                        <div class="h-full bg-gradient-to-r from-[#e5b769] to-[#c4a76d] rounded-full animate-pulse"
                            style="width: 75%"></div>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="mt-4">
            <h2 class="text-sm font-medium text-[#e5b769] mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                </svg>
                Доступные ингредиенты
            </h2>

            
            <div class="grid grid-cols-4 gap-2">
                <?php $__currentLoopData = $ingredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div
                        class="bg-gradient-to-b from-[#332e23] to-[#262214] rounded p-1 border border-[#514b3c] hover:border-[#a6925e] transition-all cursor-pointer relative group">
                        
                        <div class="w-full h-12 flex items-center justify-center">
                            <img src="<?php echo e(asset($ingredient['image'])); ?>" alt="<?php echo e($ingredient['name']); ?>"
                                class="max-h-full object-contain">
                        </div>

                        
                        <div class="text-center mt-1">
                            <span class="text-[10px] text-[#d3c6a6] truncate block"><?php echo e($ingredient['name']); ?></span>
                            <span class="text-[10px] text-[#a6925e]">x<?php echo e($ingredient['quantity']); ?></span>
                        </div>

                        
                        <div
                            class="absolute z-10 bottom-full left-1/2 transform -translate-x-1/2 mb-1 w-32 bg-[#1f1c18] border border-[#514b3c] rounded p-2 text-[10px] text-[#d3c6a6] opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none shadow-lg">
                            <div class="font-medium text-[#e5b769] mb-1"><?php echo e($ingredient['name']); ?></div>
                            <p><?php echo e($ingredient['description']); ?></p>
                            <div
                                class="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-2 h-2 bg-[#1f1c18] border-r border-b border-[#514b3c] rotate-45">
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        
        <div class="mt-4 bg-[#252117] rounded-md p-2 border border-[#514b3c]">
            <h3 class="text-xs font-medium text-[#e5b769] mb-1 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Советы по кулинарии
            </h3>
            <p class="text-[10px] text-[#9a9483]">
                Сочетайте различные ингредиенты для создания блюд. Например, грибы + морковь + вода дадут суп, а ананас
                + нож - нарезанный ананас.
            </p>
        </div>

        
        <?php if (isset($component)) { $__componentOriginalb7d8c6ec62db5962f3caec28444ff656 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb7d8c6ec62db5962f3caec28444ff656 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.under-development','data' => ['title' => 'Кулинария','description' => 'Система кулинарии находится в активной разработке. Скоро вы сможете готовить различные блюда из собранных ингредиентов.','overlay' => true,'overlayOpacity' => '85']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('under-development'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Кулинария','description' => 'Система кулинарии находится в активной разработке. Скоро вы сможете готовить различные блюда из собранных ингредиентов.','overlay' => true,'overlayOpacity' => '85']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb7d8c6ec62db5962f3caec28444ff656)): ?>
<?php $attributes = $__attributesOriginalb7d8c6ec62db5962f3caec28444ff656; ?>
<?php unset($__attributesOriginalb7d8c6ec62db5962f3caec28444ff656); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb7d8c6ec62db5962f3caec28444ff656)): ?>
<?php $component = $__componentOriginalb7d8c6ec62db5962f3caec28444ff656; ?>
<?php unset($__componentOriginalb7d8c6ec62db5962f3caec28444ff656); ?>
<?php endif; ?>
    </div>

    <div class="container mx-auto text-center px-2 py-2 flex justify-center space-x-2">
        
        <a href="<?php echo e(route('inventory.index')); ?>"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Рюкзак
        </a>
        
        <a href="<?php echo e(route('user.profile')); ?>"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Персонаж
        </a>
        
        <a href="#"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Гильдия
        </a>
    </div>

    
    <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>

    
    <script>

    </script>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/professions/cooking.blade.php ENDPATH**/ ?>