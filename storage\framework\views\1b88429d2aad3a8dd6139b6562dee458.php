<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Редактирование зелья: <?php echo e($potion->name); ?> - Админ-панель</title>
    
    
    
    
    
    
    

    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    
    
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    <main class="flex-grow container mx-auto px-4 py-6">
        
        <div
            class="container max-w-6xl mx-auto px-4 py-4 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg shadow-lg mb-6">
            <div class="flex justify-between items-center mb-4">
                
                <a href="<?php echo e(route('admin.potions.index')); ?>"
                    class="w-10 h-10 bg-[#a6925e] flex items-center justify-center rounded-lg hover:bg-[#e5b769] transition duration-300">
                    <span class="text-xl">←</span>
                </a>

                
                <h1 class="text-2xl font-bold text-[#e5b769] text-center">Редактирование зелья: <?php echo e($potion->name); ?></h1>

                
                <a href="<?php echo e(route('admin.potions.show', $potion->id)); ?>"
                    class="px-4 py-2 bg-[#4a452c] text-[#e5b769] rounded hover:bg-[#5a5a4d] transition duration-300">
                    Просмотр
                </a>
            </div>

            
            <?php if($errors->any()): ?>
                <div class="bg-[#38352c] text-[#ff6b6b] p-4 rounded-lg border border-[#a6925e] mb-4">
                    <ul class="list-disc pl-5">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>

        
        <div class="container max-w-6xl mx-auto mb-6">
            <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] rounded-lg p-6 border-2 border-[#a6925e]">
                <h2 class="text-xl font-bold text-[#e5b769] mb-6 text-center">Информация о зелье</h2>

                <form action="<?php echo e(route('admin.potions.update', $potion->id)); ?>" method="POST"
                    enctype="multipart/form-data" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    
                    <div class="space-y-4">
                        
                        <div>
                            <label for="name" class="block text-sm text-[#d9d3b8] mb-2">Название зелья <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <input type="text" name="name" id="name" value="<?php echo e(old('name', $potion->name)); ?>" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                        </div>

                        
                        <div>
                            <label for="description" class="block text-sm text-[#d9d3b8] mb-2">Описание <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <textarea name="description" id="description" rows="5" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]"><?php echo e(old('description', $potion->description)); ?></textarea>
                        </div>

                        
                        <div>
                            <label for="effect" class="block text-sm text-[#d9d3b8] mb-2">Тип эффекта <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <select name="effect" id="effect" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <?php $__currentLoopData = $potionEffects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>" <?php echo e(old('effect', $potion->effect) == $key ? 'selected' : ''); ?>>
                                        <?php echo e($value); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        
                        <div>
                            <label for="effect_value" class="block text-sm text-[#d9d3b8] mb-2">Значение эффекта <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <input type="number" name="effect_value" id="effect_value"
                                value="<?php echo e(old('effect_value', $potion->effect_value)); ?>" step="0.1" min="0" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                            <p class="text-xs text-[#d9d3b8] mt-1">Базовое значение эффекта.</p>
                        </div>

                        
                        <div>
                            <label for="effect_duration" class="block text-sm text-[#d9d3b8] mb-2">Длительность эффекта
                                (сек) <span class="text-[#ff6b6b]">*</span></label>
                            <input type="number" name="effect_duration" id="effect_duration"
                                value="<?php echo e(old('effect_duration', $potion->effect_duration)); ?>" min="0" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                            <p class="text-xs text-[#d9d3b8] mt-1">0 для мгновенных эффектов.</p>
                        </div>
                    </div>

                    
                    <div class="space-y-4">
                        
                        <div>
                            <label for="level" class="block text-sm text-[#d9d3b8] mb-2">Уровень зелья <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <select name="level" id="level" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <option value="">-- Выберите уровень --</option>
                                <?php $__currentLoopData = $potionLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $levelKey => $levelName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($levelKey); ?>" <?php echo e(old('level', $potion->level) == $levelKey ? 'selected' : ''); ?>>
                                        <?php echo e($levelName); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <p class="text-xs text-[#d9d3b8] mt-1">Выберите уровень зелья из доступных вариантов</p>
                        </div>

                        
                        <div>
                            <label for="quality" class="block text-sm text-[#d9d3b8] mb-2">Качество зелья <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <select name="quality" id="quality" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <?php $__currentLoopData = $potionQualities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quality): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($quality); ?>" <?php echo e(old('quality', $potion->quality) == $quality ? 'selected' : ''); ?>>
                                        <?php echo e($quality); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        
                        <div>
                            <label for="uses_left" class="block text-sm text-[#d9d3b8] mb-2">Количество использований
                                <span class="text-[#ff6b6b]">*</span></label>
                            <input type="number" name="uses_left" id="uses_left"
                                value="<?php echo e(old('uses_left', $potion->uses_left)); ?>" min="0" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                            <p class="text-xs text-[#d9d3b8] mt-1">Текущее количество использований зелья</p>
                        </div>

                        
                        <div>
                            <label for="max_uses" class="block text-sm text-[#d9d3b8] mb-2">Максимальное количество
                                использований <span class="text-[#ff6b6b]">*</span></label>
                            <input type="number" name="max_uses" id="max_uses"
                                value="<?php echo e(old('max_uses', $potion->max_uses ?? $potion->uses_left)); ?>" min="1" required
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                            <p class="text-xs text-[#d9d3b8] mt-1">Максимальное количество использований зелья (для
                                отображения прогресса)</p>
                        </div>

                        
                        <div>
                            <label for="recipe_id" class="block text-sm text-[#d9d3b8] mb-2">Рецепт (если есть)</label>
                            <select name="recipe_id" id="recipe_id"
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <option value="">-- Без рецепта --</option>
                                <?php $__currentLoopData = $recipes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($id); ?>" <?php echo e(old('recipe_id', $potion->recipe_id) == $id ? 'selected' : ''); ?>>
                                        <?php echo e($name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        
                        <div>
                            <label class="block text-sm text-[#d9d3b8] mb-2">Текущая иконка</label>
                            <div class="mb-2">
                                <?php if($potion->icon): ?>
                                    <img src="<?php echo e(asset('assets/' . $potion->icon)); ?>" alt="<?php echo e($potion->name); ?>"
                                        class="w-16 h-16 object-contain inline-block align-middle border border-[#8c784e] rounded p-1 bg-[#2a2721]">
                                    <span class="text-xs text-[#d9d3b8] ml-2 align-middle"><?php echo e($potion->icon); ?></span>
                                <?php else: ?>
                                    <span class="text-4xl inline-block align-middle">🧪</span>
                                    <span class="text-xs text-[#d9d3b8] ml-2 align-middle">(Иконка не задана)</span>
                                <?php endif; ?>
                            </div>

                            <label for="icon" class="block text-sm text-[#d9d3b8] mb-2">Иконка зелья <span
                                    class="text-[#ff6b6b]">*</span></label>
                            <input type="text" name="icon" id="icon" value="<?php echo e(old('icon', $potion->icon)); ?>"
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]"
                                placeholder="potions/smallBottleHP.png">
                            <p class="text-xs text-[#d9d3b8] mt-1">Укажите только имя файла из директории potions,
                                например: potions/smallBottleHP.png</p>

                            <div class="mt-2">
                                <p class="text-xs text-[#d9d3b8] mb-1">Доступные иконки зелий:</p>
                                <div class="grid grid-cols-4 gap-2 mt-1">
                                    <div class="text-center">
                                        <img src="<?php echo e(asset('assets/potions/smallBottleHP.png')); ?>"
                                            alt="Малый флакон здоровья" class="w-8 h-8 mx-auto">
                                        <p class="text-xs mt-1">smallBottleHP.png</p>
                                    </div>
                                    <div class="text-center">
                                        <img src="<?php echo e(asset('assets/potions/mediumBottleHP.png')); ?>"
                                            alt="Средний флакон здоровья" class="w-8 h-8 mx-auto">
                                        <p class="text-xs mt-1">mediumBottleHP.png</p>
                                    </div>
                                    <div class="text-center">
                                        <img src="<?php echo e(asset('assets/potions/largeBottleHP.png')); ?>"
                                            alt="Большой флакон здоровья" class="w-8 h-8 mx-auto">
                                        <p class="text-xs mt-1">largeBottleHP.png</p>
                                    </div>
                                    <div class="text-center">
                                        <img src="<?php echo e(asset('assets/potions/smallBottleMP.png')); ?>"
                                            alt="Малый флакон маны" class="w-8 h-8 mx-auto">
                                        <p class="text-xs mt-1">smallBottleMP.png</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="color" id="color"
                            value="<?php echo e(old('color', $potion->color ?? '#6c757d')); ?>">

                        
                        <div>
                            <label for="user_id" class="block text-sm text-[#d9d3b8] mb-2">Владелец зелья</label>
                            <select name="user_id" id="user_id"
                                class="w-full px-3 py-2 bg-[#2a2721] border border-[#8c784e] rounded text-[#f5f5f5] focus:outline-none focus:border-[#e5b769]">
                                <option value="">-- Нет владельца --</option>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($id); ?>" <?php echo e(old('user_id', $potion->user_id) == $id ? 'selected' : ''); ?>>
                                        <?php echo e($name); ?> (ID: <?php echo e($id); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        
                        <div class="mt-4">
                            <label class="flex items-center">
                                <input type="checkbox" name="is_template" id="is_template" value="1" <?php echo e(old('is_template', $potion->is_template) ? 'checked' : ''); ?>

                                    class="w-5 h-5 bg-[#2a2721] border border-[#8c784e] rounded text-[#a6925e] focus:outline-none focus:border-[#e5b769]">
                                <span class="ml-2 text-sm text-[#d9d3b8]">Это шаблон зелья</span>
                            </label>
                            <p class="text-xs text-[#d9d3b8] mt-1">Шаблоны используются для создания экземпляров зелий
                                для игроков</p>
                        </div>

                        
                        <?php if($potion->game_item_id): ?>
                            <div class="mt-4 p-3 bg-[#38352c] border border-[#8c784e] rounded">
                                <h3 class="text-sm font-bold text-[#e5b769] mb-2">Связанный игровой предмет</h3>
                                <p class="text-xs text-[#d9d3b8]">ID: <?php echo e($potion->game_item_id); ?></p>
                                <?php if($potion->gameItem): ?>
                                    <p class="text-xs text-[#d9d3b8]">Владелец:
                                        <?php echo e($potion->gameItem->owner ? $potion->gameItem->owner->name : 'Нет'); ?></p>
                                    <p class="text-xs text-[#d9d3b8]">Расположение: <?php echo e($potion->gameItem->location); ?></p>
                                <?php else: ?>
                                    <p class="text-xs text-[#ff6b6b]">Игровой предмет не найден</p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                    </div>

                    
                    <div class="md:col-span-2 flex justify-between mt-6">
                        <a href="<?php echo e(route('admin.potions.index')); ?>"
                            class="px-4 py-2 bg-[#4a4a3d] text-[#e5b769] rounded font-semibold hover:bg-[#5a5a4d] transition duration-300">
                            Отмена
                        </a>
                        <button type="submit"
                            class="px-6 py-2 bg-[#a6925e] text-[#2f2d2b] rounded-lg font-semibold hover:bg-[#e5b769] transition duration-300">
                            Обновить зелье
                        </button>
                    </div>
                </form>
            </div>
        </div>

        
        <footer
            class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] text-[#d9d3b8] py-3 mt-6 border-t-2 border-[#a6925e] rounded-lg">
            <div class="container max-w-6xl mx-auto px-6 text-center">
                <div class="flex justify-between items-center flex-wrap">
                    <div>
                        <p class="text-sm font-semibold">
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-[#e5b769] hover:underline">
                                Вернуться в админ-панель
                            </a>
                        </p>
                    </div>
                    <div>
                        <p class="text-sm font-semibold">
                            Время: <span class="text-[#e5b769]"><?php echo e(date('H:i')); ?></span>
                        </p>
                    </div>
                </div>
                <p class="text-sm font-semibold mt-3">
                    © <?php echo e(date('Y')); ?> Echoes of Eternity. Все права защищены.
                </p>
            </div>
        </footer>
    </main>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/potions/edit.blade.php ENDPATH**/ ?>