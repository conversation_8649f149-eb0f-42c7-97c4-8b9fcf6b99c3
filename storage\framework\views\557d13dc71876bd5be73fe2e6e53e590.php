<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['village', 'userRace', 'villageName', 'routePrefix' => 'battle.outposts.elven_haven', 'locationId' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['village', 'userRace', 'villageName', 'routePrefix' => 'battle.outposts.elven_haven', 'locationId' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php if($village): ?>
    <form action="<?php echo e(route($routePrefix . '.village.attack', ['id' => $locationId, 'villageId' => $village->id])); ?>"
        method="POST" class="flex flex-col items-center">
        <?php echo csrf_field(); ?>

        
        <?php if($village->race !== $userRace): ?>
            <button type="submit" class="flex flex-col items-center">
                <img src="<?php echo e($village->race === 'solarius' ? asset('assets/postSolarius.png') : asset('assets/postLunar.png')); ?>"
                    alt="<?php echo e($villageName); ?>" class="w-8 h-8 mb-1">
            </button>
        <?php else: ?>
            <img src="<?php echo e($village->race === 'solarius' ? asset('assets/postSolarius.png') : asset('assets/postLunar.png')); ?>"
                alt="<?php echo e($villageName); ?>" class="w-8 h-8 mb-1 opacity-50">
        <?php endif; ?>

        
        <div class="text-center text-[10px] text-[#e5b769] font-semibold mb-0.5">
            <?php echo e($village->name ?? $villageName); ?>

        </div>

        
        <div class="w-24 bg-[#2a2721] border border-[#514b3c] rounded-md relative overflow-hidden">
            <?php
                $healthPercent = ($village->health / $village->max_health) * 100;
                $healthColor = $healthPercent > 70 ? 'bg-gradient-to-r from-[#4a5c2f] to-[#5a6d3f]' :
                    ($healthPercent > 30 ? 'bg-gradient-to-r from-[#8c7a45] to-[#9c8a55]' :
                        'bg-gradient-to-r from-[#8a4a4a] to-[#9a5a5a]');
                $textColor = $healthPercent > 70 ? 'text-[#a3e635]' :
                    ($healthPercent > 30 ? 'text-[#e5b769]' : 'text-[#ff6b6b]');
                $formattedHealth = number_format($village->health, 0, ',', ' ');
                $formattedMaxHealth = number_format($village->max_health, 0, ',', ' ');
            ?>
            <div class="<?php echo e($healthColor); ?> h-5" style="width: <?php echo e($healthPercent); ?>%;"></div>
            <div class="absolute inset-0 flex justify-center items-center text-[11px] font-bold <?php echo e($textColor); ?>">
                <?php echo e($formattedHealth); ?>/<?php echo e($formattedMaxHealth); ?>

            </div>
        </div>
    </form>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/outposts/village-block.blade.php ENDPATH**/ ?>