<!DOCTYPE html>
<html lang="ru">
{{-- Указываем язык документа --}}

<head>
    <meta charset="UTF-8">
    {{-- Установка кодировки UTF-8 --}}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- Настройка viewport для адаптивности --}}
    <title>Добавить рецепт в магазин - Админ панель</title>
    {{-- Заголовок страницы --}}
    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/admin/shop-recipe-preview.js'])
    {{-- Подключение CSS и JS с помощью Vite --}}
</head>

{{-- Тело документа с фоном и шрифтом из админ-панели --}}

<body class="bg-[#1a1814] text-[#d4cbb0] font-serif">
    {{-- Основной контейнер страницы --}}
    <div class="min-h-screen flex flex-col">
        {{-- Основное содержимое --}}
        <main class="flex-grow container mx-auto px-4 py-6">
            {{-- Верхний блок с информацией и навигацией --}}
            <div
                class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] border-2 border-[#3b3629] rounded-lg shadow-lg mb-8 p-5">
                {{-- Шапка с заголовком и кнопкой назад --}}
                <div class="flex justify-between items-center mb-5">
                    {{-- Кнопка Назад к списку --}}
                    <a href="{{ route('admin.shop.index', ['tab' => 'recipes']) }}" title="Назад к списку рецептов магазина"
                        class="p-2 bg-[#3b3629] hover:bg-[#c1a96e] rounded-lg transition duration-300 border border-[#6e3f35]">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#c1a96e]" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M11 15l-3-3m0 0l3-3m-3 3h8M3 12a9 9 0 1118 0 9 9 0 01-18 0z" />
                        </svg>
                    </a>

                    {{-- Заголовок --}}
                    <h1 class="text-3xl font-bold text-[#e4d7b0]">Добавить Рецепт в Магазин</h1>

                    {{-- Пустое место справа для симметрии --}}
                    <div class="w-10"></div>
                </div>

                {{-- Отображение ошибок валидации --}}
                @if ($errors->any())
                    <div class="bg-gradient-to-r from-[#59372d] to-[#3c221b] border border-[#6e3f35] text-[#f8eac2] px-4 py-3 rounded relative mb-4" role="alert">
                        <strong class="font-bold">Ошибка валидации!</strong>
                        <ul class="mt-2">
                            @foreach ($errors->all() as $error)
                                <li class="text-sm">• {{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>

            {{-- Форма добавления рецепта --}}
            <form action="{{ route('admin.shop.store-recipe') }}" method="POST"
                class="bg-[#1a1814] border border-[#3b3629] rounded-lg shadow-md p-6">
                @csrf
                {{-- CSRF токен для защиты --}}

                {{-- Выбор рецепта --}}
                <div class="mb-4">
                    <label for="potion_recipe_id" class="block text-sm font-medium text-[#e4d7b0] mb-1">Рецепт зелья</label>
                    {{-- Метка для поля выбора рецепта --}}
                    <select id="potion_recipe_id" name="potion_recipe_id" required
                        class="w-full px-3 py-2 bg-[#2a2722] border border-[#3b3629] rounded-md shadow-sm text-[#d4cbb0] focus:outline-none focus:ring-[#c1a96e] focus:border-[#c1a96e]">
                        {{-- Выпадающий список для выбора рецепта --}}
                        <option value="" disabled selected>-- Выберите рецепт --</option>
                        {{-- Опция-заглушка --}}
                        @forelse ($recipes as $recipe)
                            {{-- Итерация по доступным для добавления рецептам --}}
                            <option value="{{ $recipe->id }}"
                                    data-quality="{{ $recipe->quality }}"
                                    data-level="{{ $recipe->level }}"
                                    data-icon="{{ $recipe->icon }}"
                                    {{ old('potion_recipe_id') == $recipe->id ? 'selected' : '' }}>
                                {{-- Значение - ID рецепта, отображаемый текст - Название (Качество, Уровень) --}}
                                {{ $recipe->name }} ({{ $recipe->quality ?? 'N/A' }}, {{ $recipe->level ?? 'N/A' }})
                            </option>
                        @empty
                            <option value="" disabled>Нет доступных рецептов для добавления</option>
                            {{-- Сообщение, если нет рецептов для добавления --}}
                        @endforelse
                        {{-- Конец цикла foreach --}}
                    </select>
                </div>

                {{-- Предварительный просмотр выбранного рецепта --}}
                <div id="recipe-preview" class="hidden mb-6 bg-[#2a2722] border border-[#3b3629] rounded-lg p-4">
                    <h3 class="text-[#e4d7b0] font-semibold mb-3">Информация о рецепте:</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-[#998d66]">Качество:</span>
                            <span id="preview-quality" class="text-[#d4cbb0] ml-2"></span>
                        </div>
                        <div>
                            <span class="text-[#998d66]">Уровень:</span>
                            <span id="preview-level" class="text-[#d4cbb0] ml-2"></span>
                        </div>
                        <div>
                            <span class="text-[#998d66]">Иконка:</span>
                            <span id="preview-icon" class="text-[#c1a96e] ml-2 font-medium"></span>
                        </div>
                    </div>
                    <div class="mt-3 p-3 bg-[#1a1814] rounded border border-[#3b3629]">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[#c1a96e] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="text-[#998d66] text-xs">Рецепт будет доступен для покупки игроками в магазине</span>
                        </div>
                    </div>
                </div>

                {{-- Цена --}}
                <div class="mb-6">
                    <label class="block text-sm font-medium text-[#e4d7b0] mb-2">Цена в магазине</label>
                    {{-- Метка для группы полей цены --}}
                    <div class="grid grid-cols-3 gap-4">
                        {{-- Поле для ввода цены в золоте --}}
                        <div>
                            <label for="price_gold" class="block text-xs font-medium text-[#c1a96e] mb-1">Золото</label>
                            <input type="number" id="price_gold" name="price_gold" min="0"
                                value="{{ old('price_gold', 0) }}"
                                class="w-full px-3 py-2 bg-[#2a2722] border border-[#3b3629] rounded-md shadow-sm text-[#c1a96e] focus:outline-none focus:ring-[#c1a96e] focus:border-[#c1a96e] placeholder:text-[#998d66]"
                                placeholder="0">
                        </div>
                        {{-- Поле для ввода цены в серебре --}}
                        <div>
                            <label for="price_silver"
                                class="block text-xs font-medium text-[#998d66] mb-1">Серебро</label>
                            <input type="number" id="price_silver" name="price_silver" min="0" max="99"
                                value="{{ old('price_silver', 0) }}"
                                class="w-full px-3 py-2 bg-[#2a2722] border border-[#3b3629] rounded-md shadow-sm text-[#998d66] focus:outline-none focus:ring-[#c1a96e] focus:border-[#c1a96e] placeholder:text-[#998d66]"
                                placeholder="0">
                        </div>
                        {{-- Поле для ввода цены в бронзе --}}
                        <div>
                            <label for="price_bronze"
                                class="block text-xs font-medium text-[#6e3f35] mb-1">Бронза</label>
                            <input type="number" id="price_bronze" name="price_bronze" min="0" max="99"
                                value="{{ old('price_bronze', 0) }}"
                                class="w-full px-3 py-2 bg-[#2a2722] border border-[#3b3629] rounded-md shadow-sm text-[#6e3f35] focus:outline-none focus:ring-[#c1a96e] focus:border-[#c1a96e] placeholder:text-[#998d66]"
                                placeholder="0">
                        </div>
                    </div>
                </div>

                {{-- Категория магазина --}}
                <div class="mb-6">
                    <label for="shop_category" class="block text-sm font-medium text-[#e4d7b0] mb-1">Категория магазина (опционально)</label>
                    <input type="text" id="shop_category" name="shop_category" maxlength="255"
                        value="{{ old('shop_category') }}"
                        class="w-full px-3 py-2 bg-[#2a2722] border border-[#3b3629] rounded-md shadow-sm text-[#d4cbb0] focus:outline-none focus:ring-[#c1a96e] focus:border-[#c1a96e] placeholder:text-[#998d66]"
                        placeholder="Например: Лечебные зелья, Боевые зелья">
                </div>

                {{-- Доступность --}}
                <div class="mb-6">
                    <label for="is_available" class="flex items-center">
                        <input type="checkbox" id="is_available" name="is_available" value="1" {{ old('is_available', true) ? 'checked' : '' }}
                            class="h-4 w-4 text-[#c1a96e] bg-[#2a2722] border-[#3b3629] rounded focus:ring-[#c1a96e]">
                        {{-- Чекбокс для установки доступности рецепта --}}
                        <span class="ml-2 text-sm text-[#d4cbb0]">Доступен для покупки</span>
                        {{-- Метка для чекбокса --}}
                    </label>
                </div>

                {{-- Кнопки --}}
                <div class="flex justify-end space-x-4">
                    {{-- Кнопка отмены --}}
                    <a href="{{ route('admin.shop.index', ['tab' => 'recipes']) }}"
                        class="py-2 px-4 bg-gradient-to-b from-[#59372d] to-[#3c221b] text-[#f8eac2] rounded-md hover:from-[#6e3f35] hover:to-[#59372d] border border-[#6e3f35] transition duration-300">
                        Отмена
                    </a>
                    {{-- Кнопка добавления --}}
                    <button type="submit"
                        class="py-2 px-4 bg-gradient-to-b from-[#2f473c] to-[#1e2e27] text-[#f8eac2] rounded-md hover:from-[#3b5a4a] hover:to-[#2f473c] border border-[#2f473c] font-semibold transition duration-300">
                        Добавить рецепт
                    </button>
                </div>
            </form>

        </main>

        {{-- Футер админ-панели --}}
        <footer class="bg-[#1a1814] text-[#998d66] py-4 mt-auto border-t border-[#3b3629]">
            <div class="container max-w-7xl mx-auto px-6 text-center text-sm">
                © {{ date('Y') }} Echoes of Eternity | Административная Панель
            </div>
        </footer>
    </div>



</body>

</html>
