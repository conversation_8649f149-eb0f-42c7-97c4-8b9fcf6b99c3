<!DOCTYPE html>
<html lang="ru">
<?php
    use Illuminate\Support\Facades\Auth;
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Городская площадь - <?php echo e(Auth::check() ? Auth::user()->name : 'Гость'); ?></title>

    
    <?php echo app('Illuminate\Foundation\Vite')([
        'resources/css/app.css',
        'resources/js/app.js',
        'resources/css/square-mobile.css',
        'resources/css/square-standardized.css',
        'resources/js/square-interactions.js',
        'resources/css/components/guild-invitation.css',
        'resources/css/components/donation-button.css'
    ]); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">

    
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasUnreadMessages' => $hasUnreadMessages ?? false,'unreadMessagesCount' => $unreadMessagesCount ?? 0,'hasBrokenItems' => $hasBrokenItems ?? false,'brokenItemsCount' => $brokenItemsCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasUnreadMessages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasUnreadMessages ?? false),'unreadMessagesCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unreadMessagesCount ?? 0),'hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBrokenItems ?? false),'brokenItemsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($brokenItemsCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile,'experienceProgress' => $experienceProgress ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile),'experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>

        
        <?php if(Auth::check()): ?>
            <?php
                $guildInvitation = Auth::user()->getLatestGuildInvitation();
            ?>
            <?php if (isset($component)) { $__componentOriginalf073577b00d5eaef97c81e52a3000637 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf073577b00d5eaef97c81e52a3000637 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.guild-invitation','data' => ['guildInvitation' => $guildInvitation]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.guild-invitation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['guildInvitation' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($guildInvitation)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf073577b00d5eaef97c81e52a3000637)): ?>
<?php $attributes = $__attributesOriginalf073577b00d5eaef97c81e52a3000637; ?>
<?php unset($__attributesOriginalf073577b00d5eaef97c81e52a3000637); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf073577b00d5eaef97c81e52a3000637)): ?>
<?php $component = $__componentOriginalf073577b00d5eaef97c81e52a3000637; ?>
<?php unset($__componentOriginalf073577b00d5eaef97c81e52a3000637); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <div class="w-full mx-auto">
            <?php if (isset($component)) { $__componentOriginal360d002b1b676b6f84d43220f22129e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal360d002b1b676b6f84d43220f22129e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumbs','data' => ['breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $attributes = $__attributesOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__attributesOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $component = $__componentOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__componentOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
            
            <div class="px-4 py-1">
                <hr class="border-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-70">
            </div>
            
            <p
                class="block text-center mx-auto text-white py-1 px-8 rounded-sm shadow-lg bg-gradient-to-b from-[#2E8B57]">
                Городская площадь
            </p>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal6fc2934a0cc4bc0e6b375c66c7191fe9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6fc2934a0cc4bc0e6b375c66c7191fe9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.content-block','data' => ['class' => 'px-2.5 py-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.content-block'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'px-2.5 py-4']); ?>
            
            <div class="flex items-center mb-4">
                <div class="text-xl">🗺️</div>
                <h2 class="text-[#e5b769] font-semibold text-lg ml-2">Доступные локации</h2>
                <div class="flex-1 h-px bg-gradient-to-r from-[#a6925e] to-transparent ml-3"></div>
            </div>

            
            <div class="space-y-4">
                
                <?php if (isset($component)) { $__componentOriginal36e02b2808d18ea8125e3c38be2c7717 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal36e02b2808d18ea8125e3c38be2c7717 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.location-card','data' => ['route' => 'market.index','title' => 'Рынок','description' => 'Торговая площадка, здесь можно приобрести необходимые товары или продать другим игрокам.','icon' => 'assets/market.png','iconEmoji' => '🏪','isActive' => true,'statusText' => 'Активно','statusColor' => 'green']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.location-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => 'market.index','title' => 'Рынок','description' => 'Торговая площадка, здесь можно приобрести необходимые товары или продать другим игрокам.','icon' => 'assets/market.png','iconEmoji' => '🏪','isActive' => true,'statusText' => 'Активно','statusColor' => 'green']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal36e02b2808d18ea8125e3c38be2c7717)): ?>
<?php $attributes = $__attributesOriginal36e02b2808d18ea8125e3c38be2c7717; ?>
<?php unset($__attributesOriginal36e02b2808d18ea8125e3c38be2c7717); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal36e02b2808d18ea8125e3c38be2c7717)): ?>
<?php $component = $__componentOriginal36e02b2808d18ea8125e3c38be2c7717; ?>
<?php unset($__componentOriginal36e02b2808d18ea8125e3c38be2c7717); ?>
<?php endif; ?>


                
                <?php if (isset($component)) { $__componentOriginal36e02b2808d18ea8125e3c38be2c7717 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal36e02b2808d18ea8125e3c38be2c7717 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.location-card','data' => ['route' => 'bank.index','title' => 'Банк','description' => 'Хранение предметов и перевод валюты другим игрокам','icon' => 'assets/bank.png','iconEmoji' => '🏦','isActive' => true,'statusText' => 'Активно','statusColor' => 'green']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.location-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => 'bank.index','title' => 'Банк','description' => 'Хранение предметов и перевод валюты другим игрокам','icon' => 'assets/bank.png','iconEmoji' => '🏦','isActive' => true,'statusText' => 'Активно','statusColor' => 'green']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal36e02b2808d18ea8125e3c38be2c7717)): ?>
<?php $attributes = $__attributesOriginal36e02b2808d18ea8125e3c38be2c7717; ?>
<?php unset($__attributesOriginal36e02b2808d18ea8125e3c38be2c7717); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal36e02b2808d18ea8125e3c38be2c7717)): ?>
<?php $component = $__componentOriginal36e02b2808d18ea8125e3c38be2c7717; ?>
<?php unset($__componentOriginal36e02b2808d18ea8125e3c38be2c7717); ?>
<?php endif; ?>
                
                <?php if (isset($component)) { $__componentOriginal36e02b2808d18ea8125e3c38be2c7717 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal36e02b2808d18ea8125e3c38be2c7717 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.location-card','data' => ['route' => '#','title' => 'Лавки Мастеров','description' => 'Торги за редкие и ценные предметы','icon' => 'assets/auction.png','iconEmoji' => '🔨','isActive' => false,'statusText' => 'В разработке','statusColor' => 'red']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.location-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => '#','title' => 'Лавки Мастеров','description' => 'Торги за редкие и ценные предметы','icon' => 'assets/auction.png','iconEmoji' => '🔨','isActive' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'statusText' => 'В разработке','statusColor' => 'red']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal36e02b2808d18ea8125e3c38be2c7717)): ?>
<?php $attributes = $__attributesOriginal36e02b2808d18ea8125e3c38be2c7717; ?>
<?php unset($__attributesOriginal36e02b2808d18ea8125e3c38be2c7717); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal36e02b2808d18ea8125e3c38be2c7717)): ?>
<?php $component = $__componentOriginal36e02b2808d18ea8125e3c38be2c7717; ?>
<?php unset($__componentOriginal36e02b2808d18ea8125e3c38be2c7717); ?>
<?php endif; ?>



                
                <?php if (isset($component)) { $__componentOriginal36e02b2808d18ea8125e3c38be2c7717 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal36e02b2808d18ea8125e3c38be2c7717 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.location-card','data' => ['route' => '#','title' => 'Доска объявлений','description' => 'Объявления и новости от игроков','icon' => 'assets/board.png','iconEmoji' => '📋','isActive' => false,'statusText' => 'В разработке','statusColor' => 'red']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.location-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => '#','title' => 'Доска объявлений','description' => 'Объявления и новости от игроков','icon' => 'assets/board.png','iconEmoji' => '📋','isActive' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'statusText' => 'В разработке','statusColor' => 'red']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal36e02b2808d18ea8125e3c38be2c7717)): ?>
<?php $attributes = $__attributesOriginal36e02b2808d18ea8125e3c38be2c7717; ?>
<?php unset($__attributesOriginal36e02b2808d18ea8125e3c38be2c7717); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal36e02b2808d18ea8125e3c38be2c7717)): ?>
<?php $component = $__componentOriginal36e02b2808d18ea8125e3c38be2c7717; ?>
<?php unset($__componentOriginal36e02b2808d18ea8125e3c38be2c7717); ?>
<?php endif; ?>
            </div>

            
            <?php if (isset($component)) { $__componentOriginal7fcae98b533e22a789f0f347e60fecdc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7fcae98b533e22a789f0f347e60fecdc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.events-section','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.events-section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7fcae98b533e22a789f0f347e60fecdc)): ?>
<?php $attributes = $__attributesOriginal7fcae98b533e22a789f0f347e60fecdc; ?>
<?php unset($__attributesOriginal7fcae98b533e22a789f0f347e60fecdc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7fcae98b533e22a789f0f347e60fecdc)): ?>
<?php $component = $__componentOriginal7fcae98b533e22a789f0f347e60fecdc; ?>
<?php unset($__componentOriginal7fcae98b533e22a789f0f347e60fecdc); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6fc2934a0cc4bc0e6b375c66c7191fe9)): ?>
<?php $attributes = $__attributesOriginal6fc2934a0cc4bc0e6b375c66c7191fe9; ?>
<?php unset($__attributesOriginal6fc2934a0cc4bc0e6b375c66c7191fe9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6fc2934a0cc4bc0e6b375c66c7191fe9)): ?>
<?php $component = $__componentOriginal6fc2934a0cc4bc0e6b375c66c7191fe9; ?>
<?php unset($__componentOriginal6fc2934a0cc4bc0e6b375c66c7191fe9); ?>
<?php endif; ?>

        
        <div class="mt-3">
            <?php if (isset($component)) { $__componentOriginalb5183a2c06ab1c06e76d28951184e93e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb5183a2c06ab1c06e76d28951184e93e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.donation-button','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.donation-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb5183a2c06ab1c06e76d28951184e93e)): ?>
<?php $attributes = $__attributesOriginalb5183a2c06ab1c06e76d28951184e93e; ?>
<?php unset($__attributesOriginalb5183a2c06ab1c06e76d28951184e93e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb5183a2c06ab1c06e76d28951184e93e)): ?>
<?php $component = $__componentOriginalb5183a2c06ab1c06e76d28951184e93e; ?>
<?php unset($__componentOriginalb5183a2c06ab1c06e76d28951184e93e); ?>
<?php endif; ?>
        </div>
    </div>

    
    <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>

</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/square/square.blade.php ENDPATH**/ ?>