
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title' => 'В разработке',
    'description' => 'Данный раздел находится в активной разработке и скоро будет доступен.',
    'showBackground' => true,
    'overlay' => false,
    'overlayOpacity' => '90'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title' => 'В разработке',
    'description' => 'Данный раздел находится в активной разработке и скоро будет доступен.',
    'showBackground' => true,
    'overlay' => false,
    'overlayOpacity' => '90'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php if($overlay): ?>
    
    <div class="absolute inset-0 bg-[#2f2d2b] bg-opacity-<?php echo e($overlayOpacity); ?> z-50 flex items-center justify-center">
        <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#a6925e] rounded-lg shadow-2xl p-8 max-w-md mx-4 text-center">
            
            <div class="mb-6">
                <div class="w-20 h-20 mx-auto bg-gradient-to-br from-[#a6925e] to-[#c4a76d] rounded-full flex items-center justify-center shadow-lg">
                    <svg class="w-10 h-10 text-[#2f2d2b]" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>

            
            <h2 class="text-2xl font-bold text-[#e5b769] mb-4"><?php echo e($title); ?></h2>
            
            
            <p class="text-[#d9d3b8] mb-6 leading-relaxed"><?php echo e($description); ?></p>
            
            
            <div class="mb-6">
                <div class="flex items-center justify-between text-sm text-[#a6925e] mb-2">
                    <span>Прогресс разработки</span>
                    <span><?php echo e(rand(15, 45)); ?>%</span>
                </div>
                <div class="w-full bg-[#3b3a33] rounded-full h-2">
                    <div class="bg-gradient-to-r from-[#a6925e] to-[#c4a76d] h-2 rounded-full animate-pulse" style="width: <?php echo e(rand(15, 45)); ?>%"></div>
                </div>
            </div>

            
            <button onclick="history.back()" 
                class="bg-gradient-to-r from-[#a6925e] to-[#c4a76d] text-[#2f2d2b] px-6 py-2 rounded-md font-medium hover:from-[#c4a76d] hover:to-[#d4b781] transition-all duration-300 transform hover:scale-105 shadow-lg">
                Вернуться назад
            </button>
        </div>
    </div>
<?php else: ?>
    
    <div class="min-h-screen bg-[#2f2d2b] text-[#f5f5f5] font-serif flex items-center justify-center p-4">
        <?php if($showBackground): ?>
            
            <div class="absolute inset-0 opacity-5">
                <div class="absolute top-10 left-10 w-32 h-32 border border-[#a6925e] rounded-full"></div>
                <div class="absolute top-20 right-20 w-24 h-24 border border-[#a6925e] rounded-full"></div>
                <div class="absolute bottom-20 left-20 w-40 h-40 border border-[#a6925e] rounded-full"></div>
                <div class="absolute bottom-10 right-10 w-28 h-28 border border-[#a6925e] rounded-full"></div>
            </div>
        <?php endif; ?>

        <div class="relative z-10 bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#a6925e] rounded-lg shadow-2xl p-8 max-w-lg mx-auto text-center">
            
            <div class="mb-8">
                <div class="w-24 h-24 mx-auto bg-gradient-to-br from-[#a6925e] to-[#c4a76d] rounded-full flex items-center justify-center shadow-lg animate-pulse">
                    <svg class="w-12 h-12 text-[#2f2d2b] animate-spin" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>

            
            <h1 class="text-3xl font-bold text-[#e5b769] mb-6"><?php echo e($title); ?></h1>
            
            
            <p class="text-[#d9d3b8] mb-8 leading-relaxed text-lg"><?php echo e($description); ?></p>
            
            
            <div class="bg-[#3b3a33] rounded-lg p-4 mb-8 border border-[#514b3c]">
                <h3 class="text-[#c4a76d] font-semibold mb-2">Что нас ждет:</h3>
                <ul class="text-[#a6925e] text-sm space-y-1">
                    <li>• Новые возможности и функции</li>
                    <li>• Улучшенный интерфейс</li>
                    <li>• Оптимизация производительности</li>
                </ul>
            </div>
            
            
            <div class="mb-8">
                <div class="flex items-center justify-between text-sm text-[#a6925e] mb-2">
                    <span>Прогресс разработки</span>
                    <span><?php echo e(rand(15, 45)); ?>%</span>
                </div>
                <div class="w-full bg-[#3b3a33] rounded-full h-3">
                    <div class="bg-gradient-to-r from-[#a6925e] to-[#c4a76d] h-3 rounded-full animate-pulse" style="width: <?php echo e(rand(15, 45)); ?>%"></div>
                </div>
            </div>

            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="history.back()" 
                    class="bg-gradient-to-r from-[#a6925e] to-[#c4a76d] text-[#2f2d2b] px-6 py-3 rounded-md font-medium hover:from-[#c4a76d] hover:to-[#d4b781] transition-all duration-300 transform hover:scale-105 shadow-lg">
                    Вернуться назад
                </button>
                <a href="<?php echo e(route('home')); ?>" 
                    class="bg-[#3b3a33] text-[#e5b769] px-6 py-3 rounded-md font-medium border border-[#514b3c] hover:border-[#a6925e] hover:bg-[#4a452c] transition-all duration-300 transform hover:scale-105">
                    На главную
                </a>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/under-development.blade.php ENDPATH**/ ?>