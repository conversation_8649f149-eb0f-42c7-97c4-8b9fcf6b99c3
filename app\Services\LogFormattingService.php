<?php

namespace App\Services;

use App\Models\User;
use App\Models\Mob;
use App\Models\Skill;
use App\Models\GameItem;
use App\Models\Resource;
use App\Models\AlchemyIngredient;
use Illuminate\Support\Facades\Log; // Добавляем для логирования ошибок

/**
 * Сервис для форматирования сообщений боевого лога в едином стиле.
 * Генерирует HTML-строки с использованием Tailwind CSS для отображения в интерфейсе.
 */
class LogFormattingService
{
    /**
     * Форматирует сообщение об атаке игрока по цели (мобу или другому игроку).
     *
     * @param mixed $attacker Атакующий игрок.
     * @param mixed $target Цель атаки (Mob или другой игрок).
     * @param int $damage Нанесенный урон.
     * @param bool $isCrit Был ли удар критическим.
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatPlayerAttack($attacker, $target, int $damage, bool $isCrit = false): string
    {
        // Определяем имя цели
        $targetName = $target instanceof Mob ? $target->name :
            ($target instanceof User ? $target->name :
                ($target instanceof \App\Models\Bot ? $target->name : 'неизвестная цель'));

        // Собираем HTML без отображения HP
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-green-600 mr-1'>Вы →</span>"; // "Вы →" зеленым
        $logHtml .= "<span class='" . ($isCrit ? 'text-orange-500 font-bold' : 'text-red-400 font-medium') . " mx-1'>" . round($damage) . "</span>"; // Урон красным (или оранжевым для крита)
        if ($isCrit) {
            $logHtml .= "<span class='text-orange-500 font-bold mr-1'>(крит!)</span>"; // Крит текст
        }
        $logHtml .= "<span class='text-gray-400'>{$targetName}</span>"; // Имя цели серым
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение об атаке моба по игроку.
     *
     * @param Mob $attacker Атакующий моб.
     * @param User $target Целевой игрок.
     * @param int $damage Нанесенный урон.
     * @param bool $isCrit Был ли удар критическим.
     * @param array $activatedSkills Активированные скиллы моба (опционально).
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatMobAttack(Mob $attacker, User $target, int $damage, bool $isCrit = false, array $activatedSkills = []): string
    {
        // Проверяем, есть ли среди активированных скиллов скилл стана
        $hasStunSkill = false;
        $stunSkillMessage = '';

        if (!empty($activatedSkills)) {
            foreach ($activatedSkills as $skill) {
                if (
                    isset($skill['skill_type']) && $skill['skill_type'] === 'stun' &&
                    isset($skill['message']) &&
                    (strpos($skill['message'], 'оглушает') !== false || strpos($skill['message'], 'Сильный удар') !== false)
                ) {
                    $hasStunSkill = true;
                    $stunSkillMessage = $skill['message'];
                    break;
                }
            }
        }

        // Если это скилл стана, показываем только сообщение об оглушении без урона
        if ($hasStunSkill) {
            $logHtml = "<span class='flex items-center text-xs'>";
            $logHtml .= "<span class='text-gray-400 mr-1'>{$attacker->name}</span>"; // Имя моба серым
            $logHtml .= "<span class='text-yellow-400'> → {$stunSkillMessage}</span>"; // Сообщение об оглушении
            $logHtml .= "</span>";
            return $logHtml;
        }

        // Обычная атака с уроном
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-gray-400 mr-1'>{$attacker->name} →</span>"; // Имя моба серым
        $logHtml .= "<span class='" . ($isCrit ? 'text-orange-500 font-bold' : 'text-red-400 font-medium') . " mx-1'>" . round($damage) . "</span>"; // Урон красным (или оранжевым для крита)
        if ($isCrit) {
            $logHtml .= "<span class='text-orange-500 font-bold mr-1'>(крит!)</span>"; // Крит текст
        }
        $logHtml .= "<span class='text-red-600'>Вам</span>"; // "Вам" красным
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение об атаке бота по игроку.
     * Формат: [имя бота] нанес Вам [количество урона] урона
     *
     * @param mixed $attacker Атакующий бот.
     * @param User $target Целевой игрок.
     * @param float $damage Нанесенный урон.
     * @param bool $isCrit Был ли удар критическим (не используется в новом формате).
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatBotAttack($attacker, User $target, float $damage, bool $isCrit = false): string
    {
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-purple-400 mr-1'>{$attacker->name}</span>"; // Имя бота фиолетовым
        $logHtml .= "<span class='text-gray-300 mr-1'>нанес Вам</span>"; // Текст светло-серым
        $logHtml .= "<span class='text-red-500 font-medium mx-1'>" . round($damage, 0, PHP_ROUND_HALF_UP) . "</span>"; // Урон красным без анимации
        $logHtml .= "<span class='text-gray-300'>урона</span>"; // Текст светло-серым
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение для игрока, когда его атакует другой игрок.
     *
     * @param User $defender Атакуемый игрок.
     * @param User $attacker Атакующий игрок.
     * @param int $damage Полученный урон.
     * @param bool $isCrit Был ли удар критическим.
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatPlayerDefend(User $defender, User $attacker, int $damage, bool $isCrit = false): string
    {
        // Собираем HTML для лога защищающегося без отображения HP
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-gray-400 mr-1'>{$attacker->name} →</span>"; // Имя атакующего серым
        $logHtml .= "<span class='" . ($isCrit ? 'text-orange-500 font-bold' : 'text-red-400 font-medium') . " mx-1'>" . round($damage) . "</span>"; // Урон красным (или оранжевым для крита)
        if ($isCrit) {
            $logHtml .= "<span class='text-orange-500 font-bold mr-1'>(крит!)</span>"; // Крит текст
        }
        $logHtml .= "<span class='text-red-600'>Вам</span>"; // "Вам" красным
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение об использовании умения.
     *
     * @param mixed $caster Использовавший умение (User или Mob).
     * @param mixed $target Цель умения (User, Mob или null).
     * @param Skill $skill Использованное умение.
     * @param bool $success Успешно ли применено умение.
     * @param string $message Сообщение о результате (например, "не хватило маны", "на кулдауне").
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatSkillUse($caster, $target, Skill $skill, bool $success, string $message): string
    {
        // Определяем имя кастера
        $casterName = $caster instanceof Mob ? $caster->name : ($caster instanceof User ? 'Вы' : 'Неизвестный');
        $casterColor = $caster instanceof User ? 'text-sky-600' : 'text-gray-400'; // Синий для игрока, серый для моба

        // Определяем имя цели, если она есть
        $targetName = '';
        if ($target) {
            $targetName = $target instanceof Mob ? $target->name : ($target instanceof User ? ($target->id === $caster->id ? 'себя' : $target->name) : 'неизвестную цель');
        }

        // Иконка результата
        $resultIcon = $success ? "<span class='text-lime-500'>✅</span>" : "<span class='text-red-500'>❌</span>";
        $messageColor = $success ? 'text-lime-400' : 'text-red-400';

        // Иконка умения
        $skillIconHtml = '';
        if ($skill->icon && file_exists(public_path($skill->icon))) {
            $skillIconHtml = "<img src='" . asset($skill->icon) . "' alt='{$skill->name}' class='w-4 h-4 inline-block mr-1'>";
        } else {
            $skillIconHtml = '🔮'; // Иконка по умолчанию
        }

        // Собираем HTML
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= $skillIconHtml; // Иконка умения
        $logHtml .= "<span class='{$casterColor} mr-1'>{$casterName}</span>"; // Имя кастера

        if ($targetName) {
            $logHtml .= "<span class='text-gray-400 mr-1'>→</span>"; // Стрелка к цели
            $logHtml .= "<span class='text-gray-300 mr-1'>{$targetName}:</span>"; // Имя цели
        }

        $logHtml .= "<span class='{$messageColor} ml-1'>{$message}</span>"; // Сообщение о результате
        $logHtml .= "<span class='ml-1'>{$resultIcon}</span>"; // Иконка успеха/неудачи
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение об использовании умения мобом (для лога игрока).
     *
     * @param Mob $mob Моб, использующий умение.
     * @param Skill $skill Использованное умение.
     * @param User $target Игрок, на которого применено умение.
     * @param int|null $damage Урон от умения (если есть).
     * @param string|null $effectDescription Описание наложенного эффекта (если есть).
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatMobSkillUseOnPlayer(Mob $mob, Skill $skill, User $target, ?int $damage = null, ?string $effectDescription = null): string
    {
        // Иконка умения
        $skillIconHtml = '';
        if ($skill->icon && file_exists(public_path($skill->icon))) {
            $skillIconHtml = "<img src='" . asset($skill->icon) . "' alt='{$skill->name}' class='w-4 h-4 inline-block mr-1'>";
        } else {
            $skillIconHtml = '🔮'; // Иконка по умолчанию
        }

        // Собираем HTML
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= $skillIconHtml; // Иконка умения
        $logHtml .= "<span class='text-gray-400 mr-1'>{$mob->name}</span>"; // Имя моба
        $logHtml .= "<span class='text-violet-400 mr-1'>исп. '{$skill->name}'</span>"; // Название умения

        // Урон
        if ($damage !== null && $damage > 0) {
            $logHtml .= "<span class='text-gray-400 mr-1'>→</span>";
            $logHtml .= "<span class='text-red-400 font-medium mx-1'>" . round($damage) . "</span>"; // Урон
            $logHtml .= "<span class='text-red-600'>Вам</span>"; // "Вам"
        }

        // Эффект
        if ($effectDescription) {
            $logHtml .= "<span class='text-orange-400 ml-1'>({$effectDescription})</span>"; // Доп. эффект
        }

        $logHtml .= "</span>";
        return $logHtml;
    }

    /**
     * Форматирует сообщение о победе над мобом.
     *
     * @param User $user Игрок, победивший моба.
     * @param Mob $mob Побежденный моб.
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatMobVictory(User $user, Mob $mob): string
    {
        $icon = "<span class='text-lime-500 text-sm'>✓</span>";
        $message = "<span class='text-lime-600'>Победа над:</span>";
        $highlight = $mob->name;
        $highlightColor = "text-lime-400";
        $additionalText = "<span class='text-lime-500'>!</span>";

        return $this->formatGenericMessage($icon, $message, $highlight, $highlightColor, $additionalText);
    }

    /**
     * Форматирует сообщение о смерти игрока.
     *
     * @param User $player Погибший игрок.
     * @param mixed $killer Убийца (Mob или User).
     * @param string $deathLocation Место смерти (для лога).
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatPlayerDeath(User $player, $killer, string $deathLocation): string
    {
        $killerName = $killer instanceof Mob ? $killer->name : ($killer instanceof User ? $killer->name : 'неизвестный');

        // Создаем HTML напрямую для лучшего контроля стилей
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='mr-1 text-red-600'>💀</span>"; // Иконка смерти красного цвета
        $logHtml .= "<span class='font-medium text-red-500'>Вы были побеждены:</span>"; // Основное сообщение
        $logHtml .= "<span class='ml-1 font-bold text-red-400'>{$killerName}</span>"; // Имя убийцы
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение о смерти игрока в PvP (для лога убийцы).
     *
     * @param User $killer Игрок-убийца.
     * @param User $deadPlayer Убитый игрок.
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatPlayerKill(User $killer, User $deadPlayer): string
    {
        $icon = "☠️";
        $message = "<span class='text-lime-600'>Вы добили игрока</span>";
        $highlight = $deadPlayer->name;
        $highlightColor = "text-lime-500";
        $additionalText = "<span class='text-lime-600'>!</span>";

        return $this->formatGenericMessage($icon, $message, $highlight, $highlightColor, $additionalText);
    }

    /**
     * Форматирует сообщение об атаке игрока другим игроком (для лога атакующего).
     * Используется в CustomOutpostController для аванпостов, созданных через админку.
     *
     * @param User $attacker Атакующий игрок.
     * @param User $target Целевой игрок.
     * @param int $damage Нанесенный урон.
     * @param bool $isCrit Был ли удар критическим.
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatPlayerAttackPlayer(User $attacker, User $target, int $damage, bool $isCrit = false): string
    {
        // Собираем HTML без отображения HP
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-green-600 mr-1'>Вы →</span>"; // "Вы →" зеленым
        $logHtml .= "<span class='" . ($isCrit ? 'text-orange-500 font-bold' : 'text-red-400 font-medium') . " mx-1'>" . round($damage) . "</span>"; // Урон красным (или оранжевым для крита)
        if ($isCrit) {
            $logHtml .= "<span class='text-orange-500 font-bold mr-1'>(крит!)</span>"; // Крит текст
        }
        $logHtml .= "<span class='text-gray-400'>{$target->name}</span>"; // Имя цели серым
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение для игрока, когда его атакует другой игрок (для лога защищающегося).
     * Используется в CustomOutpostController для аванпостов, созданных через админку.
     *
     * @param User $defender Атакуемый игрок.
     * @param User $attacker Атакующий игрок.
     * @param int $damage Полученный урон.
     * @param bool $isCrit Был ли удар критическим.
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatPlayerAttackedByPlayer(User $defender, User $attacker, int $damage, bool $isCrit = false): string
    {
        // Собираем HTML для лога защищающегося без отображения HP
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-gray-400 mr-1'>{$attacker->name} →</span>"; // Имя атакующего серым
        $logHtml .= "<span class='" . ($isCrit ? 'text-orange-500 font-bold' : 'text-red-400 font-medium') . " mx-1'>" . round($damage) . "</span>"; // Урон красным (или оранжевым для крита)
        if ($isCrit) {
            $logHtml .= "<span class='text-orange-500 font-bold mr-1'>(крит!)</span>"; // Крит текст
        }
        $logHtml .= "<span class='text-red-600'>Вам</span>"; // "Вам" красным
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение об атаке моба на замеченного игрока в руднике.
     *
     * @param string $mobName Имя моба
     * @param string $playerName Имя игрока
     * @param int $damage Нанесенный урон
     * @param string $mineName Название рудника
     * @param array $activatedSkills Активированные скиллы моба
     * @return string Отформатированная HTML-строка лога
     */
    public function formatMineDetectionAttack(string $mobName, string $playerName, int $damage, string $mineName, array $activatedSkills = []): string
    {
        // Проверяем, есть ли среди активированных скиллов скилл стана "Сильный удар"
        $hasStunSkill = false;
        $stunSkillMessage = '';

        if (!empty($activatedSkills)) {
            foreach ($activatedSkills as $skill) {
                if (
                    isset($skill['skill_type']) && $skill['skill_type'] === 'stun' &&
                    isset($skill['message']) &&
                    (strpos($skill['message'], 'оглушает') !== false || strpos($skill['message'], 'Сильный удар') !== false)
                ) {
                    $hasStunSkill = true;
                    $stunSkillMessage = $skill['message'];
                    break;
                }
            }
        }

        // Если это скилл стана, показываем только сообщение об оглушении без урона
        if ($hasStunSkill) {
            $logHtml = "<span class='flex items-center text-xs'>";
            $logHtml .= "<span class='text-amber-600 mr-1'></span>"; // Иконка рудника для контекста
            $logHtml .= "<span class='text-gray-400 mr-1'>{$mobName}</span>"; // Имя моба серым
            $logHtml .= "<span class='text-yellow-400'> → {$stunSkillMessage}</span>"; // Сообщение об оглушении
            $logHtml .= "</span>";
            return $logHtml;
        }

        // Обычная атака с уроном
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-amber-600 mr-1'></span>"; // Иконка рудника для контекста
        $logHtml .= "<span class='text-gray-400 mr-1'>{$mobName} →</span>"; // Имя моба серым, как в стандартных атаках
        $logHtml .= "<span class='text-red-400 font-medium mx-1'>" . round($damage) . "</span>"; // Урон красным
        $logHtml .= "<span class='text-red-600'>Вам</span>"; // "Вам" красным, как в стандартных атаках

        // Добавляем информацию об активированных скиллах (кроме стана, который уже обработан выше)
        if (!empty($activatedSkills)) {
            foreach ($activatedSkills as $skill) {
                if (
                    isset($skill['message']) &&
                    !(isset($skill['skill_type']) && $skill['skill_type'] === 'stun')
                ) {
                    // Используем {!! !!} для корректного отображения HTML в сообщении скилла
                    $logHtml .= "<br><span class='text-yellow-400 text-xs'>" . $skill['message'] . "</span>";
                }
            }
        }

        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение об ответной атаке игрока (для лога атакующего).
     * Используется в CustomOutpostController для аванпостов, созданных через админку.
     *
     * @param User $attacker Атакующий игрок (тот, кто наносит ответный удар).
     * @param User $target Целевой игрок (тот, кто атаковал первым).
     * @param int $damage Нанесенный урон.
     * @param bool $isCrit Был ли удар критическим.
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatPlayerRetaliatePlayer(User $attacker, User $target, int $damage, bool $isCrit = false): string
    {
        // Собираем HTML без отображения HP
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-green-600 mr-1'>Вы ↺</span>"; // "Вы ↺" зеленым (символ ответного удара)
        $logHtml .= "<span class='" . ($isCrit ? 'text-orange-500 font-bold' : 'text-red-400 font-medium') . " mx-1'>" . round($damage) . "</span>"; // Урон красным (или оранжевым для крита)
        if ($isCrit) {
            $logHtml .= "<span class='text-orange-500 font-bold mr-1'>(крит!)</span>"; // Крит текст
        }
        $logHtml .= "<span class='text-gray-400'>{$target->name}</span>"; // Имя цели серым
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение для игрока, когда его атакуют в ответ (для лога защищающегося).
     * Используется в CustomOutpostController для аванпостов, созданных через админку.
     *
     * @param User $defender Атакуемый игрок (тот, кто атаковал первым).
     * @param User $attacker Атакующий игрок (тот, кто наносит ответный удар).
     * @param int $damage Полученный урон.
     * @param bool $isCrit Был ли удар критическим.
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatPlayerRetaliatedByPlayer(User $defender, User $attacker, int $damage, bool $isCrit = false): string
    {
        // Собираем HTML для лога защищающегося без отображения HP
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-gray-400 mr-1'>{$attacker->name} ↺</span>"; // Имя атакующего серым, символ ответного удара
        $logHtml .= "<span class='" . ($isCrit ? 'text-orange-500 font-bold' : 'text-red-400 font-medium') . " mx-1'>" . round($damage) . "</span>"; // Урон красным (или оранжевым для крита)
        if ($isCrit) {
            $logHtml .= "<span class='text-orange-500 font-bold mr-1'>(крит!)</span>"; // Крит текст
        }
        $logHtml .= "<span class='text-red-600'>Вам</span>"; // "Вам" красным
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение о получении предмета, ресурса или ингредиента.
     *
     * @param User $user Игрок, получивший предмет.
     * @param mixed $item Полученный предмет (GameItem, Resource или AlchemyIngredient).
     * @param int $quantity Количество.
     * @return string|null Отформатированная HTML-строка лога или null при ошибке.
     */
    public function formatItemDrop(User $user, $item, int $quantity): ?string
    {
        $itemName = '';
        $itemIcon = '';
        $rarityClass = 'text-gray-400'; // Цвет по умолчанию

        try {
            if ($item instanceof GameItem && $item->item) {
                // Это игровой предмет (GameItem)
                $itemName = $item->item->name;
                $itemIcon = $item->item->icon && file_exists(public_path($item->item->icon)) ? asset($item->item->icon) : null;
                $rarityClass = $this->getQualityColorClass($item->quality ?? 'common');
                Log::info('formatItemDrop обрабатывает GameItem', [
                    'name' => $itemName,
                    'icon' => $itemIcon ? 'имеется' : 'отсутствует'
                ]);
            } elseif ($item instanceof Resource) {
                // Это ресурс (Resource)
                $itemName = $item->name;
                $itemIcon = $item->icon && file_exists(public_path($item->icon)) ? asset($item->icon) : null;
                $rarityClass = $this->getResourceColorClass($item->id); // Цвет для ресурсов
                Log::info('formatItemDrop обрабатывает Resource', [
                    'name' => $itemName,
                    'icon' => $itemIcon ? 'имеется' : 'отсутствует'
                ]);
            } elseif ($item instanceof AlchemyIngredient) {
                // Это алхимический ингредиент
                $itemName = $item->name;
                // Используем аксессор для получения полного URL иконки
                $iconPath = $item->getIconPathAttribute();

                // Получаем относительный путь для проверки существования файла
                $relativeIconPath = '';
                if ($item->image_path) {
                    $relativeIconPath = 'storage/' . $item->image_path;
                } elseif ($item->icon) {
                    if (str_starts_with($item->icon, 'assets/')) {
                        $relativeIconPath = $item->icon;
                    } else {
                        $relativeIconPath = 'assets/icons/alchemy/' . $item->icon;
                    }
                } else {
                    $relativeIconPath = 'assets/icons/alchemy/default.png';
                }

                // Проверяем существование файла по относительному пути
                $itemIcon = file_exists(public_path($relativeIconPath)) ? $iconPath : null;

                $rarityClass = $item->getCssClassForRarity(); // Используем метод модели для цвета

                Log::info('formatItemDrop обрабатывает AlchemyIngredient', [
                    'id' => $item->id,
                    'name' => $itemName,
                    'icon_path' => $iconPath,
                    'relative_path' => $relativeIconPath,
                    'icon_exists' => file_exists(public_path($relativeIconPath)),
                    'rarity_class' => $rarityClass
                ]);
            } else {
                // Неизвестный тип предмета
                Log::warning("Попытка отформатировать лог для неизвестного типа предмета.", ['item_type' => get_class($item)]);
                $itemName = 'Неизвестный предмет';
                $itemIcon = null;
            }
        } catch (\Exception $e) {
            Log::error("Ошибка при получении данных предмета для лога: " . $e->getMessage(), [
                'item_details' => $item,
                'exception' => $e
            ]);
            return null; // Возвращаем null в случае ошибки
        }

        // Иконка HTML
        $iconHtml = $itemIcon ? "<img src='{$itemIcon}' alt='{$itemName}' class='w-3 h-3 inline-block'>" : "❓"; // Иконка по умолчанию, если нет пути

        // Сообщение
        $message = $quantity > 1 ? "{$itemName} x{$quantity}" : $itemName;

        $result = $this->formatGenericMessage(
            $iconHtml,
            "", // Пустое основное сообщение
            $message,
            $rarityClass // Используем цвет редкости/типа
        );

        Log::info('formatItemDrop возвращает результат', [
            'item_name' => $itemName,
            'quantity' => $quantity,
            'result_length' => strlen($result),
            'result_excerpt' => substr($result, 0, 50) . '...'
        ]);

        return $result;
    }

    /**
     * Форматирует сообщение о получении опыта.
     *
     * @param User $user Игрок.
     * @param int $experience Полученное количество опыта.
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatExperienceGain(User $user, int $experience): string
    {
        $icon = "<span class='text-blue-400'>✦</span>";
        $message = "Опыт:";
        $highlight = "+{$experience}";
        $highlightColor = "text-sky-400";

        return $this->formatGenericMessage($icon, $message, $highlight, $highlightColor);
    }

    /**
     * Форматирует сообщение о получении валюты.
     *
     * @param User $user Игрок.
     * @param array $currency Массив с валютой ['gold' => amount, 'silver' => amount, 'bronze' => amount].
     * @return array Массив отформатированных HTML-строк лога для каждого типа валюты.
     */
    public function formatCurrencyGain(User $user, array $currency): array
    {
        $logs = [];
        $currencyOrder = ['gold', 'silver', 'bronze']; // Порядок отображения

        foreach ($currencyOrder as $type) {
            if (isset($currency[$type]) && $currency[$type] > 0) {
                $amount = $currency[$type];
                $iconPath = asset("assets/{$type}Icon.png"); // Путь к иконке валюты
                $iconHtml = "<img src='{$iconPath}' alt='{$type}' class='w-3 h-3 inline-block'>";
                $highlight = "+{$amount}";
                $highlightColor = "text-yellow-400"; // Цвет для валюты

                $logs[] = $this->formatGenericMessage(
                    $iconHtml,
                    "", // Пустое основное сообщение
                    $highlight,
                    $highlightColor
                );
            }
        }
        return $logs;
    }

    /**
     * Форматирует сообщение о том, что инвентарь полон и предмет не может быть добавлен.
     *
     * @param User $user Игрок, чей инвентарь полон.
     * @param mixed $item Предмет или ингредиент, который не был добавлен.
     * @param int $quantity Количество предметов, которые не были добавлены.
     * @param string|null $iconPath Путь к иконке предмета (если известен).
     * @return string|null Отформатированная HTML-строка или null, если сообщение не нужно выводить.
     */
    public function formatInventoryFull(User $user, $item, int $quantity, ?string $iconPath = null): ?string
    {
        // Получаем имя предмета в зависимости от типа
        $itemName = '';

        if ($item instanceof GameItem && $item->item) {
            $itemName = $item->item_name ?? $item->item->name;
            if (!$iconPath && $item->item && $item->item->icon) {
                $iconPath = $item->item->icon;
            }
        } elseif ($item instanceof AlchemyIngredient) {
            $itemName = $item->name;
            if (!$iconPath && $item->icon) {
                $iconPath = $item->icon;
            }

            // Не показываем сообщение для Лиантера и Фаерлина
            if ($itemName === 'Лиантер' || $itemName === 'Фаерлин') {
                return null;
            }
        } elseif ($item instanceof Resource) {
            $itemName = $item->name;
            if (!$iconPath && $item->icon) {
                $iconPath = $item->icon;
            }
        } elseif (is_string($item)) {
            // Если передано просто имя предмета строкой
            $itemName = $item;

            // Не показываем сообщение для Лиантера и Фаерлина
            if ($itemName === 'Лиантер' || $itemName === 'Фаерлин') {
                return null;
            }
        } else {
            $itemName = 'неизвестный предмет';
        }

        // Собираем HTML
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='mr-1 text-red-500'>❌</span>";
        $logHtml .= $iconPath
            ? "<img src='" . asset($iconPath) . "' class='w-4 h-4 mr-1 inline-block align-middle' alt='{$itemName}'>"
            : "<span class='w-4 h-4 mr-1 inline-block align-middle text-gray-400'>?</span>";
        $logHtml .= "<span class='text-red-400 mx-1'>{$itemName} x{$quantity} потерян (инвентарь полон).</span>";
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Общая функция для форматирования сообщений лога.
     * Собирает HTML-строку из компонентов.
     *
     * @param string $icon Эмодзи или HTML-код иконки.
     * @param string $message Основное сообщение (может содержать HTML).
     * @param string $highlight Выделенный текст (например, урон, имя).
     * @param string $highlightColor CSS класс Tailwind для цвета выделенного текста.
     * @param string $additionalText Дополнительный текст после выделенного (может содержать HTML).
     * @return string Отформатированная HTML-строка.
     */
    public function formatGenericMessage(string $icon, string $message, string $highlight = '', string $highlightColor = '', string $additionalText = ''): string
    {
        // Иконка (может быть HTML)
        $iconHtml = !empty($icon) ? "<span class='inline-flex items-center mr-1'>{$icon}</span>" : "";

        // Основное сообщение (может быть HTML)
        $messageHtml = $message;

        // Выделенный текст (обычно число урона или имя)
        $highlightHtml = !empty($highlight)
            ? "<span class='" . (!empty($highlightColor) ? $highlightColor : "text-gray-300") . " font-medium mx-1'>{$highlight}</span>"
            : '';

        // Дополнительный текст (может быть HTML)
        $additionalHtml = !empty($additionalText) ? "<span class='ml-1'>{$additionalText}</span>" : '';

        // Возвращаем собранную строку
        return "<span class='flex items-center text-xs'>{$iconHtml}{$messageHtml}{$highlightHtml}{$additionalHtml}</span>";
    }

    // ---------------------------------------------------------------------
    // Вспомогательные методы для определения цветов (скопированы из ElvenHavenController)
    // ---------------------------------------------------------------------

    /**
     * Получает CSS класс цвета для ресурса по его ID.
     *
     * @param int $resourceId ID ресурса.
     * @return string CSS класс Tailwind.
     */
    protected function getResourceColorClass($resourceId): string
    {
        // Цвета для ресурсов
        switch ($resourceId) {
            case 1:
                return 'text-yellow-700'; // Древесина
            case 2:
                return 'text-slate-400';  // Камень
            case 3:
                return 'text-cyan-500';   // Металл
            // Добавьте другие ID
            default:
                return 'text-gray-400'; // По умолчанию
        }
    }

    /**
     * Получает CSS класс Tailwind для отображения качества предмета.
     *
     * @param string $quality Качество предмета ('common', 'uncommon', etc.).
     * @return string CSS класс Tailwind.
     */
    protected function getQualityColorClass($quality): string
    {
        // Цвета для качества предметов
        $classes = [
            'common' => 'text-gray-300',      // Обычный
            'uncommon' => 'text-emerald-400', // Необычный
            'rare' => 'text-blue-400',       // Редкий
            'epic' => 'text-violet-400',     // Эпический
            'legendary' => 'text-amber-400',   // Легендарный
        ];
        return $classes[strtolower($quality)] ?? 'text-gray-300'; // По умолчанию - обычный
    }

    /**
     * Определяет класс анимации свечения на основе CSS класса стиля предмета
     *
     * @param string $itemStyle CSS класс стиля предмета (например, 'text-blue-400')
     * @return string Класс анимации свечения
     */
    protected function getGlowClassFromStyle(string $itemStyle): string
    {
        // ИСПРАВЛЕНИЕ: Обновленная карта соответствия цветов текста и классов анимации свечения
        $glowMap = [
            'text-gray-300' => 'animate-breathing-glow glow-color-gray-200',     // Обычное (common)
            'text-emerald-400' => 'animate-breathing-glow glow-color-green-400', // Необычное (uncommon)
            'text-green-400' => 'animate-breathing-glow glow-color-green-400',   // Необычное (альтернативный)
            'text-blue-400' => 'animate-breathing-glow glow-color-blue-400',     // Редкое (rare)
            'text-violet-400' => 'animate-breathing-glow glow-color-purple-400', // Эпическое (epic)
            'text-purple-400' => 'animate-breathing-glow glow-color-purple-400', // Эпическое (альтернативный)
            'text-amber-400' => 'animate-breathing-glow glow-color-orange-400',  // Легендарное (legendary)
            'text-orange-400' => 'animate-breathing-glow glow-color-orange-400', // Легендарное (альтернативный)
        ];

        // Ищем соответствующий класс анимации
        foreach ($glowMap as $textColor => $glowClass) {
            if (strpos($itemStyle, $textColor) !== false) {
                return $glowClass;
            }
        }

        // По умолчанию возвращаем анимацию для обычного качества
        return 'animate-breathing-glow glow-color-gray-200';
    }

    /**
     * Форматирует сообщение о лечении ботом (жрецом) союзника (бота или игрока).
     * УСТАРЕВШИЙ МЕТОД - используйте formatBotHealing() для нового формата
     *
     * @param Bot $healer Бот-жрец, который лечит
     * @param mixed $target Цель лечения (User или Bot)
     * @param int $healAmount Количество восстановленного HP
     * @param int $oldHp Старое значение HP
     * @param int $newHp Новое значение HP
     * @return string Отформатированная HTML-строка лога
     */
    public function formatBotHeal($healer, $target, int $healAmount, int $oldHp, int $newHp): string
    {
        // Иконка скилла
        $iconPath = asset('assets/skillHeal.png');
        $iconHtml = "<img src='{$iconPath}' alt='Лечение' class='w-4 h-4 inline-block mr-0.5 align-middle'>";

        // Имя цели
        $targetName = $target instanceof \App\Models\User ? 'Вас' : $target->name;
        $targetColor = $target instanceof \App\Models\User ? 'text-lime-400' : 'text-gray-300';

        // Имя бота-жреца
        $healerName = $healer->name;

        // Компактный HTML без 'было → стало'
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-green-600 mr-0.5'>{$healerName}</span>";
        $logHtml .= "<span class='text-gray-400 mr-0.5'>исцеляет</span>";
        $logHtml .= "<span class='{$targetColor} mr-0.5'>{$targetName}</span>";
        $logHtml .= $iconHtml;
        $logHtml .= "<span class='text-green-400 font-bold ml-0.5'>+{$healAmount}</span>";
        $logHtml .= "</span>";
        return $logHtml;
    }

    /**
     * Форматирует сообщение о лечении ботом игрока в требуемом формате.
     * Формат: [имя бота] исцелил Вас на [значение исцеления] HP
     *
     * @param mixed $healer Бот-жрец, который лечит
     * @param mixed $target Цель лечения (User или Bot)
     * @param int $healAmount Количество восстановленного HP
     * @param bool $isCritical Было ли лечение критическим (опционально)
     * @return string Отформатированная HTML-строка лога
     */
    public function formatBotHealing($healer, $target, int $healAmount, bool $isCritical = false): string
    {
        // Имя бота-жреца (зеленый цвет)
        $healerName = $healer->name ?? 'Неизвестный жрец';

        // Определяем текст в зависимости от цели
        // Проверяем является ли цель игроком (User) по классу или наличию определенных свойств
        $isPlayer = $target instanceof \App\Models\User ||
            (is_object($target) && property_exists($target, 'email')) ||
            (is_object($target) && method_exists($target, 'getTable') && $target->getTable() === 'users');

        $targetText = $isPlayer ? 'Вас' : ($target->name ?? 'цель');

        // Определяем текст действия (критическое или обычное лечение)
        $actionText = $isCritical ? 'критически исцелил' : 'исцелил';

        // Формируем HTML согласно требованиям:
        // - Имя бота: зеленый цвет (text-green-400)
        // - Текст "исцелил Вас на": обычный цвет (text-gray-300)
        // - Значение исцеления и "HP": зеленый цвет (text-green-500)
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-green-400'>{$healerName}</span>";
        $logHtml .= "<span class='text-gray-300 ml-1'>{$actionText} {$targetText} на</span>";
        $logHtml .= "<span class='text-green-500 ml-1 font-medium'>{$healAmount} HP</span>";
        $logHtml .= "</span>";

        return $logHtml;
    }

    /**
     * Форматирует сообщение о применении баффа ботом (например, усиление брони) в едином стиле с лечением.
     *
     * @param $caster Бот, который применяет бафф
     * @param $target Цель баффа (обычно игрок)
     * @param string $buffName Название баффа (например, "Укрепление брони")
     * @param int $buffValue Значение баффа (например, +500)
     * @param int $duration Длительность баффа в секундах
     * @return string Отформатированная HTML-строка лога
     */
    public function formatBotBuff($caster, $target, string $buffName, int $buffValue, int $duration): string
    {
        // Используем ту же иконку, что и для лечения, чтобы стиль был единым
        $iconPath = asset('assets/skillShieldPriest.png');
        $iconHtml = "<img src='{$iconPath}' alt='Бафф' class='w-4 h-4 inline-block mr-0.5 align-middle'>";

        // Имя цели
        $targetName = $target instanceof \App\Models\User ? 'Вас' : $target->name;
        $targetColor = $target instanceof \App\Models\User ? 'text-lime-400' : 'text-gray-300';

        // Имя бота
        $casterName = $caster->name;

        // Формируем HTML в едином стиле с лечением
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-green-600 mr-0.5'>{$casterName}</span>";
        $logHtml .= "<span class='text-gray-400 mr-0.5'>наложил на</span>";
        $logHtml .= "<span class='{$targetColor} mr-0.5'>{$targetName}</span>";
        $logHtml .= $iconHtml;
        $logHtml .= "<span class='text-blue-400 font-bold ml-0.5'>+{$buffValue} ({$duration} сек)</span>";
        $logHtml .= "</span>";
        return $logHtml;
    }

    /**
     * Форматирует сообщение о получении урона от окружения (ловушки, яд и т.д.).
     *
     * @param User $user Игрок, получивший урон.
     * @param int $damage Полученный урон.
     * @param string $source Источник урона (например, 'ловушка', 'яд').
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatEnvironmentDamage(User $user, int $damage, string $source): string
    {
        $icon = "<span class='text-orange-500'>💥</span>"; // Иконка урона от окружения
        $message = "<span class='text-orange-600'>Урон от '{$source}':</span>";
        $highlight = round($damage);
        $highlightColor = "text-red-400 font-medium"; // Красный цвет для урона

        return $this->formatGenericMessage($icon, $message, $highlight, $highlightColor);
    }

    /**
     * Форматирует сообщение о получении награды от обелиска для самого игрока
     *
     * @param string $itemIcon Иконка предмета (HTML или эмодзи)
     * @param string $itemName Название предмета
     * @param string $itemStyle CSS класс для стиля предмета
     * @param bool $includeItemName Включать ли название предмета в сообщение (для персонального лога)
     * @return string Отформатированная HTML-строка лога
     */
    public function formatObeliskRewardForSelf(string $itemIcon, string $itemName, string $itemStyle, bool $includeItemName = false): string
    {
        // Логируем входные параметры для отладки
        Log::info("formatObeliskRewardForSelf вызван с параметрами", [
            'itemIcon' => $itemIcon,
            'itemName' => $itemName,
            'itemStyle' => $itemStyle,
            'includeItemName' => $includeItemName
        ]);

        // Определяем класс анимации качества на основе стиля предмета
        $glowClass = $this->getGlowClassFromStyle($itemStyle);

        // Создаем HTML напрямую для лучшего контроля стилей
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-green-400 mr-1'>Вы получили</span>"; // Сообщение

        // Добавляем иконку предмета с анимацией качества
        if (strpos($itemIcon, '<img') !== false) {
            // Если это HTML-тег img, добавляем классы анимации
            $itemIcon = str_replace('class=\'', "class='$glowClass ", $itemIcon);
            $logHtml .= "<span class='mr-1 {$itemStyle}'>{$itemIcon}</span>";
        } else {
            // Если это эмодзи, оборачиваем в span с анимацией
            $logHtml .= "<span class='mr-1 {$itemStyle} {$glowClass}'>{$itemIcon}</span>";
        }

        // Добавляем название предмета, если требуется (для персонального лога)
        if ($includeItemName) {
            $logHtml .= "<span class='{$itemStyle} ml-1'>{$itemName}</span>";
        }

        $logHtml .= "</span>";

        // Логируем результат для отладки
        Log::info("formatObeliskRewardForSelf результат", [
            'html' => $logHtml,
            'glow_class' => $glowClass
        ]);

        return $logHtml;
    }

    /**
     * Форматирует сообщение о получении награды от обелиска для других игроков
     *
     * @param string $playerName Имя игрока, получившего награду
     * @param string $itemIcon Иконка предмета (HTML или эмодзи)
     * @param string $itemName Название предмета (не используется в выводе)
     * @param string $itemStyle CSS класс для стиля предмета
     * @return string Отформатированная HTML-строка лога
     */
    public function formatObeliskRewardForOthers(string $playerName, string $itemIcon, string $itemName, string $itemStyle): string
    {
        // Логируем входные параметры для отладки
        Log::info("formatObeliskRewardForOthers вызван с параметрами", [
            'playerName' => $playerName,
            'itemIcon' => $itemIcon,
            'itemName' => $itemName,
            'itemStyle' => $itemStyle
        ]);

        // Определяем класс анимации качества на основе стиля предмета
        $glowClass = $this->getGlowClassFromStyle($itemStyle);

        // Создаем HTML напрямую для лучшего контроля стилей
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-gray-400 mr-1'>{$playerName} получил</span>"; // Имя игрока и сообщение

        // Добавляем иконку предмета с анимацией качества
        if (strpos($itemIcon, '<img') !== false) {
            // Если это HTML-тег img, добавляем классы анимации
            $itemIcon = str_replace('class=\'', "class='$glowClass ", $itemIcon);
            $logHtml .= "<span class='mr-1 {$itemStyle}'>{$itemIcon}</span>";
        } else {
            // Если это эмодзи, оборачиваем в span с анимацией
            $logHtml .= "<span class='mr-1 {$itemStyle} {$glowClass}'>{$itemIcon}</span>";
        }

        $logHtml .= "</span>";

        // Логируем результат для отладки
        Log::info("formatObeliskRewardForOthers результат", [
            'html' => $logHtml,
            'glow_class' => $glowClass
        ]);

        return $logHtml;
    }

    /**
     * Форматирует сообщение о неудачной попытке получения награды от обелиска для самого игрока (полный рюкзак)
     *
     * @param string $itemIcon Иконка предмета (HTML или эмодзи)
     * @param string $itemName Название предмета
     * @param string $itemStyle CSS класс для стиля предмета
     * @param bool $includeItemName Включать ли название предмета в сообщение (для персонального лога)
     * @return string Отформатированная HTML-строка лога
     */
    public function formatObeliskRewardFailureForSelf(string $itemIcon, string $itemName, string $itemStyle, bool $includeItemName = false): string
    {
        // Логируем входные параметры для отладки
        Log::info("formatObeliskRewardFailureForSelf вызван с параметрами", [
            'itemIcon' => $itemIcon,
            'itemName' => $itemName,
            'itemStyle' => $itemStyle,
            'includeItemName' => $includeItemName
        ]);

        // Определяем класс анимации качества на основе стиля предмета
        $glowClass = $this->getGlowClassFromStyle($itemStyle);

        // Создаем HTML напрямую для лучшего контроля стилей
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-red-400 mr-1'>Вы не получили</span>"; // Сообщение "Вы не получили" вместо "Предмет"

        // Добавляем иконку предмета с анимацией качества
        if (strpos($itemIcon, '<img') !== false) {
            // Если это HTML-тег img, добавляем классы анимации
            $itemIcon = str_replace('class=\'', "class='$glowClass ", $itemIcon);
            $logHtml .= "<span class='mr-1 {$itemStyle}'>{$itemIcon}</span>";
        } else {
            // Если это эмодзи, оборачиваем в span с анимацией
            $logHtml .= "<span class='mr-1 {$itemStyle} {$glowClass}'>{$itemIcon}</span>";
        }

        // Добавляем название предмета, если требуется (для персонального лога)
        if ($includeItemName) {
            $logHtml .= "<span class='{$itemStyle} ml-1'>{$itemName}</span>";
        }

        $logHtml .= "<span class='text-red-400'>(рюкзак полон)</span>"; // Дополнительный текст в скобках
        $logHtml .= "</span>";

        // Логируем результат для отладки
        Log::info("formatObeliskRewardFailureForSelf результат", [
            'html' => $logHtml,
            'glow_class' => $glowClass
        ]);

        return $logHtml;
    }

    /**
     * Форматирует сообщение о неудачной попытке получения награды от обелиска для других игроков (полный рюкзак)
     *
     * @param string $playerName Имя игрока, не получившего награду
     * @param string $itemIcon Иконка предмета (HTML или эмодзи)
     * @param string $itemName Название предмета (не используется в выводе)
     * @param string $itemStyle CSS класс для стиля предмета
     * @return string Отформатированная HTML-строка лога
     */
    public function formatObeliskRewardFailureForOthers(string $playerName, string $itemIcon, string $itemName, string $itemStyle): string
    {
        // Логируем входные параметры для отладки
        Log::info("formatObeliskRewardFailureForOthers вызван с параметрами", [
            'playerName' => $playerName,
            'itemIcon' => $itemIcon,
            'itemName' => $itemName,
            'itemStyle' => $itemStyle
        ]);

        // Определяем класс анимации качества на основе стиля предмета
        $glowClass = $this->getGlowClassFromStyle($itemStyle);

        // Создаем HTML напрямую для лучшего контроля стилей
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-gray-400 mr-1'>{$playerName} не получил</span>"; // Имя игрока и сообщение

        // Добавляем иконку предмета с анимацией качества
        if (strpos($itemIcon, '<img') !== false) {
            // Если это HTML-тег img, добавляем классы анимации
            $itemIcon = str_replace('class=\'', "class='$glowClass ", $itemIcon);
            $logHtml .= "<span class='mr-1 {$itemStyle}'>{$itemIcon}</span>";
        } else {
            // Если это эмодзи, оборачиваем в span с анимацией
            $logHtml .= "<span class='mr-1 {$itemStyle} {$glowClass}'>{$itemIcon}</span>";
        }

        $logHtml .= "<span class='text-gray-400'>(рюкзак полон)</span>"; // Дополнительный текст
        $logHtml .= "</span>";

        // Логируем результат для отладки
        Log::info("formatObeliskRewardFailureForOthers результат", [
            'html' => $logHtml,
            'glow_class' => $glowClass
        ]);

        return $logHtml;
    }

    /**
     * Форматирует сообщение об окончании действия эффекта (баффа/дебаффа).
     * Использует иконку умения, если она доступна.
     *
     * @param \App\Models\ActiveEffect $effect Истекший эффект.
     * @return string Отформатированная HTML-строка лога.
     */
    public function formatEffectEnd(\App\Models\ActiveEffect $effect): string
    {
        // Получаем умение, связанное с эффектом
        $skill = $effect->skill;
        // Инициализируем переменную для HTML иконки
        $skillIconHtml = '';

        // Пытаемся получить иконку умения
        try {
            if ($skill && $skill->icon && file_exists(public_path($skill->icon))) {
                // Если иконка есть, создаем тег img
                $skillIconHtml = "<img src='" . asset($skill->icon) . "' alt='" . e($skill->name ?? 'Эффект') . "' class='w-4 h-4 inline-block align-middle'>";
            } else {
                // Если иконки нет или умения нет, используем иконку по умолчанию
                $skillIconHtml = "<span class='inline-block align-middle'>✨</span>"; // Иконка по умолчанию (звездочка)
            }
        } catch (\Exception $e) {
            // Логируем ошибку, если не удалось получить иконку
            Log::error('Ошибка при получении иконки умения для лога завершения эффекта.', [
                'effect_id' => $effect->id,
                'skill_id' => $skill ? $skill->id : 'null',
                'icon_path' => $skill ? $skill->icon : 'null',
                'error' => $e->getMessage()
            ]);
            // Используем иконку по умолчанию в случае ошибки
            $skillIconHtml = "<span class='inline-block align-middle'>✨</span>"; // Иконка по умолчанию (звездочка)
        }


        // Собираем HTML сообщение лога
        // Используем flex для выравнивания элементов по вертикали
        // text-xs для маленького шрифта, text-amber-400 для янтарного/оранжевого цвета текста
        $logHtml = "<span class='flex items-center text-xs text-amber-400'>";
        // Иконка предупреждения в начале сообщения
        $logHtml .= "<span class='mr-1'>⚠️</span>";
        // Статический текст "Эффект " с отступом справа
        $logHtml .= "<span class='mr-1'>Эффект</span>";
        // Вставляем HTML код иконки умения (или иконку по умолчанию)
        $logHtml .= $skillIconHtml;
        // Статический текст " закончился" с отступом слева
        $logHtml .= "<span class='ml-1'>закончился</span>";
        // Закрываем основной span контейнер
        $logHtml .= "</span>";

        // Возвращаем готовую HTML строку
        return $logHtml;
    }

    /**
     * Форматирует сообщение о получении награды от моба в стиле обелиска
     *
     * @param string $itemIcon Иконка предмета/ресурса/валюты (HTML или эмодзи)
     * @param string $itemName Название предмета/ресурса/валюты
     * @param string $itemStyle CSS класс для стиля предмета
     * @param bool $includeItemName Включать ли название предмета в сообщение
     * @return string Отформатированная HTML-строка лога
     */
    public function formatMobDropReward(string $itemIcon, string $itemName, string $itemStyle, bool $includeItemName = true): string
    {
        // Логируем входные параметры для отладки
        Log::info("formatMobDropReward вызван с параметрами", [
            'itemIcon' => $itemIcon,
            'itemName' => $itemName,
            'itemStyle' => $itemStyle,
            'includeItemName' => $includeItemName
        ]);

        // Определяем класс анимации качества на основе стиля предмета
        $glowClass = $this->getGlowClassFromStyle($itemStyle);

        // Создаем HTML напрямую для лучшего контроля стилей (в стиле обелиска)
        $logHtml = "<span class='flex items-center text-xs'>";
        $logHtml .= "<span class='text-green-400 mr-1'>Вы получили</span>"; // Сообщение в стиле обелиска

        // Добавляем иконку предмета с анимацией качества
        if (strpos($itemIcon, '<img') !== false) {
            // Если это HTML-тег img, добавляем классы анимации
            $itemIcon = str_replace('class=\'', "class='$glowClass ", $itemIcon);
            $logHtml .= "<span class='mr-1 {$itemStyle}'>{$itemIcon}</span>";
        } else {
            // Если это эмодзи, оборачиваем в span с анимацией
            $logHtml .= "<span class='mr-1 {$itemStyle} {$glowClass}'>{$itemIcon}</span>";
        }

        // Добавляем название предмета, если требуется
        if ($includeItemName) {
            $logHtml .= "<span class='{$itemStyle} ml-1'>{$itemName}</span>";
        }

        $logHtml .= "</span>";

        // Логируем результат для отладки
        Log::info("formatMobDropReward результат", [
            'html' => $logHtml,
            'glow_class' => $glowClass
        ]);

        return $logHtml;
    }
}